package com.smart.adp.infrastructure.gateway.base;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.model.base.bo.SacSystemConfigBO;
import com.smart.adp.domain.model.base.gateway.SacSystemConfigGateway;
import com.smart.adp.domain.model.base.valueObject.SacSystemConfigVO;
import com.smart.adp.infrastructure.repository.base.SacSystemConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.model.base.valueObject.table.SacSystemConfigVOTableDef.SAC_SYSTEM_CONFIG_VO;
import static com.smart.adp.domain.model.base.valueObject.table.SacSystemConfigValueVOTableDef.SAC_SYSTEM_CONFIG_VALUE_VO;

/**
 * @Description: 系统配置gateway实现类
 * @Author: rik.ren
 * @Date: 2025/5/22 11:11
 **/
@Service
public class SacSystemConfigGatewayImpl implements SacSystemConfigGateway {

    @Autowired
    private SacSystemConfigMapper sacSystemConfigMapper;

    @Override
    public List<SacSystemConfigBO> queryConfigInfoByCondition(SacSystemConfigBO param, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_SYSTEM_CONFIG_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(SAC_SYSTEM_CONFIG_VO.as("config"))
                .innerJoin(SAC_SYSTEM_CONFIG_VALUE_VO.as("value")).on(SAC_SYSTEM_CONFIG_VO.CONFIG_ID.eq(SAC_SYSTEM_CONFIG_VALUE_VO.CONFIG_ID));
        param.buildConditions(wrapper);
        return sacSystemConfigMapper.selectListByQuery(wrapper);
    }
}
