package com.smart.adp.infrastructure.feign.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 线索用户旅程返回VO
 * @Author: rik.ren
 * @Date: 2025/3/15 18:11
 **/
@ToString
@EqualsAndHashCode
@Schema(description = "线索用户旅程返回VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClueEventFlowRsp implements Serializable {
    /**
     * 触发时间
     */
    private LocalDateTime triggerTime;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * "事件类型，1了解，2到店，3试驾，4下定，5交车，6战败
     */
    private Integer eventType;

    /**
     * 线索id
     */
    private String custId;
}
