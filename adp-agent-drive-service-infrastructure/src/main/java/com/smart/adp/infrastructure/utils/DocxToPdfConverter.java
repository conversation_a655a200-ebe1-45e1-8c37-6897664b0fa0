package com.smart.adp.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;
import org.docx4j.convert.out.pdf.PdfConversion;
import org.docx4j.convert.out.pdf.viaXSLFO.Conversion;
import org.docx4j.convert.out.pdf.viaXSLFO.PdfSettings;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * @Description: word转PDF
 * @Author: rik.ren
 * @Date: 2025/06/25 18:32
 **/
@Slf4j
public class DocxToPdfConverter {
    public static File convertDocxToPdf(File docxFile) throws Exception {
        // 加载 .docx 文件
        WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(new FileInputStream(docxFile));
        // 创建临时 PDF 文件
        File tempPdfFile = File.createTempFile("converted_", ".pdf");

        try (FileOutputStream os = new FileOutputStream(tempPdfFile)) {
            // 初始化转换器和设置
            PdfConversion pdfConversion = new Conversion(wordMLPackage);
            PdfSettings pdfSettings = new PdfSettings();

            System.setProperty("docx4j.Convert.Out.FO.OutputMethod", "pdf");
            System.setProperty("docx4j.Convert.Out.FO.OutputSettings",
                    "com.sun.org.apache.xalan.internal.xsltc.trax.TransformerFactoryImpl");
            // 执行转换
            pdfConversion.output(os, pdfSettings);
        } catch (IOException e) {
            throw new IOException("PDF 转换过程中发生异常", e);
        }

        return tempPdfFile;
    }
}