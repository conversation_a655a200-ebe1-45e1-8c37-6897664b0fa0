package com.smart.adp.infrastructure.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description: TDA接口
 * @Author: rik.ren
 * @Date: 2025/6/3 16:53
 **/
@FeignClient(name = "${feign.client.config.tda.url}", url = "${feign.client.config.tda.url}")
public interface TDAFeign {

    /**
     * 结束试驾发送TDA
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/sca/saletool/smart/drive", consumes = "application/json")
    String stopTestDriveSendTda(@RequestBody Object param);
}
