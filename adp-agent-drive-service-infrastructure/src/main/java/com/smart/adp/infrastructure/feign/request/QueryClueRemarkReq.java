package com.smart.adp.infrastructure.feign.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 查询线索用户旅程
 * @Author: rik.ren
 * @Date: 2025/3/13 13:26
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "查询线索用户旅程")
public class QueryClueRemarkReq {

    /**
     * 线索 ID
     */
    @Schema(description = "客户ID", required = true)
    private String custId;

    /**
     * 客户ID集合
     */
    @Schema(description = "客户ID集合", required = true)
    private List<String> listCustId;

    /**
     * 线索状态
     */
    @Schema(description = "线索状态", required = true)
    private String statusCode;

    /**
     * 是否需要线索扩展信息
     */
    @Schema(description = "是否需要线索扩展信息", required = false)
    private Boolean needClueRemark = Boolean.FALSE;

}
