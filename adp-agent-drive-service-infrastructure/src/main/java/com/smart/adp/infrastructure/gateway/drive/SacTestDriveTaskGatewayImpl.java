package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import com.smart.adp.domain.model.drive.entity.SacTestDriveTaskEntity;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveTaskGateway;
import com.smart.adp.domain.qry.DriveTaskQry;
import com.smart.adp.domain.vo.DriveTaskVO;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveTaskEntityMapper;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveTaskMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveTaskEntityTableDef.SAC_TEST_DRIVE_TASK_ENTITY;

/**
 * @Description: 试乘试驾任务gateway实现
 * @Author: rik.ren
 * @Date: 2025/6/30 13:58
 **/
@Slf4j
@Service
public class SacTestDriveTaskGatewayImpl implements SacTestDriveTaskGateway {
    @Autowired
    private SacTestDriveTaskEntityMapper testDriveTaskEntityMapper;
    @Autowired
    private SacTestDriveTaskMapper testDriveTaskBOMapper;

    @Override
    public Boolean createTestDriveTask(SacTestDriveTaskBO param) {
        return testDriveTaskEntityMapper.insert(param) > 0;
    }

    @Override
    public Boolean modifyTestDriveTask(SacTestDriveTaskBO param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("ID"), SqlConsts.EQUALS,
                param.getId()).and(QueryCondition.create(new QueryColumn("UPDATE_CONTROL_ID"), SqlConsts.EQUALS,
                param.getOldUpdateControlId()));
        return testDriveTaskEntityMapper.updateByCondition(param, condition) > 0;
    }

    @Override
    public SacTestDriveTaskBO getTestDriveTask(SacTestDriveTaskBO boParam, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_TASK_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_TASK_ENTITY.as("task"));
        boParam.buildTestDriveTaskCondition(wrapper);
        QueryOrderBy orderBy = new QueryOrderBy(SAC_TEST_DRIVE_TASK_ENTITY.CREATED_DATE, SqlConsts.DESC);
        wrapper.orderBy(orderBy);
        wrapper.limit(1);
        SacTestDriveTaskEntity sacTestDriveTaskResult = testDriveTaskEntityMapper.selectOneByQuery(wrapper);
        return BeanUtil.copyProperties(sacTestDriveTaskResult, SacTestDriveTaskBO.class);
    }

    @Override
    public List<SacTestDriveTaskBO> queryTestDriveTaskList(SacTestDriveTaskBO boParam, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_TASK_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_TASK_ENTITY.as("task"));
        boParam.buildTestDriveTaskCondition(wrapper);
        QueryOrderBy orderBy = new QueryOrderBy(SAC_TEST_DRIVE_TASK_ENTITY.CREATED_DATE, SqlConsts.DESC);
        wrapper.orderBy(orderBy);
        List<SacTestDriveTaskBO> result = testDriveTaskBOMapper.selectListByQuery(wrapper);
//        List<SacTestDriveTaskBO> result = BeanUtil.copyToList(sacTestDriveTaskResult, SacTestDriveTaskBO.class);
        return result;
    }

    /**
     * 查询试驾单
     *
     * @param param
     * @param orderBy
     * @param columns 尽量指定字段查询
     * @return
     */
    @Override
    public DomainPage<SacTestDriveTaskBO> queryTestDriveTaskPage(SacTestDriveTaskBO param, QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            throw new IllegalArgumentException("请指定查询的字段");
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_TASK_ENTITY.as("task"));
        param.buildTestDriveTaskCondition(wrapper);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_TEST_DRIVE_TASK_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacTestDriveTaskBO> paginate = testDriveTaskBOMapper.paginate(param.getPageNumber(), param.getPageSize(), wrapper);
        DomainPage<SacTestDriveTaskBO> domainPage = PageConverterFactory.toDomainPage(paginate);
        return domainPage;
    }

    /**
     * 查询满足条件的试驾任务个数
     *
     * @param param
     * @return
     */
    @Override
    public Long queryTestDriveTaskCount(SacTestDriveTaskBO param) {
        QueryWrapper wrapper = QueryWrapper.create().select().from(SAC_TEST_DRIVE_TASK_ENTITY.as("task"));
        param.buildTestDriveTaskCondition(wrapper);
        Long result = testDriveTaskBOMapper.selectCountByQuery(wrapper);
        return result;
    }

    @Override
    public Page<DriveTaskVO> page(DriveTaskQry qry) {
        Page<DriveTaskVO> page = new Page<>(qry.getPageIndex(), qry.getPageSize());
        QueryWrapper wrapper = qry.conditionWrapper()
                                  .orderBy(SAC_TEST_DRIVE_TASK_ENTITY.CREATED_DATE, false);
        return testDriveTaskEntityMapper.paginateAs(page, wrapper, DriveTaskVO.class);
    }

    @Override
    public List<DriveTaskVO> list(DriveTaskQry qry) {
        return testDriveTaskEntityMapper.selectListByQueryAs(qry.conditionWrapper(), DriveTaskVO.class);
    }

    @Override
    public boolean existByQry(DriveTaskQry qry) {
        QueryWrapper existWrapper = qry.conditionWrapper()
                                       .select(SAC_TEST_DRIVE_TASK_ENTITY.ID)
                                       .limit(1);
        SacTestDriveTaskEntity driveTask = testDriveTaskEntityMapper.selectOneByQuery(existWrapper);
        return Objects.nonNull(driveTask);
    }
}
