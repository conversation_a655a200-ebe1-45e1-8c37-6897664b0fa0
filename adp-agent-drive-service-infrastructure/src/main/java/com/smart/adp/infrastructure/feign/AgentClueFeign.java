package com.smart.adp.infrastructure.feign;

import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.infrastructure.feign.request.ClueListReq;
import com.smart.adp.infrastructure.feign.response.ClueDlrRsp;
import com.smart.adp.infrastructure.feign.response.ClueDlrSaveRsp;
import com.smart.adp.infrastructure.feign.response.ClueEventFlowRsp;
import com.smart.adp.infrastructure.feign.response.SacOneCustRemarkRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * @Description: 调用dws服务的feign
 * @Author: rik.ren
 * @Date: 2025/3/11 20:08
 **/
@FeignClient(name = "${feign.client.config.adp-agent-clue.url}", url = "${feign.client.config.adp-agent-clue.url}")
public interface AgentClueFeign {
    /**
     * 查询用户旅程
     *
     * @param param
     * @return
     */
    @PostMapping("/api/rpc/agent/clue/queryListUserEventFlow")
    RespBody<List<ClueEventFlowRsp>> queryUserEventFlow(@RequestBody Object param);

    /**
     * 查询用户旅程
     *
     * @param param
     * @return
     */
    @PostMapping("/api/rpc/agent/clue/queryClueRemark")
    RespBody<List<SacOneCustRemarkRsp>> queryClueRemark(@RequestBody Object param);

    /**
     * 根据手机号码精确查询一条线索
     *
     * @param param
     * @return
     */
    @PostMapping("/api/rpc/agent/clue/queryClueInfoDlr")
    RespBody<ClueDlrRsp> queryClueInfoDlr(@RequestBody Object param);

    /**
     * 经销商线索保存
     *
     * @param param
     * @return
     */
    @PostMapping("/api/agent/clue/agentDlrClueSave")
    RespBody<ClueDlrSaveRsp> saveAgentDlrClue(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody Object param);

    /**
     * 更新意向车信息
     *
     * @param param
     * @return 会返回最新的意向车型
     */
    @PostMapping("/api/rpc/agent/clue/modifyIntentionCar")
    RespBody<String> modifyIntentionCar(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication, @RequestBody Object param);

    /**
     * 批量查询线索
     *
     * @param req 查询请求
     * @return 线索信息
     */
    @PostMapping("/api/rpc/agent/clue/clueList")
    RespBody<List<ClueDlrRsp>> clueList(@RequestBody ClueListReq req);
}
