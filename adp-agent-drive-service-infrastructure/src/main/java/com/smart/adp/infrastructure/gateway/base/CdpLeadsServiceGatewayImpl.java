package com.smart.adp.infrastructure.gateway.base;

import com.smart.adp.domain.model.base.gateway.CdpLeadsServiceGateway;
import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsEventVO;
import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsVO;
import com.smart.adp.infrastructure.repository.base.IfsBaseCdpLeadsEventMapper;
import com.smart.adp.infrastructure.repository.base.IfsBaseCdpLeadsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: adp推送数据给cdp的gateway实现
 * @Author: rik.ren
 * @Date: 2025/5/29 17:59
 **/
@Slf4j
@Service
public class CdpLeadsServiceGatewayImpl implements CdpLeadsServiceGateway {
    @Autowired
    private IfsBaseCdpLeadsMapper cdpLeadsMapper;

    @Autowired
    private IfsBaseCdpLeadsEventMapper cdpLeadsEventMapper;

    /**
     * 保存数据
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveData(IfsBaseCdpLeadsVO entity) {
        return cdpLeadsMapper.insert(entity) > 0;
    }

    /**
     * 保存数据
     *
     * @param entity
     * @return
     */
    @Override
    public Boolean saveDataEvent(IfsBaseCdpLeadsEventVO entity) {
        return cdpLeadsEventMapper.insert(entity) > 0;
    }
}
