package com.smart.adp.infrastructure.config.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.util.Objects;

/**
 * <p>
 * cache implementation
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/4
 * @see RedisCache
 */
@Slf4j
public class SCache extends RedisCache {

    private static final String CACHE_LOG_KEY = "[s-cache]";

    protected SCache(String name, RedisCacheWriter writer, RedisCacheConfiguration cacheConfiguration) {
        super(name, writer, cacheConfiguration);
    }

    @Override
    protected Object lookup(Object key) {
        log.debug(CACHE_LOG_KEY + "get {}", key);
        try {
            Object value = super.lookup(key);
            log.debug(CACHE_LOG_KEY + "get res {}", value);
            return value;
        } catch (Exception e) {
            log.error(CACHE_LOG_KEY + "get exception {}", e.getMessage());
            return null;
        }
    }

    @Override
    public void put(Object key, @Nullable Object value) {
        log.debug(CACHE_LOG_KEY + "put {} {}", key, value);
        try {
            super.put(key, value);
        } catch (Exception e) {
            log.error(CACHE_LOG_KEY + "put exception {}", e.getMessage());
        }
    }

    public void put(Object key, Object value, Duration duration) {
        put(key, value, duration.toMillis());
    }

    public void put(Object key, Object value, long ttl) {
        log.debug(CACHE_LOG_KEY + "put {} {} {}", key, value, ttl);
        try {
            String name = getName();

            if (Objects.isNull(value)) {
                throw new IllegalArgumentException("value must not be null");
            }

            getNativeCache().put(name, serializeCacheKey(createCacheKey(key)), serializeCacheValue(value), Duration.ofMillis(ttl));
        } catch (Exception e) {
            log.error(CACHE_LOG_KEY + "put exception {}", e.getMessage());
        }
    }

    @Override
    public void evict(Object key) {
        log.debug(CACHE_LOG_KEY + "evict {}", key);
        try {
            super.evict(key);
        } catch (Exception e) {
            log.error(CACHE_LOG_KEY + "evict exception {}", e.getMessage());
        }
    }
}
