package com.smart.adp.infrastructure.repository.drive;

import com.mybatisflex.core.BaseMapper;
import com.smart.adp.domain.model.drive.entity.TestCarPrepareEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 试驾车表 映射层。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Mapper
public interface TestCarPrepareEntityMapper extends BaseMapper<TestCarPrepareEntity> {


    /**
     * 试驾结束时更新整备表
     *
     * @param testCarPrepareEntity
     * @return
     */
    int updatePrepareForStop(@Param("param") TestCarPrepareEntity testCarPrepareEntity);
}
