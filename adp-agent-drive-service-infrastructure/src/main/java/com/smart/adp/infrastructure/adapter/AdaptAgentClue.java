package com.smart.adp.infrastructure.adapter;

import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.enums.ClueLevelEnum;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.infrastructure.feign.request.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 查询agent-clue服务入参适配器的真正实现类
 * @Author: rik.ren
 * @Date: 2025/03/11 20:16
 **/
@Component
public class AdaptAgentClue extends AbstractIAdapterClueParamConvent {
    /**
     * 调用agent-clue服务，针对agent-clue的查询线索事件节点信息接口的入参进行适配
     *
     * @param param
     * @return
     */
    @Override
    public QueryUserEventFlowReq queryClueEventFlowInfo(List<String> param) {
        return QueryUserEventFlowReq.builder().listCustId(param).build();
    }

    /**
     * 从clue查询线索的扩展信息
     *
     * @param param
     * @return
     */
    @Override
    public Object queryClueRemark(List<String> param) {
        return QueryClueRemarkReq.builder().listCustId(param).build();
    }

    /**
     * @param custPhone
     * @return
     */
    @Override
    public Object queryClueInfoDlr(String custPhone) {
        return QueryClueDlrInfoReq.builder().phone(custPhone).build();
    }

    /**
     * 根据手机号查询线索
     *
     * @param listCustPhone
     * @return
     */
    @Override
    public Object queryClueInfoDlr(List<String> listCustPhone, Integer defeatFlag) {
        return QueryClueDlrInfoReq.builder().listPhone(listCustPhone).defeatFlag(defeatFlag).build();
    }

    /**
     * 更新意向车型
     *
     * @param custId
     * @param newIntentionCar
     * @return
     */
    @Override
    public Object modifyIntentionCar(String custId, String newIntentionCar, LocalDateTime lastTestdriverTime) {
        return ModifyIntentionCarReq.builder().custId(custId).newIntentionCar(newIntentionCar).lastTestdriverTime(lastTestdriverTime).build();
    }

    /**
     * @param sacTestDriveSheetBO
     * @return
     */
    @Override
    public Object CreateAgentClue(SacTestDriveSheetBO sacTestDriveSheetBO, UserBusiEntity userBusiEntity) {
        CreateAgentClueReq createAgentClueReq = CreateAgentClueReq.builder()
                .custName(sacTestDriveSheetBO.getCustomerName())
                .clueLevel(ClueLevelEnum.H.getCode())
                .location(null)
                .agentResource(null)
                .competitorModels(null)
                .phone(sacTestDriveSheetBO.getCustomerPhone())
                .genderCode(sacTestDriveSheetBO.getSex())
                .genderName(sacTestDriveSheetBO.getCustomerSexName())
                .infoChanMCode("agent_nature")
                .infoChanMName("门店自然客流")
                .infoChanDCode("agent_nature")
                .infoChanDName("门店自然客流")
                .channelCode("agent_nature")
                .channelName("门店自然客流")
                .intenCarTypeCode(sacTestDriveSheetBO.getSmallCarTypeCode())
                .intenCarTypeName(sacTestDriveSheetBO.getSmallCarTypeName())
                .intenLevelCode(sacTestDriveSheetBO.getIntenLevelCode())
                .intenLevelName(sacTestDriveSheetBO.getIntenLevelName())
                .outColorCode(null)
                .outColorName(null)
                .innerColorCode(null)
                .innerColorName(null)
                .planBuyDate("1")
                .planBuyDateName("2周以内")
                .businessHeatName(sacTestDriveSheetBO.getBusinessHeatName())
                .businessHeatCode(sacTestDriveSheetBO.getBusinessHeatCode())
                .reviewPersonId(userBusiEntity.getUserID())
                .reviewPersonName(userBusiEntity.getEmpName())
                .systemSource("ADP-DRIVE")
                .smartId(null)
                .planReviewTime(LocalDateTime.now().plusDays(+1).format(TimeConstant.DEFAULT_FORMATTER))
                .clueType("dlrClue")
                .dlrCode(sacTestDriveSheetBO.getDlrCode())
                .dlrShortName(sacTestDriveSheetBO.getDlrName())
                .build();
        return createAgentClueReq;

    }
}
