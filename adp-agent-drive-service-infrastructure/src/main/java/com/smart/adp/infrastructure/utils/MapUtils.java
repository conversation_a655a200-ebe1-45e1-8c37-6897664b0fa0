package com.smart.adp.infrastructure.utils;

import com.smart.adp.domain.common.constants.TimeConstant;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: map工具库
 * @Author: rik.ren
 * @Date: 2024/12/5 13:09
 **/
@Slf4j
public class MapUtils {

    // 定义日期时间格式
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    // 定义日期时间格式
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 实体集合转换成List<Map<String, Object>>
     *
     * @param list        实体集合对象
     * @param containNull 是否需要包含值为空的属性
     * @param <T>
     * @return
     */
    public static <T> List<Map<String, Object>> convertToMapList(List<T> list, Boolean containNull) {
        return list.stream()
                .map(entity -> entityToMap(entity, containNull))
                .collect(Collectors.toList());
    }

    public static <T> Map<String, Object> entityToMap(T entity, Boolean containNull) {
        Map<String, Object> map = new HashMap<>();
        Field[] fields = entity.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                // 使用field.get(entity)来获取字段值
                if (!containNull && Objects.isNull(field.get(entity))) {
                    continue;
                }
                map.put(field.getName(), field.get(entity));
            } catch (IllegalAccessException e) {
                log.error("com.smart.adp.infrastructure.utils.MapUtils.entityToMap.exception", e);
            }
        }
        return map;
    }

    /**
     * 将List<Map<String, Object>>中的Timestamp类型值转换为String类型
     *
     * @param list 包含Map的List
     */
    public static void convertTimestampToString(List<Map<String, Object>> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        list.forEach(map -> {
            if (map != null) {
                map.replaceAll((key, value) -> convertTimestamp(value));
            }
        });
    }

    /**
     * 将Timestamp或Date转换为字符串
     *
     * @param value 时间对象（Timestamp、Date或其他类型）
     * @return 转换后的字符串，如果无法转换则返回原值
     */
    private static Object convertTimestamp(Object value) {
        if (value instanceof Timestamp) {
            return SDF.format((Timestamp) value);
        } else if (value instanceof Date) {
            return SDF.format((Date) value);
        } else if (value instanceof java.time.LocalDateTime) {
            return ((java.time.LocalDateTime) value).format(TimeConstant.DEFAULT_FORMATTER);
        } else if (value instanceof java.time.Instant) {
            return SDF.format(Date.from((java.time.Instant) value));
        }
        // 如果不是时间类型，则返回原值
        return value;
    }
}
