package com.smart.adp.infrastructure.publisher;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.common.constants.MQConstants.START_DRIVING_MODIFY_CLUE_EXCHANGE;

/**
 * <AUTHOR>
 * date 2025/5/29 17:04
 * @description 发消息给CDP服务
 **/
@Slf4j
@Service
public class CDPPublisher {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 开始试驾时，修改线索的意向车型等信息
     *
     * @param msg
     */
    public void sendCDPMessageForStartDriving(String msg) {
        log.info("开始试驾时推送线索服务修改线索的意向车型等信息 msg={}", msg);
        String uuid = rabbitTemplate.getUUID();
        Message message = MessageBuilder.withBody(msg.getBytes()).setMessageId(uuid).setContentType("application/json").build();
        CorrelationData correlationData = new CorrelationData(uuid);
        rabbitTemplate.convertAndSend(START_DRIVING_MODIFY_CLUE_EXCHANGE, "", message, correlationData);
    }
}
