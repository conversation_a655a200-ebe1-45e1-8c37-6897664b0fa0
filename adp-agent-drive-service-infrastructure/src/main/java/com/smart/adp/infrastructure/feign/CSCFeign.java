package com.smart.adp.infrastructure.feign;

import com.smart.adp.domain.common.resp.EntityResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * @Description: 调用csc服务的feign
 * @Author: rik.ren
 * @Date: 2025/6/20 16:30
 **/
@FeignClient(name = "${feign.client.config.csc-service.url}", url = "${feign.client.config.csc-service.url}")
public interface CSCFeign {
    /**
     * 客户履历保存
     *
     * @param param
     * @return
     */
    @PostMapping("/ly/adp/csc/onecustinfo/resumesave.do")
    EntityResult<Object> resumeSave(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                    @RequestBody Map<String, Object> param);
}
