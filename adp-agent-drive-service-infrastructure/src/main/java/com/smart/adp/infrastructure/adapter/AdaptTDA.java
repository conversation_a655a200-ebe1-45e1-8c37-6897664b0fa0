package com.smart.adp.infrastructure.adapter;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

/**
 * @Description: 查询agent-clue服务入参适配器的真正实现类
 * @Author: rik.ren
 * @Date: 2025/03/11 20:16
 **/
@Component
public class AdaptTDA extends AbstractIAdapterTDAParamConvent {

    /**
     * 结束试驾推送信息给TDA
     *
     * @param boParam
     * @return
     */
    @Override
    public HttpEntity<Object> stopTestDriveSendTda(SacTestDriveSheetBO boParam, UscMdmOrgEmployeeBO empBoParam) {
        HashMap<String, Object> driveMap = Maps.newHashMap();
        driveMap.put("drive_id", boParam.getTestDriveOrderNo());
        driveMap.put("user_id", empBoParam.getEmpCode());//来自t_usc_mdm_org_employee表的empCode
        driveMap.put("reception_ed", LocalDateTime.now().plusMinutes(30).toInstant(ZoneOffset.of("+8")).toEpochMilli());

        Object receiverTime = boParam.getReceiverTime();
        if (ObjectUtil.isNotEmpty(receiverTime)) {
            LocalDateTime parse =
                    LocalDateTime.parse(receiverTime.toString(), DateTimeFormatter.ISO_LOCAL_DATE_TIME).minusMinutes(30);
//            LocalDateTime parse = LocalDateTime.parse(receiverTime.toString(), TimeConstant.DEFAULT_FORMATTER).minusMinutes(30);
            long receptionBg = parse.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            driveMap.put("reception_bg", receptionBg);
        }

        HashMap<String, Object> driver = Maps.newHashMap();
        driver.put("drive_ed", LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        driver.put("drive_bg",
                LocalDateTime.parse(boParam.getStartTime(), TimeConstant.DEFAULT_FORMATTER).toInstant(ZoneOffset.of("+8")).toEpochMilli());
        driver.put("drive_car", boParam.getSmallCarTypeCode());
        driver.put("drive_route", Lists.newArrayList());
        driveMap.put("drive_info", driver);

        HashMap<String, Object> clientInfo = Maps.newHashMap();
        clientInfo.put("client_name", boParam.getCustomerName());
        clientInfo.put("client_phone", boParam.getCustomerPhone());
        clientInfo.put("client_id", boParam.getCustomerId());
        driveMap.put("client_info", clientInfo);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json; charset=UTF-8");
        HttpEntity<Object> entity = new HttpEntity<>(JSONObject.toJSONString(driveMap), httpHeaders);
        return entity;
    }
}
