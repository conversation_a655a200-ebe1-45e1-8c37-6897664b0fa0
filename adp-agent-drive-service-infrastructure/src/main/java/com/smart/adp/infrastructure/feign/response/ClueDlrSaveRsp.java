package com.smart.adp.infrastructure.feign.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * date 2025/5/11 22:58
 * @description 创建线索返参
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "创建线索 VO")
public class ClueDlrSaveRsp {

    @Schema(description = "线索 ID")
    private String id;

    @Schema(description = "线索 单号")
    private String serverOrder;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String custId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String custName;

    /**
     * 回访记录ID
     */
    @Schema(description = "回访记录ID")
    private String reviewId;

    /**
     * 线索手机号
     */
    @Schema(description = "线索手机号")
    private String phone;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    private String dlrShortName;

    /**
     * 意向级别编码
     */
    @Schema(description = "意向级别编码")
    private String intenLevelCode;
}
