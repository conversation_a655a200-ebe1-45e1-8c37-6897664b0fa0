package com.smart.adp.infrastructure.gateway.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.resp.OptResult;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.base.entity.UscMdmOrgEmployeeEntity;
import com.smart.adp.domain.model.base.gateway.BaseDataServiceGateway;
import com.smart.adp.infrastructure.feign.BaseFeign;
import com.smart.adp.infrastructure.repository.base.UscMdmOrgEmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.model.base.entity.table.UscMdmOrgEmployeeEntityTableDef.USC_MDM_ORG_EMPLOYEE_ENTITY;

/**
 * @Description: baseDataGateway实现
 * @Author: rik.ren
 * @Date: 2025/5/24 14:09
 **/
@Service
@Slf4j
public class BaseDataGatewayImpl implements BaseDataServiceGateway {
    @Autowired
    private BaseFeign baseFeign;

    @Autowired
    private UscMdmOrgEmployeeMapper ucMdmOrgEmployeeMapper;

    /**
     * 生成单号
     *
     * @param dlrId
     * @param billTypeId
     * @param token
     * @return
     */
    @Override
    public String generateOrderCode(String dlrId, String billTypeId, String token) throws Exception {
        try {
            // 试乘试驾预约单单号生成
            log.info("试乘试驾预约单单号生成参数 {} -{} -{}", billTypeId, dlrId, token);
            OptResult optResult = baseFeign.generateOrderCode(token, dlrId, billTypeId);
            // 调用服务成功则直接获取单号，不成功则调用自定义单号生成方法
            if ("1".equals(optResult.getResult())) {
                return optResult.getMsg();
            } else {
                log.info("试乘试驾预约单单号生成异常 {}", JSONObject.toJSONString(optResult));
                throw BusicenException.create("调用生成【试驾预约单单号】出错！[result=" + optResult.getResult() + ", msg="
                        + optResult.getMsg() + "]");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 根据条件查询员工信息
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<UscMdmOrgEmployeeBO> queryUscMdmOrgEmp(UscMdmOrgEmployeeBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{USC_MDM_ORG_EMPLOYEE_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(needColumn).from(USC_MDM_ORG_EMPLOYEE_ENTITY.as("emp"));
        param.buildCondition(wrapper);
        List<UscMdmOrgEmployeeEntity> listUscMdmOrgEmployeeEntity = ucMdmOrgEmployeeMapper.selectListByQuery(wrapper);
        if (CollectionUtil.isEmpty(listUscMdmOrgEmployeeEntity)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(listUscMdmOrgEmployeeEntity, UscMdmOrgEmployeeBO.class);
    }
}
