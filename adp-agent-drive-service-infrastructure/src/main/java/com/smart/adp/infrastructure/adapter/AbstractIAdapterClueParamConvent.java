package com.smart.adp.infrastructure.adapter;

import com.smart.adp.domain.adapter.IAdapterClueRequestEntity;
import com.smart.adp.infrastructure.feign.request.QueryUserEventFlowReq;

import java.util.List;

/**
 * @Description: 适配器-线索的抽象实现类，目的后续的转换类不用实现全部的接口
 * @Author: rik.ren
 * @Date: 2025/03/11 18:10
 **/
public abstract class AbstractIAdapterClueParamConvent implements IAdapterClueRequestEntity {

    @Override
    public QueryUserEventFlowReq queryClueEventFlowInfo(List<String> param) {
        return null;
    }

}
