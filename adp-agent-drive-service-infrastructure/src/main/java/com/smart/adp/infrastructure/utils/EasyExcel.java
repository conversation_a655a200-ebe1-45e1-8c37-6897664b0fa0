package com.smart.adp.infrastructure.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName easyExcel
 * @Description 读取Excel
 * <AUTHOR>
 * @Date 2023/3/2 2:32
 * @Version 1.0
 */
@Slf4j
public class EasyExcel<T> {
    private Class<T> tClass;

    public EasyExcel(Class<T> tClass) {
        this.tClass = tClass;
    }

    private Logger logger = LoggerFactory.getLogger(EasyExcel.class);

    /**
     * 读取文件
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public List<T> readExcel(InputStream inputStream) throws IOException {
        List<T> result = com.alibaba.excel.EasyExcel.read(inputStream, tClass, new AnalysisEventListener<T>() {
            List<T> list = new ArrayList<>();

            @Override
            public void invoke(T item, AnalysisContext context) {
                context.readWorkbookHolder().setIgnoreEmptyRow(false);
                list.add(item);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                logger.info("所有数据解析完成！");
            }

            @Override
            public boolean hasNext(AnalysisContext context) {
                ReadRowHolder readRowHolder = context.readRowHolder();
                if (ObjectUtils.checkObjAllFieldsIsNull(readRowHolder.getCurrentRowAnalysisResult())) {
                    doAfterAllAnalysed(context);
                    return false;
                }
                return super.hasNext(context);
            }
        }).headRowNumber(1).ignoreEmptyRow(true).doReadAllSync();
        return result;
    }

    /**
     * 写入Excel
     *
     * @param fileName
     * @param source
     * @throws IOException
     */
    public void export(String fileName, List<T> source) throws IOException {
        HttpServletResponse responses = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        responses.setContentType("application/vnd.ms-excel");
        responses.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        responses.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        com.alibaba.excel.EasyExcel.write(responses.getOutputStream(), tClass)
                .sheet("明细")
                .doWrite(source);
    }
}
