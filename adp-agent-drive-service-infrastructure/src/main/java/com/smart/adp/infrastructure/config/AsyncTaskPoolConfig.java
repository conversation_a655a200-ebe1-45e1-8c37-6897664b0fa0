package com.smart.adp.infrastructure.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * date 2025/3/10 15:07
 * @description 异步事件线程池配置
 **/
@Configuration
@EnableAsync
public class AsyncTaskPoolConfig {

    /**
     * 服务器的cpu个数
     */
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    /**
     * 核心线程数
     */
    private static final int COUR_SIZE = CPU_COUNT * 2;

    /**
     * 最大线程数
     */
    private static final int MAX_COUR_SIZE = CPU_COUNT * 4;

    /**
     * 线程池队列容量 目前评估15000
     */
    private static final int QUEUE_CAPACITY = 5000 * 2;

    @Bean
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(CPU_COUNT * 2);
        executor.setMaxPoolSize(CPU_COUNT * 2);
        executor.setQueueCapacity(1000);

        executor.setThreadNamePrefix("task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean("driveExecutor")
    public Executor threadPoolTaskExecutor() {

        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        // 设置核心线程数，如果线程数小于核心线程数，则优先创建核心线程
        taskExecutor.setCorePoolSize(COUR_SIZE);
        // 设置最大线程数，如果线程数大于核心线程数，并且队列未满，则优先创建最大线程
        taskExecutor.setMaxPoolSize(MAX_COUR_SIZE);
        // 设置队列容量，如果队列已满，则创建新线程
        taskExecutor.setQueueCapacity(QUEUE_CAPACITY);
        // 设置线程空闲时间，如果线程空闲时间超过该值，则回收线程
        taskExecutor.setKeepAliveSeconds(30);
        // 设置线程前缀名称
        taskExecutor.setThreadNamePrefix("driveExecutor-");
        // 设置拒绝策略，如果队列已满并且线程数大于最大线程数，则拒绝请求
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(30);

        taskExecutor.initialize();
        return TtlExecutors.getTtlExecutor(taskExecutor);
    }
}
