package com.smart.adp.infrastructure.adapter;

import com.smart.adp.infrastructure.feign.request.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;

/**
 * @Description: 查询中台服务入参适配器的真正实现类
 * @Author: rik.ren
 * @Date: 2025/03/11 20:16
 **/
@Component
public class AdaptMid extends AbstractIAdapterMidParamConvent {
    /**
     * 中台ocr接口适配器
     *
     * @param imageUrl
     * @return
     */
    @Override
    public Object midOCR(String imageUrl, MultipartFile imageBody) {
        MidOCRReq midOCRReq = MidOCRReq.builder().build();
        MidOCRReq.Recognizes recognizes = midOCRReq.new Recognizes();
        recognizes.setUrl(imageUrl);
        // 新增文件流处理逻辑
        if (imageBody != null && !imageBody.isEmpty()) {
            try {
                byte[] fileBytes = imageBody.getBytes();
                String base64Body = Base64.getEncoder().encodeToString(fileBytes);
                recognizes.setBody(base64Body);
            } catch (IOException e) {
                throw new RuntimeException("文件读取失败", e);
            }
        }
        midOCRReq.setRecognizes(Arrays.asList(recognizes));
        return midOCRReq;
    }

    /**
     * 中台身份证信息接口
     *
     * @param name
     * @param idCardNO
     * @return
     */
    @Override
    public Object midIdCardInfo(String name, String idCardNO) {
        return IdentityIdCardInfoReq.builder().idCardName(name).idCardNo(idCardNO).build();
    }
}
