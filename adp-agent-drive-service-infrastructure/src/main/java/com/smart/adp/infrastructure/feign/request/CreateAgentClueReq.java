package com.smart.adp.infrastructure.feign.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/5/13 18:54
 * @description 店端线索DTO
 **/
@Builder
@Data
@Schema(description = "线索创建入参")
public class CreateAgentClueReq implements Serializable {

    /**
     * 客户名称
     */
    @Schema(description = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    private String custName;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String custId;

    /**
     * 线索等级
     */
    @Schema(description = "线索等级(H/A/B/C/D/E),代理商项目新增")
    @NotBlank(message = "线索等级不能为空")
    private String clueLevel;

    /**
     * 市或区编码
     */
    @Schema(description = "客户所在地(市或区编码)，代理商项目新增")
    private String location;

    /**
     * 代理商来源
     */
    @Schema(description = "代理商来源，代理商项目新增")
    private String agentResource;

    /**
     * 竞品车型
     */
    @Schema(description = "竞品车型,代理商项目新增")
    private String competitorModels;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    /**
     * 性别编码
     */
    @Schema(description = "性别编码")
    private String genderCode;

    /**
     * 性别
     */
    @Schema(description = "性别")
    @NotBlank(message = "性别不能为空")
    private String genderName;

    /**
     * 一级信息来源编码
     */
    @Schema(description = "一级信息来源编码")
    private String infoChanMCode;

    /**
     * 一级信息来源名称
     */
    @Schema(description = "一级信息来源名称")
    private String infoChanMName;

    /**
     * 二级信息来源编码
     */
    @Schema(description = "二级信息来源编码")
    private String infoChanDCode;

    /**
     * 二级信息来源名称
     */
    @Schema(description = "二级信息来源名称")
    private String infoChanDName;

    /**
     * 最低一级的信息来源编码
     */
    @Schema(description = "最低一级的信息来源编码")
    private String channelCode;

    /**
     * 最低一级的信息来源名称
     */
    @Schema(description = "最低一级的信息来源名称")
    private String channelName;

    /**
     * 意向车型编码
     */
    @Schema(description = "意向车型编码")
    @NotBlank(message = "意向车型不能为空")
    private String intenCarTypeCode;


    /**
     * 意向车型名称
     */
    @Schema(description = "意向车型名称")
    private String intenCarTypeName;

    /**
     * 意向级别编码
     */
    @Schema(description = "意向级别编码")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @Schema(description = "意向级别名称")
    private String intenLevelName;

    /**
     * 外观色编码
     */
    @Schema(description = "外观色编码")
    private String outColorCode;

    /**
     * 外观色名称
     */
    @Schema(description = "外观色名称")
    private String outColorName;

    /**
     * 内饰色编码
     */
    @Schema(description = "内饰色编码")
    private String innerColorCode;

    /**
     * 内饰色编码
     */
    @Schema(description = "内饰色编码")
    private String innerColorName;

    /**
     * planBuyDate
     * 如："column2": "1"
     */
    @Schema(description = "planBuyDate")
    private String planBuyDate;

    /**
     * 如："column1": "2周以内"
     */
    @Schema(description = "planBuyDateName")
    private String planBuyDateName;


    /**
     * 扩展字段5
     * 如："column5": "Hot"
     * 业务含义字段 businessHeatName
     */
    @Schema(description = "businessHeatName")
    private String businessHeatName;

    /**
     * 扩展字段6
     * 如："column6": "Hot"
     * 业务含义字段 businessHeatCode
     */
    @Schema(description = "businessHeatCode")
    @NotBlank(message = "热度不能为空")
    private String businessHeatCode;

    /**
     * 回访人员用户ID
     */
    @Schema(description = "回访人员用户ID")
    private String reviewPersonId;

    /**
     * 回访人员名称
     */
    @Schema(description = "回访人员名称")
    private String reviewPersonName;

    /**
     * 系统来源
     */
    @Schema(description = "一级信息来源编码")
    private String systemSource = "ADP-DLR";

    /**
     * smartId
     */
    @Schema(description = "smartId")
    private String smartId;

    /**
     * 下次回访时间
     */
    @Schema(description = "下次回访时间")
    private String planReviewTime;

    /**
     * clueType 总部线索塞值处理orgCode
     */
    @Schema(description = "clueType")
    private String clueType = "dlrClue";

    /**
     * dlrCode
     */
    @Schema(description = "dlrCode")
    private String dlrCode;

    /**
     * dlrShortName
     */
    @Schema(description = "dlrShortName")
    private String dlrShortName;
}
