package com.smart.adp.infrastructure.advice;

import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.infrastructure.config.cache.SCache;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class SmartADPCacheAspect {

    private final SpelExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Autowired
    private CacheManager cacheManager;

    @Around("@annotation(smartADPCacheObj)")
    public Object around(ProceedingJoinPoint joinPoint, SmartADPCache smartADPCacheObj) throws Throwable {
        // 1. 获取key
        String cacheName = smartADPCacheObj.value();
        String keySpEL = smartADPCacheObj.key();
        String key = generateKey(joinPoint, keySpEL);

        // 2. 查询缓存
        SCache cache = (SCache) cacheManager.getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper cachedValue = cache.get(key);
            if (cachedValue != null) {
                return cachedValue.get();
            }
        }
        // 3. 未命中缓存
        Object result = joinPoint.proceed();

        // 4. 设置缓存，使用RedisTemplate设置过期时间
        if (cache != null && result != null) {
            long expire = smartADPCacheObj.expire();
            cache.put(key, result, smartADPCacheObj.timeUnit().toMillis(expire));
        }
        return result;
    }

    // 生成缓存键（基于 SpEL）
    private String generateKey(ProceedingJoinPoint joinPoint, String keySpEL) {
        if (keySpEL.isEmpty()) {
            return "default_key";
        }

        // 1. 获取方法参数名和参数值
        Object[] args = joinPoint.getArgs();
        Method method = ((org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature()).getMethod();
        String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);

        // 2. 绑定参数名和参数值到SpEL
        StandardEvaluationContext context = new StandardEvaluationContext();
        if (parameterNames != null) {
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
        }

        // 3. 解析SpEL表达式
        Expression expression = parser.parseExpression(keySpEL);
        return expression.getValue(context, String.class);
    }
}