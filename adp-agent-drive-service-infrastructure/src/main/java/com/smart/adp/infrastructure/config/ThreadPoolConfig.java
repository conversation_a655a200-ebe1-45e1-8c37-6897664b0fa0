package com.smart.adp.infrastructure.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "thread-pool")
public class ThreadPoolConfig {
    /**
     * 核心线程数的倍数
     */
    @Value("${thread-pool.coreSizeMultiple:8}")
    private int coreSizeMultiple;
    /**
     * 最大线程数的倍数
     */
    @Value("${thread-pool.maxSizeMultiple:16}")
    private int maxSizeMultiple;
    /**
     * 队列大小
     */
    @Value("${thread-pool.queueCapacity:10000}")
    private int queueCapacity;

    @Bean(name = "smartExecutor")
    public Executor smartExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * coreSizeMultiple,
                Runtime.getRuntime().availableProcessors() * maxSizeMultiple, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity), new ThreadFactory() {// 内联线程工厂
            private final AtomicInteger counter = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "smart-pool-" + counter.getAndIncrement());
                thread.setDaemon(false);//非守护线程
                thread.setUncaughtExceptionHandler((t, e) -> log.info("Thread [" + t.getName() + "] 错误" + ": " + e));
                return thread;
            }
        }, new ThreadPoolExecutor.CallerRunsPolicy()  // 拒绝策略：由调用线程执行，这可以减缓新任务提交的速度，避免系统过载
        ) {
            // 动态调整核心线程数
            @Override
            public void execute(Runnable command) {
                int activeCount = getActiveCount();
                int queueSize = getQueue().size();
                // 当活跃线程 >= 核心数 且 队列已过半时扩容
                log.info("扩充活跃线程数：{}，核心线程数：{}，队列大小：{}, 比例：{}", activeCount, getCorePoolSize(), queueSize, queueCapacity);
                if (activeCount >= getCorePoolSize() && queueSize > queueCapacity / 2) {
                    int newCore = Math.min(getCorePoolSize() * 16, Runtime.getRuntime().availableProcessors() * 16);
                    setCorePoolSize(newCore);
                    log.info("核心线程数扩充到：{}，{}", newCore, Runtime.getRuntime().availableProcessors());
                }
                super.execute(command);
            }

            // 空闲时自动收缩核心线程数
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                int activeCount = getActiveCount();
                int queueSize = getQueue().size();
                log.info("收缩活跃线程数：{}，核心线程数：{}，队列大小：{}, 比例：{}", activeCount, getCorePoolSize(), queueSize, queueCapacity);
                if (activeCount < getCorePoolSize() && queueSize < queueCapacity / 4) {
                    int newCore = Math.max(activeCount, Runtime.getRuntime().availableProcessors() * 8);
                    setCorePoolSize(newCore);
                    log.info("核心线程数收缩到：{}，{}", newCore, Runtime.getRuntime().availableProcessors());
                }
            }
        };
        // 允许核心线程超时
        executor.allowCoreThreadTimeOut(true);
        return TtlExecutors.getTtlExecutor(executor);
    }

    /**
     * PDF转换专用队列线程池
     */
    @Bean(name = "pdfTaskExecutor")
    public Executor pdfTaskExecutor() {
        return new ThreadPoolExecutor(
                1,  // 固定1个核心线程
                1,  // 最大线程数保持1
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100), // 设置队列容量为100
                r -> {
                    Thread t = new Thread(r);
                    t.setName("pdf-convert-queue");
                    return t;
                },
                new ThreadPoolExecutor.AbortPolicy() // 队列满时拒绝任务
        );
    }
}