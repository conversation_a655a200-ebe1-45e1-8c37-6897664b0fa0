package com.smart.adp.infrastructure.advice;

import java.util.List;

/**
 * @Description: forkJoin，需要执行的业务逻辑函数式接口
 * @Author: rik.ren
 * @Date: 2025/03/28 13:54
 **/
@FunctionalInterface
public interface SelfLogicForkJoinFunction<T1, T2> {
    /**
     * 需要读取excel后，执行的业务逻辑代码
     *
     * @param listParam 待执行的数据集
     * @param param     参数集
     */
    List<T2> selfLogicForkJoinFunction(List<T1> listParam, Object... param);
}
