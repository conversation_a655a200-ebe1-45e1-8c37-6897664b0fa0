package com.smart.adp.infrastructure.feign;

import com.smart.adp.domain.common.resp.OptResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 调用base服务的feign
 * @Author: rik.ren
 * @Date: 2025/5/11 20:08
 **/
@FeignClient(name = "${feign.client.config.adp-base-service.url}", url = "${feign.client.config.adp-base-service.url}")
public interface BaseFeign {
    /**
     * 调用单号生成器
     *
     * @param authentication
     * @param dlrId
     * @param billTypeId
     * @return
     * @throws Exception
     */
    @PostMapping("/ordercoderule/generateOrderCode.do")
    OptResult generateOrderCode(@RequestHeader(HttpHeaders.AUTHORIZATION) String authentication,
                                @RequestParam("dlrId") String dlrId, @RequestParam("billTypeId") String billTypeId) throws Exception;

}
