package com.smart.adp.infrastructure.feign.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 驾驶证OCR识别结果对象
 * @Author: rik.ren
 * @Date: 2025/6/19 18:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DriverLicenseOCRRsp {
    /**
     * 姓名（背面识别）
     */
    @Schema(description = "驾驶证背面识别姓名")
    private String nameBack;

    /**
     * 驾驶证号码
     */
    @Schema(description = "驾驶证号")
    private String licenseNumber;

    /**
     * 姓名（正面识别）
     */
    @Schema(description = "驾驶证正面识别姓名")
    private String name;

    /**
     * 档案编号
     */
    @Schema(description = "驾驶证档案编号")
    private String record;

    /**
     * 请求唯一标识
     */
    @Schema(description = "OCR请求唯一标识")
    private String requestId;

    /**
     * 国籍
     */
    @Schema(description = "持证人国籍")
    private String nationality;

    /**
     * 出生日期（格式：YYYY-MM-DD）
     */
    @Schema(description = "出生日期")
    private String birthDate;

    /**
     * 错误信息（识别失败时返回）
     */
    @Schema(description = "识别失败错误信息，成功不会返回此字段")
    private String message;

    /**
     * 签发机关
     */
    @Schema(description = "驾驶证签发机关")
    private String issueAuthority;

    /**
     * 准驾车型代号
     */
    @Schema(description = "准驾车型代号")
    private String approvedType;

    /**
     * 性别（男/女）
     */
    @Schema(description = "性别")
    private String sex;

    /**
     * 驾驶证副页号码
     */
    @Schema(description = "驾驶证背面编号")
    private String licenseNumberBack;

    /**
     * 初次领证日期
     */
    @Schema(description = "初次领证日期")
    private String initialIssueDate;

    /**
     * 错误码（识别失败时返回）
     */
    @Schema(description = "识别失败错误码，成功不会返回此字段")
    private String code;

    /**
     * 住址
     */
    @Schema(description = "持证人住址")
    private String address;

    /**
     * 业务流水号
     */
    @Schema(description = "OCR业务流水号")
    private String bizNo;

    /**
     * 档案记录编号
     */
    @Schema(description = "驾驶证档案记录编号")
    private String recordNumber;

}

