package com.smart.adp.infrastructure.utils;

import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description: 生成试驾协议文件工具类
 * @Author: rik.ren
 * @Date: 2025/06/25 09:32
 **/
@Slf4j
@Service
public class AgreementFileUtil {

    @Value("${businessConfig.testDriveAgreement.testDriveAgreementTemplateUrl:testDriveAgreementFile/TemplateForTestDriveNotification" +
            ".docx}")
    private String templatePath;

    @Value("${businessConfig.testDriveAgreement.testDriveAgreementOutputDir:testDriveAgreementFile-contracts}")
    private String outputDir;

    @Autowired
    private ObsUtils obsUtils;

    @Autowired
    @Qualifier("pdfTaskExecutor")
    private Executor pdfQueueExecutor;

    /**
     * 生成试驾协议入口
     *
     * @param generateData
     * @return
     */
    public String[] generateAgreementEntrance(GenerateData generateData) {
        File templateFile = null;
        File signatureImage = null;
        AtomicReference<File> pdfFile = new AtomicReference<>();
        AtomicReference<File> filledDocx = new AtomicReference<>();
        try {
            // 1. 从OBS下载模板和签名图片
            templateFile = obsUtils.downloadFile(templatePath);
            signatureImage = obsUtils.downloadFile(generateData.getSignatureUrl());

            // 2. 填充Word文档
            filledDocx.set(fillDocxTemplate(templateFile, signatureImage, generateData));

            // 3. 上传到OBS
            String wordPath = String.format("%s/%s/testDriveAgreement_%s_%s.docx",
                    outputDir,
                    LocalDateTime.now().format(TimeConstant.TIME_D_FORMATTER),
                    generateData.getPartyB(),
                    generateData.getTestDriveSheetId());
            String obsWordUrl = obsUtils.uploadToObs(filledDocx.get(), wordPath);

            // 4. 异步转换为PDF，使用了一个独立的线程池，核心线程只有1，当做串行队列使用
            CompletableFuture.runAsync(() -> {
                File tempPdfFile = null;
                try {
                    tempPdfFile = DocxToPdfConverter.convertDocxToPdf(filledDocx.get());
                    pdfFile.set(tempPdfFile);
                    String pdfPath = wordPath.replace(".docx", ".pdf");
                    obsUtils.uploadToObs(pdfFile.get(), pdfPath);
                } catch (Exception e) {
                    log.error("生成试驾协议转PDF异常 ", e);
                } finally {
                    if (filledDocx.get() != null) {
                        filledDocx.get().delete();
                    }
                    if (pdfFile.get() != null) {
                        pdfFile.get().delete();
                    }
                    if (tempPdfFile != null) {
                        tempPdfFile.delete();
                    }
                }
            }, pdfQueueExecutor);

            return new String[]{obsWordUrl, obsWordUrl.replace(".docx", ".pdf")};
        } catch (Exception e) {
            log.error("生成试乘试驾告知书失败", e);
            throw new BusinessException(RespCode.FAIL.getCode(), "生成试乘试驾告知书失败");
        } finally {
            // 5. 清理临时文件
            if (templateFile != null) {
                templateFile.delete();
            }
            if (signatureImage != null) {
                signatureImage.delete();
            }
        }
    }

    /**
     * 填充Word文档
     *
     * @param templateFile   下载的临时模板文件
     * @param signatureImage 用户签名图片文件
     * @param generateData   用户信息
     * @return word文件
     * @throws Exception
     */
    private File fillDocxTemplate(File templateFile, File signatureImage, GenerateData generateData) throws Exception {
        try (XWPFDocument doc = new XWPFDocument(new FileInputStream(templateFile))) {
            // 文本替换映射
            Map<String, String> replacements = new HashMap<>();
            replacements.put("<PartyA>", generateData.getPartyA());
            replacements.put("<PartyB>", generateData.getPartyB());
            replacements.put("<TestDriveType>", generateData.getTestDriveType());
            replacements.put("<IdCardNo>", generateData.getIdCardNo());
            replacements.put("<SignatureDate>", generateData.getSignDate());

            // 替换所有段落中的文本
            for (XWPFParagraph p : doc.getParagraphs()) {
                replaceTextInParagraph(p, replacements);
            }

            // 替换表格中的文本
            for (XWPFTable tbl : doc.getTables()) {
                for (XWPFTableRow row : tbl.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph p : cell.getParagraphs()) {
                            replaceTextInParagraph(p, replacements);
                        }
                    }
                }
            }

            // 替换签名图片
            replaceSignatureImage(doc, signatureImage);

            // 保存填充后的文档
            File filledDoc = File.createTempFile("filled_", ".docx");
            try (FileOutputStream out = new FileOutputStream(filledDoc)) {
                doc.write(out);
            }
            return filledDoc;
        }
    }

    /**
     * 替换表格中的文本
     *
     * @param paragraph
     * @param replacements
     */
    private void replaceTextInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        // 收集段落中所有Run的文本和位置
        List<TextSegment> segments = new ArrayList<>();
        StringBuilder paragraphText = new StringBuilder();

        for (XWPFRun run : paragraph.getRuns()) {
            String runText = run.getText(0);
            if (runText == null || runText.isEmpty()) continue;

            int start = paragraphText.length();
            paragraphText.append(runText);
            int end = paragraphText.length();

            segments.add(new TextSegment(run, start, end, runText));
        }

        String fullText = paragraphText.toString();
        if (fullText.isEmpty()) return;

        // 检查每个替换项
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            String key = entry.getKey();
            if (!fullText.contains(key)) continue;

            // 找到关键字的起始位置
            int keyStart = fullText.indexOf(key);
            int keyEnd = keyStart + key.length();

            // 清除所有Run的文本
            for (XWPFRun run : paragraph.getRuns()) {
                run.setText("", 0);
            }

            // 重建段落内容
            String newText = fullText.replace(key, entry.getValue());
            XWPFRun newRun = paragraph.createRun();
            newRun.setText(newText);

            // 复制原始格式（使用第一个Run的格式）
            if (!paragraph.getRuns().isEmpty()) {
                XWPFRun firstRun = paragraph.getRuns().get(0);
                newRun.setFontFamily(firstRun.getFontFamily());
                newRun.setFontSize(firstRun.getFontSize());
                newRun.setBold(firstRun.isBold());
                newRun.setItalic(firstRun.isItalic());
            }

            // 处理完一个替换项后退出，避免多次修改
            break;
        }
    }

    /**
     * 替换签名图片
     *
     * @param doc
     * @param signatureImage
     * @throws Exception
     */
    private void replaceSignatureImage(XWPFDocument doc, File signatureImage) throws Exception {
        // 获取图片实际尺寸（动态调整）
        BufferedImage bimg = ImageIO.read(signatureImage);
        int width = bimg.getWidth();
        int height = bimg.getHeight();

        // 查找签名位置（SignatureImage占位符）
        // 动态计算合适的尺寸
        int emuWidth = Units.toEMU(width > 0 ? Math.min(width, 150) : 100);
        int emuHeight = Units.toEMU(height > 0 ? Math.min(height, 80) : 80);
        outerLoop:
        for (XWPFTable table : doc.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph p : cell.getParagraphs()) {
                        if (p.getText().contains("<SignatureImage>")) {
                            // 清除该段落所有内容（包括原有占位符）
                            for (int i = p.getRuns().size() - 1; i >= 0; i--) {
                                p.removeRun(i);
                            }

                            // 插入新图片
                            try (FileInputStream is = new FileInputStream(signatureImage)) {
                                XWPFRun run = p.createRun();
                                run.addPicture(
                                        is,
                                        getImageType(signatureImage),  // 自动检测图片类型
                                        "signature.png",
                                        emuWidth,
                                        emuHeight
                                );
                            }
                            break outerLoop; // 找到并处理后退出
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据文件扩展名获取图片类型
     *
     * @param imageFile
     * @return
     */
    private int getImageType(File imageFile) {
        String name = imageFile.getName().toLowerCase();
        if (name.endsWith(".png")) return XWPFDocument.PICTURE_TYPE_PNG;
        if (name.endsWith(".jpg") || name.endsWith(".jpeg")) return XWPFDocument.PICTURE_TYPE_JPEG;
        if (name.endsWith(".gif")) return XWPFDocument.PICTURE_TYPE_GIF;
        if (name.endsWith(".bmp")) return XWPFDocument.PICTURE_TYPE_BMP;
        return XWPFDocument.PICTURE_TYPE_PNG; // 默认
    }

    // 辅助类，用于跟踪文本位置
    private static class TextSegment {
        XWPFRun run;
        int start;
        int end;
        String text;

        TextSegment(XWPFRun run, int start, int end, String text) {
            this.run = run;
            this.start = start;
            this.end = end;
            this.text = text;
        }
    }

    /**
     * 用户数据结构
     */
    @Data
    public static class GenerateData {
        /**
         * 甲方
         */
        private String partyA;
        /**
         * 乙方
         */
        private String partyB;
        /**
         * 试驾类型
         */
        private String testDriveType;
        /**
         * 身份证号
         */
        private String idCardNo;
        /**
         * 签署时间
         */
        private String signDate;
        /**
         * 签名图片URL
         */
        private String signatureUrl;
        /**
         * 试驾单id
         */
        private String testDriveSheetId;
    }
}