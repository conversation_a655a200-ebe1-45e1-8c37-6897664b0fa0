package com.smart.adp.infrastructure.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.smart.adp.domain.common.constants.MQConstants.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    /**
     * message converter
     */
    @Bean
    public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public RabbitTemplate rabbitTemplate(
            ConnectionFactory connectionFactory,
            MessageConverter converter) {

        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(converter);

        // 发送确认回调
        template.setConfirmCallback((correlation, ack, reason) -> {
            if (ack) {
//                log.info("消息到达Broker: {}", correlation.getId());
            } else {
                log.error("消息未到达 Broker: {}", reason);
            }
        });

        // 路由失败回调
        template.setReturnsCallback(returned -> {
            log.error("消息无法路由到队列: {}", returned.getReplyText());
        });

        return template;
    }

    @Bean
    public CachingConnectionFactory connectionFactory(RabbitProperties properties) {
        CachingConnectionFactory factory = new CachingConnectionFactory();
        factory.setHost(properties.getHost());
        factory.setPort(properties.getPort());
        factory.setUsername(properties.getUsername());
        factory.setPassword(properties.getPassword());
        factory.setVirtualHost(properties.getVirtualHost());
        // recovery
        factory.getRabbitConnectionFactory().setAutomaticRecoveryEnabled(true);
        factory.getRabbitConnectionFactory().setNetworkRecoveryInterval(5000);
        factory.getRabbitConnectionFactory().setTopologyRecoveryEnabled(true);
        factory.setRequestedHeartBeat(60);
        return factory;
    }
}
