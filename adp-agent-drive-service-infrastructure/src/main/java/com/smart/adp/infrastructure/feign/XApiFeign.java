package com.smart.adp.infrastructure.feign;

import com.smart.adp.domain.common.resp.EntityResult;
import com.smart.adp.domain.common.resp.MessageClueResponse;
import com.smart.adp.domain.common.resp.OptResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description: 调用xapi.api服务的feign
 * @Author: rik.ren
 * @Date: 2025/5/11 20:08
 **/
@FeignClient(name = "${feign.client.config.adp-xapi-api-service.url}", url = "${feign.client.config.adp-xapi-api-service.url}")
public interface XApiFeign {
    /**
     * 发送短信
     *
     * @param param
     * @return
     */
    @PostMapping("/noauth/rest/SMS_ADP/SMS_ADP_001")
    MessageClueResponse smsSendMessage(@RequestBody Map<String, Object> param);

    /**
     * 向接口表写入数据
     *
     * @param ms
     * @param param
     * @return
     */
    @PostMapping("/rest/ADP_INSERT/{ms}")
    OptResult sendApiData(@PathVariable("ms") String ms, @RequestBody Map<String, Object> param);

    /**
     * 试驾结束推送ZTMQ
     *
     * @param ms
     * @param param
     * @return
     */
    @PostMapping("/rest/ADP_MS/{ms}")
    EntityResult<List<String>> ms(@PathVariable("ms") String ms, @RequestBody Object param);
}
