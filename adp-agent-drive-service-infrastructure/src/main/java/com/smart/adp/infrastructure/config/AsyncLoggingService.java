package com.smart.adp.infrastructure.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * @Description: 日志记录
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Service
public class AsyncLoggingService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncLoggingService.class);

    @Async("taskExecutor")
    public void logMethodParams(String methodName, Object[] args) {
        String params = Arrays.stream(args)
                .map(arg -> arg == null ? "null" : arg.toString())
                .collect(Collectors.joining(", "));
        logger.info("请求的方法: {} 入参: {}", methodName, params);
    }

    @Async("taskExecutor")
    public void info(String log, Object... args) {
        logger.info(log, args);
    }
}