package com.smart.adp.infrastructure.advice;

import java.util.List;

/**
 * @Description: 需要执行的业务逻辑函数式接口
 * @Author: rik.ren
 * @Date: 2025/03/19 10:58
 **/
@FunctionalInterface
public interface MyBusinessLogicFunction<T, M> {

    /**
     * 需要在某个任务完毕后，执行的业务逻辑代码
     *
     * @param executorListParam 并行任务执行完的结果集，此处一定是集合，集合中是并行任务中每个任务的结果
     * @param param             后续函数方法需要用到的参数
     * @return 自定义类型
     */
    M myselfBusinessLogic(List<T> executorListParam, Object[] param);
}
