package com.smart.adp.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisUtil {

    private static int defaultTime = 86400;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public boolean setExpire(String key, long time) {
        try {
            if (time > 0L) {
                this.redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception var5) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.setExpire1.exception", var5);
            return false;
        }
    }

    public boolean setExpire(String key, long time, TimeUnit timeUnit) {
        try {
            if (time > 0L) {
                this.redisTemplate.expire(key, time, timeUnit);
            }
            return true;
        } catch (Exception var5) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.setExpire2.exception", var5);
            return false;
        }
    }

    public long getExpire(String key) {
        return this.redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    public boolean hasKey(String key) {
        try {
            return this.redisTemplate.hasKey(key);
        } catch (Exception var3) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.hasKey.exception", var3);
            return false;
        }
    }

    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                this.redisTemplate.delete(key[0]);
            } else {
                this.redisTemplate.delete((Collection<String>) CollectionUtils.arrayToList(key));
            }
        }
    }

    public Object get(String key) {
        return key == null ? null : this.redisTemplate.opsForValue().get(key);
    }

    public boolean set(String key, Object value) {
        try {
            this.redisTemplate.opsForValue().set(key, value, (long) defaultTime, TimeUnit.SECONDS);
            return true;
        } catch (Exception var4) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.set1.exception", var4);
            return false;
        }
    }

    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0L) {
                this.redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                this.set(key, value);
            }
            return true;
        } catch (Exception var6) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.set2.exception", var6);
            return false;
        }
    }

    public boolean set(String key, Object value, long time, TimeUnit timeUnit) {
        try {
            if (time > 0L) {
                this.redisTemplate.opsForValue().set(key, value, time, timeUnit);
            } else {
                this.set(key, value);
            }
            return true;
        } catch (Exception var6) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.set3.exception", var6);
            return false;
        }
    }

    public boolean hset(String key, String item, Object value) {
        try {
            this.redisTemplate.opsForHash().put(key, item, value);
            this.setExpire(key, (long) defaultTime);
            return true;
        } catch (Exception var5) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.hset1.exception", var5);
            return false;
        }
    }

    public boolean hset(String key, String item, Object value, long time, TimeUnit timeUni) {
        try {
            redisTemplate.setHashKeySerializer(new StringRedisSerializer());
            redisTemplate.setHashValueSerializer(new StringRedisSerializer());
            byte[] bytes = item.getBytes(StandardCharsets.UTF_8);
            this.redisTemplate.opsForHash().put(key, new String(bytes, StandardCharsets.UTF_8), value);
            this.setExpire(key, time, timeUni);
            return true;
        } catch (Exception var5) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.hset2.exception", var5);
            return false;
        }
    }

    public boolean hset(String key, String item, Object value, long time) {
        try {
            this.redisTemplate.opsForHash().put(key, item, value);
            if (time > 0L) {
                this.setExpire(key, time);
            }
            return true;
        } catch (Exception var7) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.hset3.exception", var7);
            return false;
        }
    }

    public Object hget(String key, String item) {
        return this.redisTemplate.opsForHash().get(key, item);
    }

    public void hdelAll(String key, Object... item) {
        this.redisTemplate.opsForHash().delete(key, item);
    }

    public boolean hHasKey(String key, String item) {
        return this.redisTemplate.opsForHash().hasKey(key, item);
    }

    public long setRemove(String key, Object... values) {
        try {
            Long count = this.redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception var4) {
            log.error("com.smart.adp.infrastructure.utils.RedisUtil.setRemove.exception", var4);
            return 0L;
        }
    }
}
