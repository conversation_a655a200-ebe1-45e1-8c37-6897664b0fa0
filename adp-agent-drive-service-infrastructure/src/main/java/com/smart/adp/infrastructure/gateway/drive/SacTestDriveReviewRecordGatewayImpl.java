package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.model.drive.bo.TestDriveReviewRecordBO;
import com.smart.adp.domain.model.drive.entity.SacTestDriveReviewRecordEntity;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveReviewRecordGateway;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveReviewRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveReviewRecordEntityTableDef.SAC_TEST_DRIVE_REVIEW_RECORD_ENTITY;

/**
 * @Description: 试乘试驾跟进gateway实现
 * @Author: rik.ren
 * @Date: 2025/5/27 17:49
 **/
@Service
public class SacTestDriveReviewRecordGatewayImpl implements SacTestDriveReviewRecordGateway {

    @Autowired
    private SacTestDriveReviewRecordMapper sacTestDriveReviewRecordMapper;

    /**
     * 查询试乘试驾跟进记录
     *
     * @param param
     * @return
     */
    @Override
    public SacTestDriveReviewRecordEntity queryTestDriveReviewRecord(TestDriveReviewRecordBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_TEST_DRIVE_REVIEW_RECORD_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(needColumn).from(SAC_TEST_DRIVE_REVIEW_RECORD_ENTITY.as("record")).limit(1);
        param.buildTestDriveReviewConditions(wrapper);
        SacTestDriveReviewRecordEntity result = sacTestDriveReviewRecordMapper.selectOneByQuery(wrapper);
        return result;
    }
}
