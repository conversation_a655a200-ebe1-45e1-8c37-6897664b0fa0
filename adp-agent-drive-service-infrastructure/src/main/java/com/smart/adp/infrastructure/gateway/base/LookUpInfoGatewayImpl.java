package com.smart.adp.infrastructure.gateway.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.model.base.bo.LookUpInfoBO;
import com.smart.adp.domain.model.base.gateway.LookUpInfoGateway;
import com.smart.adp.domain.model.base.valueObject.LookUpInfoVO;
import com.smart.adp.infrastructure.repository.base.LookUpInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.model.base.valueObject.table.LookUpInfoVOTableDef.LOOK_UP_INFO_VO;

/**
 * @Description: lookUpInfo的gateway实现
 * @Author: rik.ren
 * @Date: 2025/7/1 13:38
 **/
@Slf4j
@Service
public class LookUpInfoGatewayImpl implements LookUpInfoGateway {

    @Autowired
    private LookUpInfoMapper lookUpInfoMapper;

    /**
     * 根据条件查询字典信息
     *
     * @param param
     * @param columns
     * @return
     */
    @Override
    public List<LookUpInfoBO> queryLookUpInfo(LookUpInfoBO param, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{LOOK_UP_INFO_VO.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(LOOK_UP_INFO_VO.as("look"));
        param.buildConditions(wrapper);
        List<LookUpInfoVO> listLookUpInfoVO = lookUpInfoMapper.selectListByQuery(wrapper);
        if (CollectionUtil.isNotEmpty(listLookUpInfoVO)) {
            return BeanUtil.copyToList(listLookUpInfoVO, LookUpInfoBO.class);
        }
        return Collections.emptyList();
    }
}
