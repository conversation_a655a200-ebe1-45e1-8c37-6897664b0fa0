package com.smart.adp.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 更新意向车信息DTO
 * @Author: rik.ren
 * @Date: 2025/5/29 13:26
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModifyIntentionCarReq implements Serializable {

    /**
     * 线索 ID
     */
    private String custId;

    /**
     * 新的意向车
     */
    private String newIntentionCar;

    /**
     * 最近一次试驾时间
     */
    private LocalDateTime lastTestdriverTime;

}
