package com.smart.adp.infrastructure.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

/**
 * @ClassName ObjectUtils
 * @Description
 * <AUTHOR>
 * @Date 2023/3/25 17:18
 * @Version 1.0
 */
@Slf4j
public class ObjectUtils {
    public static boolean checkObjAllFieldsIsNull(Object object) {
        if (null == object) {
            return true;
        }
        try {
            for (Field f : object.getClass().getDeclaredFields()) {
                ReflectionUtils.makeAccessible(f);
                if (f.get(object) != null && StringUtils.isNotBlank(f.get(object).toString())) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("com.smart.adp.infrastructure.utils.ObjectUtils.checkObjAllFieldsIsNull.exception", e);
        }
        return true;
    }
}
