package com.smart.adp.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 调用中台OCR识别入参
 * @Author: rik.ren
 * @Date: 2025/6/20 10:53
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MidOCRReq {

    /**
     * 业务来源
     */
    @Builder.Default
    private String bizSource = "adp";
    /**
     * 识别参数
     */
    private List<Recognizes> recognizes;

    @Data
    public class Recognizes {
        /**
         * 业务编号
         */
        private String bizNo = "1";
        /**
         * 图片链接
         */
        private String url;
        /**
         * 图片二进制文件
         */
        private String body;
    }
}
