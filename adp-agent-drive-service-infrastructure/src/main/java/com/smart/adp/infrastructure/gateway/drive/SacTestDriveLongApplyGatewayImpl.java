package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.model.drive.entity.SacTestDriveLongApplyEntity;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveLongApplyGateway;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveLongApplyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveLongApplyEntityTableDef.SAC_TEST_DRIVE_LONG_APPLY_ENTITY;

/**
 * @Description: 超长试驾申请gateway实现
 * @Author: rik.ren
 * @Date: 2025/5/22 17:17
 **/
@Service
public class SacTestDriveLongApplyGatewayImpl implements SacTestDriveLongApplyGateway {

    @Autowired
    private SacTestDriveLongApplyMapper sacTestDriveLongApplyMapper;

    /**
     * 根据条件查询超长试驾申请信息
     *
     * @param param
     * @return
     */
    @Override
    public List<SacTestDriveLongApplyEntity> queryTestDriveLongApplyByCondition(SacTestDriveLongApplyEntity param,
                                                                                QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_TEST_DRIVE_LONG_APPLY_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(needColumn).from(SAC_TEST_DRIVE_LONG_APPLY_ENTITY.as("long"));
        param.buildCondition(wrapper);
        return sacTestDriveLongApplyMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据条件查询超长试驾申的个数
     *
     * @param param
     * @return
     */
    @Override
    public Long queryTestDriveLongApplyCountByCondition(SacTestDriveLongApplyEntity param) {
        if (ObjectUtil.isEmpty(param)) {
            return 0L;
        }
        QueryWrapper wrapper = QueryWrapper.create().select().from(SAC_TEST_DRIVE_LONG_APPLY_ENTITY.as("long"));
        param.buildCondition(wrapper);
        return sacTestDriveLongApplyMapper.selectCountByQuery(wrapper);
    }
}
