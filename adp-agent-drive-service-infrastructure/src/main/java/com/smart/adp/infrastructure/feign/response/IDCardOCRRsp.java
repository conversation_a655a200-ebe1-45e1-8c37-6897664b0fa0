package com.smart.adp.infrastructure.feign.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 身份证识别结果
 * @Author: rik.ren
 * @Date: 2025/6/19 17:50
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IDCardOCRRsp {
    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;
    /**
     * 请求id
     */
    @Schema(description = "请求id")
    private String requestId;
    /**
     * 有效期限
     */
    @Schema(description = "有效期限")
    private String validPeriod;
    /**
     * 出生日期（格式：YYYY-MM-DD）
     */
    @Schema(description = "出生日期")
    private String birthDate;
    /**
     * 错误信息，成功识别是没有这个字段
     */
    @Schema(description = "错误信息，成功识别是没有这个字段")
    private String message;
    /**
     * 签发机关
     */
    @Schema(description = "签发机关")
    private String issueAuthority;
    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    private String idNumber;
    /**
     * 性别
     */
    @Schema(description = "性别")
    private String sex;
    /**
     * 民族
     */
    @Schema(description = "民族")
    private String ethnicity;
    /**
     * 错误码，如果识别成功没有这个字段
     */
    @Schema(description = "错误码，如果识别成功没有这个字段")
    private String code;
    /**
     * 住址
     */
    @Schema(description = "住址")
    private String address;
    /**
     * 业务编号
     */
    @Schema(description = "业务编号")
    private String bizNo;

}
