package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;
import com.smart.adp.domain.model.drive.gateway.SacAppointmentSheetGateway;
import com.smart.adp.infrastructure.repository.drive.SacAppointmentSheetEntityMapper;
import com.smart.adp.infrastructure.repository.drive.SacAppointmentSheetMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * @Description: 试乘试驾预约单gateway实现
 * @Author: rik.ren
 * @Date: 2025/5/22 15:25
 **/
@Service
public class SacAppointmentSheetGatewayImpl implements SacAppointmentSheetGateway {

    @Autowired
    private SacAppointmentSheetMapper sacAppointmentSheetMapper;

    @Autowired
    private SacAppointmentSheetEntityMapper sacAppointmentSheetEntityMapper;

    /**
     * 根据条件查询预约单信息
     *
     * @param param
     * @param needColumn
     * @return
     */
    @Override
    public List<SacAppointmentSheetBO> queryAppointByCondition(SacAppointmentSheetBO param, QueryColumn... needColumn) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        if (ObjectUtil.isEmpty(needColumn)) {
            needColumn = new QueryColumn[]{SAC_APPOINTMENT_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(needColumn).from(SAC_APPOINTMENT_SHEET_ENTITY.as("app"))
                .innerJoin(SAC_TEST_DRIVE_SHEET_ENTITY.as("test")).on(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID));
        param.buildCondition(wrapper);
        return sacAppointmentSheetMapper.selectListByQuery(wrapper);
    }

    /**
     * 根据条件查询预约单的个数
     *
     * @param param
     * @return
     */
    @Override
    public Long queryAppointByCondition(SacAppointmentSheetBO param) {
        if (ObjectUtil.isEmpty(param)) {
            return 0L;
        }
        QueryWrapper wrapper = QueryWrapper.create().select().from(SAC_APPOINTMENT_SHEET_ENTITY.as("app"))
                .innerJoin(SAC_TEST_DRIVE_SHEET_ENTITY.as("test"))
                .on(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID));
        param.buildCondition(wrapper);
        return sacAppointmentSheetMapper.selectCountByQuery(wrapper);
    }

    /**
     * 创建试乘试驾预约单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean createAppointmentSheet(SacAppointmentSheetBO param) {
        return sacAppointmentSheetEntityMapper.insert(param) > 0;
    }

    /**
     * 更新预约单信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyAppointmentSheet(SacAppointmentSheetBO param) {
        if (StringUtils.isEmpty(param.getAppointmentId())) {
            return false;
        }
        // 更新的条件
        QueryCondition condition = QueryCondition.create(new QueryColumn("APPOINTMENT_ID"), SqlConsts.EQUALS, param.getAppointmentId());
        // 更新的字段就是param中的不为空的字段
        return sacAppointmentSheetEntityMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 删除预约单信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean deleteAppointmentSheet(SacAppointmentSheetBO param) {
        return sacAppointmentSheetEntityMapper.delete(param) > 0;
    }
}
