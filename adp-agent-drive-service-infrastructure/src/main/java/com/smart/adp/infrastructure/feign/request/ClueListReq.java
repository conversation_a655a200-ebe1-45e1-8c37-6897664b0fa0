package com.smart.adp.infrastructure.feign.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/25
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Builder
@Schema(description = "线索列表请求")
public class ClueListReq {

    @Schema(description = "手机号列表")
    private List<String> phones;

    @Schema(description = "客户 ID 列表")
    private List<String> custIds;

    @Schema(description = "线索 ID 列表")
    private List<String> clueIds;
}
