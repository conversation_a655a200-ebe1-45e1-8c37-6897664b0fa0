package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryOrderBy;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.common.resp.EntityResult;
import com.smart.adp.domain.common.resp.MessageClueResponse;
import com.smart.adp.domain.common.resp.OptResult;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.drive.entity.SacTestDriveSheetEntity;
import com.smart.adp.domain.enums.DriveTaskStateCodeEnum;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveSheetGateway;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.infrastructure.feign.XApiFeign;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveSheetEntityMapper;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveSheetMapper;
import com.smart.adp.infrastructure.repository.drive.SacTestDriveTaskMapper;
import com.smart.adp.infrastructure.utils.PageConverterFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.smart.adp.domain.model.clue.entity.table.SacClueInfoDlrEntityTableDef.SAC_CLUE_INFO_DLR_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveTaskEntityTableDef.SAC_TEST_DRIVE_TASK_ENTITY;

/**
 * @Description: 试驾gateway实现
 * @Author: rik.ren
 * @Date: 2025/3/15 15:27
 **/
@Slf4j
@Service
public class SacTestDriveSheetGatewayImpl implements SacTestDriveSheetGateway {

    @Autowired
    private SacTestDriveSheetMapper testDriveSheetMapper;

    @Autowired
    private SacTestDriveSheetEntityMapper testDriveSheetEntityMapper;

    @Autowired
    private SacTestDriveTaskMapper testDriveTaskMapper;

    @Autowired
    private XApiFeign xApiFeign;

    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns 尽量指定字段查询
     * @return
     */
    @Override
    public DomainPage<SacTestDriveSheetBO> queryTestDriveSheet(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                               QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"));
        boParam.buildTestDriveCondition(wrapper);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacTestDriveSheetBO> paginate = testDriveSheetMapper.paginate(boParam.getPageNumber(),
                boParam.getPageSize(), wrapper);
        DomainPage<SacTestDriveSheetBO> domainPage = PageConverterFactory.toDomainPage(paginate);
        return domainPage;
    }

    /**
     * 查询试驾单
     *
     * @param boParam
     * @param orderBy
     * @param columns 尽量指定字段查询
     * @return
     */
    @Override
    public DomainPage<SacTestDriveSheetBO> queryTestDriveSheet(SacTestDriveSheetBO boParam, SacClueInfoDlrBO boClueParam,
                                                               QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = null;
        if (StringUtils.isBlank(boParam.getInfoChanMCode()) && StringUtils.isBlank(boParam.getInfoChanDCode())) {
            // 没有查询二级渠道和三级渠道，就不用关联线索表
            wrapper =
                    QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"))
                            .leftJoin(SAC_APPOINTMENT_SHEET_ENTITY.as("appointment"))
                            .on(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID));
        } else {
            wrapper =
                    QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"))
                            .leftJoin(SAC_APPOINTMENT_SHEET_ENTITY.as("appointment"))
                            .on(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID))
                            .leftJoin(SAC_CLUE_INFO_DLR_ENTITY.as("clue"))
                            .on(SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID.eq(SAC_CLUE_INFO_DLR_ENTITY.CUST_ID));
        }
        boParam.buildTestDriveCondition(wrapper);
        boClueParam.conditions(wrapper);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        Page<SacTestDriveSheetBO> paginate = testDriveSheetMapper.paginate(boParam.getPageNumber(),
                boParam.getPageSize(), wrapper);
        return PageConverterFactory.toDomainPage(paginate);
    }

    /**
     * 根据条件查询试驾单满足条件的总数
     *
     * @param boParam
     * @return
     */
    @Override
    public Long queryTestDriveSheetCount(SacTestDriveSheetBO boParam, SacClueInfoDlrBO boClueParam) {
        QueryWrapper wrapper = null;
        if (StringUtils.isBlank(boParam.getInfoChanMCode()) && StringUtils.isBlank(boParam.getInfoChanDCode())) {
            // 没有查询二级渠道和三级渠道，就不用关联线索表
            wrapper =
                    QueryWrapper.create().from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"))
                            .leftJoin(SAC_APPOINTMENT_SHEET_ENTITY.as("appointment"))
                            .on(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID));
        } else {
            wrapper =
                    QueryWrapper.create().from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"))
                            .leftJoin(SAC_APPOINTMENT_SHEET_ENTITY.as("appointment"))
                            .on(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID))
                            .leftJoin(SAC_CLUE_INFO_DLR_ENTITY.as("clue"))
                            .on(SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID.eq(SAC_CLUE_INFO_DLR_ENTITY.CUST_ID));
        }
        boParam.buildTestDriveCondition(wrapper);
        boClueParam.conditions(wrapper);
        return testDriveSheetMapper.selectCountByQuery(wrapper);
    }

    /**
     * 查询试驾单
     *
     * @param boParam
     * @param columns 尽量指定字段查询
     * @return
     */
    @Override
    public SacTestDriveSheetBO queryTestDriveSheet(SacTestDriveSheetBO boParam, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = null;
        if (boParam.getNeedAppointmentSheet()) {
            wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"))
                    .innerJoin(SAC_APPOINTMENT_SHEET_ENTITY.as("appointment"))
                    .on(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID.eq(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID))
                    .limit(1);
        } else {
            wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet")).limit(1);
        }
        boParam.buildTestDriveCondition(wrapper);
        SacTestDriveSheetBO result = testDriveSheetMapper.selectOneByQuery(wrapper);
        return result;
    }

    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    @Override
    public List<SacTestDriveSheetBO> queryTestDriveSheetList(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                             QueryOrderBy orderBy, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_TEST_DRIVE_SHEET_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create().select(columns).from(SAC_TEST_DRIVE_SHEET_ENTITY.as("sheet"));
        boParam.buildTestDriveCondition(wrapper);
        if (ObjectUtil.isEmpty(orderBy)) {
            orderBy = new QueryOrderBy(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SqlConsts.DESC);
        }
        wrapper.orderBy(orderBy);
        return testDriveSheetMapper.selectListByQuery(wrapper);
    }

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean updateTestDriveSheet(SacTestDriveSheetBO param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("TEST_DRIVE_SHEET_ID"), SqlConsts.EQUALS,
                param.getTestDriveSheetId());
        return testDriveSheetEntityMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 插入试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean insertTestDriveSheet(SacTestDriveSheetEntity param) {
        return testDriveSheetEntityMapper.insert(param) > 0;
    }

    @Override
    public long toDoTestDriveTaskNum(UserBusiEntity user) {
        QueryWrapper wrapper = QueryWrapper.create().and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_DLR_CODE.eq(user.getDlrCode(),
                StringUtil::hasText)).and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_STATE_CODE.eq(DriveTaskStateCodeEnum.TO_DO.getCode())).and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_ID.eq(user.getUserID(), UserUtil::productExpertValid));

        return testDriveTaskMapper.selectCountByQuery(wrapper);
    }

    @Override
    public long toDoTestDriveNum(UserBusiEntity user) {
        QueryWrapper wrapper =
                QueryWrapper.create().and(SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CODE.eq(user.getDlrCode(), StringUtil::hasText)).and(SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID.eq(user.getUserID(), UserUtil::productExpertValid)).and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS.in(TestDriveStatusEnum.NOT_STARTED.getCode(), TestDriveStatusEnum.IN_PROGRESS.getCode()));

        return testDriveSheetMapper.selectCountByQuery(wrapper);
    }

    /**
     * 发送试驾短信
     *
     * @param param
     * @return
     */
    @Override
    public Boolean sendTestDriveMsg(Map<String, Object> param) {
        MessageClueResponse result = xApiFeign.smsSendMessage(param);
        if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getMessage())) {
            log.error("发送试驾短信错误");
            return Boolean.FALSE;
        }
        log.info("发送试驾短信结果 {}", JSONObject.toJSONString(result));
        return Boolean.TRUE;
    }

    /**
     * 删除试乘试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean delTestDriveSheet(SacTestDriveSheetBO param) {
        return testDriveSheetEntityMapper.delete(param) > 0;
    }

    /**
     * 试驾结束发送ZTMQ
     *
     * @param param
     * @return
     */
    @Override
    public Boolean vehicleData(Map<String, Object> param) {
        OptResult vehicleDataResult = xApiFeign.sendApiData("drive_insert_vehicle_data", param);
        if (ObjectUtil.isEmpty(vehicleDataResult)) {
            log.error("试驾结束时记录接口表，获取BI车机数据异常");
            return Boolean.FALSE;
        }
        log.info("试驾结束时记录接口表获取BI车机数据结果 {}", JSONObject.toJSONString(vehicleDataResult));
        return Boolean.TRUE;
    }

    /**
     * 试驾结束发送ZTMQ
     *
     * @param param
     * @return
     */
    @Override
    public Boolean sendZTMQ(Map<String, Object> param) {
        EntityResult<List<String>> xapiResult = xApiFeign.ms("adp_ms_base_driveEnd", param);
        if (ObjectUtil.isEmpty(xapiResult)) {
            log.error("结束试驾发送ZTMQ错误");
            return Boolean.FALSE;
        }
        log.info("结束试驾发送ZTMQ返回结果 {}", JSONObject.toJSONString(xapiResult));
        return Boolean.TRUE;
    }
}
