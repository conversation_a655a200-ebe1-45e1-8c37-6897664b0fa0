package com.smart.adp.infrastructure.gateway.drive;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryCondition;
import com.mybatisflex.core.query.QueryWrapper;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;
import com.smart.adp.domain.model.drive.gateway.TestCarPrepareGateway;
import com.smart.adp.infrastructure.repository.drive.TestCarPrepareEntityMapper;
import com.smart.adp.infrastructure.repository.drive.TestCarPrepareMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.smart.adp.domain.model.base.entity.table.AgentDlrInfoEntityTableDef.AGENT_DLR_INFO_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.TestCarPrepareEntityTableDef.TEST_CAR_PREPARE_ENTITY;


/**
 * @Description: 试驾车gateway实现
 * @Author: rik.ren
 * @Date: 2025/5/16 14:03
 **/
@Service
public class TestCarPrepareGatewayImpl implements TestCarPrepareGateway {

    @Resource
    private TestCarPrepareMapper testCarPrepareMapper;
    @Resource
    private TestCarPrepareEntityMapper testCarPrepareEntityMapper;

    @Override
    public List<TestCarPrepareBO> queryTestCarPrepareList(TestCarPrepareBO param, QueryColumn... columns) {
        if (ObjectUtil.isNull(param)) {
            return Collections.emptyList();
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(TEST_CAR_PREPARE_ENTITY.as("pp"))
                .innerJoin(AGENT_DLR_INFO_ENTITY.as("dlr")).on(TEST_CAR_PREPARE_ENTITY.APPLY_DLR_CODE.eq(AGENT_DLR_INFO_ENTITY.DLR_CODE));
        param.testCarConditions(wrapper);
        return testCarPrepareMapper.selectListByQuery(wrapper);
    }

    /**
     * 更新试驾车信息
     *
     * @param param
     * @return
     */
//    @Override
    public Boolean modifyTestCarPrepare1(TestCarPrepareBO param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CAR_LICENCE_NO"), SqlConsts.EQUALS,
                param.getCarLicenceNo());
        return testCarPrepareMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 更新试驾车信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestCarPrepare(TestCarPrepareBO param) {
        QueryCondition condition = QueryCondition.create(new QueryColumn("CAR_LICENCE_NO"), SqlConsts.EQUALS,
                param.getCarLicenceNo());
        return testCarPrepareEntityMapper.updateByCondition(param, condition) > 0;
    }

    /**
     * 更新试驾车信息给结束试驾使用
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestCarPrepareForStop(TestCarPrepareBO param) {
        return testCarPrepareEntityMapper.updatePrepareForStop(param) > 0;
    }

    /**
     * 根据条件获取试驾车信息
     *
     * @param param
     * @param columns
     * @return
     */
    @Override
    public TestCarPrepareBO queryTestCarPrepare(TestCarPrepareBO param, QueryColumn... columns) {
        if (ObjectUtil.isNull(param)) {
            return null;
        }
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{TEST_CAR_PREPARE_ENTITY.ALL_COLUMNS};
        }
        QueryWrapper wrapper = QueryWrapper.create()
                .select(columns)
                .from(TEST_CAR_PREPARE_ENTITY.as("pp"))
                .innerJoin(AGENT_DLR_INFO_ENTITY.as("dlr")).on(TEST_CAR_PREPARE_ENTITY.APPLY_DLR_CODE.eq(AGENT_DLR_INFO_ENTITY.DLR_CODE))
                .limit(1);
        param.testCarConditions(wrapper);
        return testCarPrepareMapper.selectOneByQuery(wrapper);
    }
}
