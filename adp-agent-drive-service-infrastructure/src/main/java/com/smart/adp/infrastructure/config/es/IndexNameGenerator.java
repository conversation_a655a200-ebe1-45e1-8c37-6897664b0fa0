package com.smart.adp.infrastructure.config.es;

import cn.hutool.core.text.StrPool;
import com.mybatisflex.core.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.index.AliasActionParameters;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/5/30
 */
@Component("indexNameGenerator")
public class IndexNameGenerator {

    @Value("${system.meta.esIndex:}")
    private String suffix;
    private static final String ALIAS_SUFFIX = "_alias";

    /**
     * 获取当前索引
     *
     * @param indexName 索引名
     * @return java.lang.String
     */
    public String getIndex(String indexName) {
        if (StringUtil.noText(suffix)) {
            return indexName;
        } else {
            return indexName + StrPool.UNDERLINE + suffix;
        }
    }

    /**
     * 获取索引别名
     *
     * @param indexName 索引名
     * @return java.lang.String
     */
    public String getAlias(String indexName) {
        return getIndex(indexName) + ALIAS_SUFFIX;
    }

    /**
     * 获取索引别名参数
     *
     * @param indexName 索引名
     * @return org.springframework.data.elasticsearch.core.index.AliasActionParameters
     */
    public AliasActionParameters getActionParameters(String indexName) {
        return AliasActionParameters.builder()
                                    .withIndices(getIndex(indexName))
                                    .withAliases(getAlias(indexName))
                                    .build();
    }
}
