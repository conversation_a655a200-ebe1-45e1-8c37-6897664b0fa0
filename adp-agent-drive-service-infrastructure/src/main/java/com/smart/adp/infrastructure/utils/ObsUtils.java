package com.smart.adp.infrastructure.utils;

import com.obs.services.ObsClient;
import com.obs.services.model.GetObjectRequest;
import com.obs.services.model.ObsObject;
import com.obs.services.model.PutObjectResult;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Description: OBS工具类
 * @Author: rik.ren
 * @Date: 2025/06/25 18:10
 **/
@Slf4j
@Component
public class ObsUtils {

    @Value("${huawei.obs.endpoint}")
    private String endpoint;

    @Value("${huawei.obs.ak}")
    private String ak;

    @Value("${huawei.obs.sk}")
    private String sk;

    @Value("${huawei.obs.bucket}")
    private String bucketName;

    @Value("${huawei.obs.customDomainName}")
    private String customDomainName;


    private ObsClient obsClient;

    @PostConstruct
    public void init() {
        this.obsClient = new ObsClient(ak, sk, endpoint);
        log.info("obsClient初始化完成");
    }

    /**
     * 统一文件下载入口
     *
     * @param source
     * @return
     * @throws IOException
     */
    public File downloadFile(String source) throws IOException {
        if (source == null || source.isEmpty()) {
            throw new IllegalArgumentException("文件来源不能为空");
        }

        if (source.startsWith("http://") || source.startsWith("https://")) {
            log.info("从URL下载文件: {}", source);
            return downloadFromUrl(source);
        } else {
            log.info("从OBS下载文件，对象键: {}", source);
            return downloadFromObs(source);
        }
    }

    // 从URL下载
    private File downloadFromUrl(String url) throws IOException {
        File tempFile = File.createTempFile("url_temp_", getFileExtension(url));

        try {
            URL fileUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) fileUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();

            if (connection.getResponseCode() != 200) {
                throw new IOException("HTTP请求失败: " + connection.getResponseCode());
            }

            try (InputStream input = connection.getInputStream();
                 OutputStream output = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
            }
            return tempFile;
        } catch (Exception e) {
            tempFile.delete();
            throw new IOException("下载URL文件失败: " + url, e);
        }
    }

    /**
     * 从文件路径下载
     *
     * @param objectKey
     * @return
     * @throws IOException
     */
    private File downloadFromObs(String objectKey) throws IOException {
        File tempFile = File.createTempFile("obs_temp_", objectKey.substring(objectKey.lastIndexOf(".")));
        try {
            ObsObject object = obsClient.getObject(new GetObjectRequest(bucketName, objectKey));
            InputStream input = object.getObjectContent();
            OutputStream output = new FileOutputStream(tempFile);
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            tempFile.delete();
            throw e;
        }
        return tempFile;
    }

    /**
     * 文件上传OBS
     *
     * @param file
     * @param objectKey
     * @return
     */
    public String uploadToObs(File file, String objectKey) {
        PutObjectResult result = obsClient.putObject(bucketName, objectKey, file);
        if (Integer.parseInt(RespCode.OK.getCode()) != result.getStatusCode()) {
            throw new BusinessException(RespCode.FAIL.getCode(), "文件上传失败");
        }
        return customDomainName + objectKey;
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName
     * @return
     */
    private String getFileExtension(String fileName) {
        if (fileName.contains(".")) {
            return "." + fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        return ".dat";
    }
}