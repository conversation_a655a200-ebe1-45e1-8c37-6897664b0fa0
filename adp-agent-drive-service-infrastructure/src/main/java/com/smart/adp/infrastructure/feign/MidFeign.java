package com.smart.adp.infrastructure.feign;

import com.smart.adp.domain.common.resp.RespResult;
import com.smart.adp.infrastructure.feign.response.DriverLicenseOCRRsp;
import com.smart.adp.infrastructure.feign.response.IDCardOCRRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description: 中台接口
 * @Author: rik.ren
 * @Date: 2025/6/20 10:10
 **/
@FeignClient(name = "${feign.client.config.mid.url}", url = "${feign.client.config.mid.url}")
public interface MidFeign {

    /**
     * 身份证OCR识别
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/ocr/recognize/id-card", consumes = "application/json")
    RespResult<List<IDCardOCRRsp>> idCardOCR(@RequestBody Object param);

    /**
     * 驾驶证OCR识别
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/ocr/recognize/driving-license", consumes = "application/json")
    RespResult<List<DriverLicenseOCRRsp>> drivingLicenseOCR(@RequestBody Object param);

    /**
     * 姓名身份证号匹配关系
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/identity/idCardInfo", consumes = "application/json")
    RespResult<Boolean> idCardInfo(@RequestBody Object param);


}
