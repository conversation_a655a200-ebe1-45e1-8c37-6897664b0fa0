package com.smart.adp.infrastructure.config.cache;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;

/**
 * <p>
 * cache manager implementation
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/4
 * @see RedisCacheManager
 */
public class SCacheManager extends RedisCacheManager {

    private final RedisCacheWriter writer;

    private final RedisCacheConfiguration cacheConfiguration;

    public SCacheManager(RedisCacheWriter writer, RedisCacheConfiguration cacheConfiguration) {
        super(writer, cacheConfiguration);
        this.writer = writer;
        this.cacheConfiguration = cacheConfiguration;
    }

    @Override
    protected RedisCache createRedisCache(String name, RedisCacheConfiguration cacheConfig) {
        return new SCache(name, writer, cacheConfig != null ? cacheConfig : cacheConfiguration);
    }
}
