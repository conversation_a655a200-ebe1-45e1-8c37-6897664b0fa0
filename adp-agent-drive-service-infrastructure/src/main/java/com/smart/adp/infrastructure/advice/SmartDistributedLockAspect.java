package com.smart.adp.infrastructure.advice;

import com.smart.adp.domain.common.annotation.SmartDistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayDeque;
import java.util.Deque;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 分布式锁增强实现
 * @Author: rik.ren
 * @Date: 2025/6/4 16:38
 **/
@Slf4j
@Aspect
@Component
public class SmartDistributedLockAspect {

    // Redis操作模板
    private final StringRedisTemplate redisTemplate;

    // SpEL表达式解析器
    private final ExpressionParser parser = new SpelExpressionParser();

    // 方法参数名称发现器
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    // Redis锁键前缀
    private static final String LOCK_PREFIX = "LOCK:";

    // 锁值（固定值）
    private static final String LOCK_VALUE = "LOCKED";

    // 使用栈Deque来保存多个锁的key，每个锁的获取都会压入栈中，释放时按顺序弹出，确保每个锁都能正确释放，即使存在嵌套调用。
    // 比如com.smart.adp.application.service.drive.impl.TestDriveSheetServiceImpl.createTestDriveSheet方法和他调用的子方法testDriveAggregate
    // .createTestDriveSheet都有分布式锁的注解，那么如果不适用栈来保存锁的key，那么在释放锁时，可能只会释放子方法的锁，而外部方法的锁无法主动释放
    private static final ThreadLocal<Deque<String>> lockHolder = ThreadLocal.withInitial(() -> new ArrayDeque<>());

    @Autowired
    public SmartDistributedLockAspect(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 分布式锁切面逻辑 - 核心方法
     *
     * @param joinPoint      切入点对象
     * @param lockAnnotation 方法上的分布式锁注解
     * @return 目标方法执行结果
     * @throws Throwable 目标方法抛出的异常
     *                   执行步骤：
     *                   1. 解析锁的key（支持SpEL表达式）
     *                   2. 检查是否可重入（当前线程已持有相同锁）
     *                   3. 根据配置选择阻塞/非阻塞模式获取锁
     *                   4. 获取锁失败则抛出异常
     *                   5. 获取锁成功则执行目标方法
     *                   6. 最终释放锁（确保锁被释放）
     */
    @Around("@annotation(lockAnnotation)")
    public Object around(ProceedingJoinPoint joinPoint, SmartDistributedLock lockAnnotation) throws Throwable {
        // 1. 解析锁的key（使用SpEL表达式）
        String lockKey = LOCK_PREFIX + resolveKey(lockAnnotation.key(), joinPoint); // 提前添加前缀

        log.info("{}：{}", lockAnnotation.logFlag(), lockKey);
        long expire = lockAnnotation.expire();
        TimeUnit timeUnit = lockAnnotation.timeUnit();

        // 2. 检查是否可重入（当前线程已持有相同锁）
        if (isReentrant(lockKey)) {
            // 如果可重入，直接执行目标方法（无需再次获取锁）
            return joinPoint.proceed();
        }

        boolean lockAcquired = false;
        try {
            if (lockAnnotation.block()) {
                log.info("进入阻塞锁模式");
                // 3.1 阻塞模式：在指定时间内循环尝试获取锁
                long waitEnd = System.currentTimeMillis() + lockAnnotation.waitTime() * 1000;
                while (System.currentTimeMillis() < waitEnd) {
                    log.info("准备尝试获取锁");
                    if (tryLock(lockKey, expire, timeUnit)) {
                        lockAcquired = true;
                        // 记录当前线程持有的锁
                        lockHolder.get().push(lockKey);
                        break;
                    }
                    // 等待100ms后重试
                    TimeUnit.MILLISECONDS.sleep(100);
                }
            } else {
                // 3.2 非阻塞模式：尝试获取一次锁
                log.info("进入非阻塞锁模式尝试获取一次锁");
                lockAcquired = tryLock(lockKey, expire, timeUnit);
                if (lockAcquired) {
                    // 记录当前线程持有的锁
                    lockHolder.get().push(lockKey);
                }
            }

            // 4. 获取锁失败处理
            if (!lockAcquired) {
                log.info("获取锁失败：{}", lockKey);
                throw new RuntimeException(lockAnnotation.message());
            }

            // 5. 执行目标方法
            return joinPoint.proceed();
        } finally {
            if (lockAcquired && isReentrant(lockKey)) {
                releaseLock(lockKey);
                // 弹出当前锁标识，恢复外层锁
                Deque<String> lockStack = lockHolder.get();
                lockStack.pop();
                if (lockStack.isEmpty()) {
                    lockHolder.remove();
                }
            }
        }
    }

    /**
     * 尝试获取分布式锁
     *
     * @param key      锁的业务key（不包含前缀）
     * @param expire   锁的过期时间
     * @param timeUnit 时间单位
     * @return 是否成功获取锁
     * 实现原理：
     * 使用Redis的setIfAbsent命令实现原子操作：
     * 1. 如果key不存在则设置值并返回成功
     * 2. 同时设置过期时间防止死锁
     */
    private Boolean tryLock(String key, long expire, TimeUnit timeUnit) {
        // 原子操作：设置键值（仅当键不存在时），并设置过期时间
        Boolean acquired = redisTemplate.opsForValue().setIfAbsent(key, LOCK_VALUE, expire, timeUnit);
        log.info("获取锁 {}，结果：{}", key, acquired);
        return Boolean.TRUE.equals(acquired);
    }

    /**
     * 释放分布式锁
     *
     * @param key 锁的业务key（不包含前缀）
     *            <p>
     *            注意：只删除当前线程持有的锁
     */
    private void releaseLock(String key) {
        log.info("开始释放锁 {}", key);
        // 删除Redis中的锁键
        redisTemplate.delete(key);
        log.info("锁释放成功 {}", key);
    }

    /**
     * 检查是否可重入（当前线程是否已持有相同锁）
     *
     * @param key 要检查的锁key
     * @return 是否可重入
     * <p>
     * 可重入条件：当前线程已持有相同的锁
     */
    private boolean isReentrant(String key) {
        Deque<String> lockStack = lockHolder.get();
        return !lockStack.isEmpty() && lockStack.peek().equals(key);
    }

    /**
     * 解析锁的key（支持SpEL表达式）
     *
     * @param keyExpression 注解中的key表达式
     * @param joinPoint     切入点对象
     * @return 解析后的实际key
     * <p>
     * 实现步骤：
     * 1. 获取目标方法信息
     * 2. 创建SpEL表达式上下文
     * 3. 解析表达式并获取值
     */
    private String resolveKey(String keyExpression, ProceedingJoinPoint joinPoint) {
        if (StringUtils.isNotEmpty(keyExpression)) {
            // 获取目标方法
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            // 获取方法参数
            Object[] args = joinPoint.getArgs();

            // 创建SpEL表达式上下文
            EvaluationContext context = new MethodBasedEvaluationContext(null, method, args, parameterNameDiscoverer);

            // 解析表达式
            Expression expression = parser.parseExpression(keyExpression);

            // 获取表达式值
            return expression.getValue(context, String.class);
        }
        throw new IllegalArgumentException("解析锁key失败");
    }
}