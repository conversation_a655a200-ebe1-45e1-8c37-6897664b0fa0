package com.smart.adp.infrastructure.gateway.clue;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.domain.adapter.IAdapterClueRequestEntity;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.bo.ClueEventFlowBO;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.clue.bo.SacOneCustRemarkBO;
import com.smart.adp.domain.model.clue.gateway.DlrClueGateway;
import com.smart.adp.infrastructure.feign.AgentClueFeign;
import com.smart.adp.infrastructure.feign.response.ClueDlrRsp;
import com.smart.adp.infrastructure.feign.response.ClueDlrSaveRsp;
import com.smart.adp.infrastructure.feign.response.ClueEventFlowRsp;
import com.smart.adp.infrastructure.feign.response.SacOneCustRemarkRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 线索gateway实现
 * @Author: rik.ren
 * @Date: 2025/5/27 10:17
 **/
@Slf4j
@Service
public class DlrClueGatewayImpl implements DlrClueGateway {

    @Autowired
    private AgentClueFeign agentClueFeign;

    @Resource
    private IAdapterClueRequestEntity adapterClueRequest;

    /**
     * 创建线索
     *
     * @param param
     * @param userBusiEntity
     * @return
     */
    @Override
    public SacClueInfoDlrBO saveAgentDlrClue(Object param, UserBusiEntity userBusiEntity) {
        if (ObjectUtil.isEmpty(userBusiEntity)) {
            userBusiEntity = UserInfoContext.get();
        }
        RespBody<ClueDlrSaveRsp> clueSaveResult = agentClueFeign.saveAgentDlrClue(userBusiEntity.getToken(), param);
        if (ObjectUtil.isEmpty(clueSaveResult) || ObjectUtil.isEmpty(clueSaveResult.getBody()) || !RespCode.OK.getCode().equals(clueSaveResult.getCode())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "新建线索失败");
        }
        return BeanUtil.copyProperties(clueSaveResult.getBody(), SacClueInfoDlrBO.class);
    }

    /**
     * 根据手机号查询线索
     *
     * @param custPhone
     * @return
     */
    @Override
    public SacClueInfoDlrBO queryClueInfoDlr(String custPhone) {
        RespBody<ClueDlrRsp> clueInfoResult = agentClueFeign.queryClueInfoDlr(adapterClueRequest.queryClueInfoDlr(custPhone));
        if (ObjectUtil.isEmpty(clueInfoResult) || ObjectUtil.isEmpty(clueInfoResult.getBody()) || !RespCode.OK.getCode().equals(clueInfoResult.getCode())) {
            log.error("创建试乘试驾单获取对应线索失败");
            throw new BusinessException(RespCode.FAIL.getCode(), "获取对应线索失败");
        }
        SacClueInfoDlrBO sacClueInfoDlrBO = BeanUtil.copyProperties(clueInfoResult.getBody(), SacClueInfoDlrBO.class);
        sacClueInfoDlrBO.setInfoChanMCode(clueInfoResult.getBody().getInfoChanMcode());
        sacClueInfoDlrBO.setInfoChanMName(clueInfoResult.getBody().getInfoChanMname());
        sacClueInfoDlrBO.setInfoChanDCode(clueInfoResult.getBody().getInfoChanDcode());
        sacClueInfoDlrBO.setInfoChanDdName(clueInfoResult.getBody().getInfoChanDname());
        return sacClueInfoDlrBO;
    }

    /**
     * 根据手机号查询线索
     *
     * @param custPhone
     * @return
     */
//    @Override
//    public List<SacClueInfoDlrBO> queryListClueInfoDlr(List<String> custPhone, Integer defeatFlag) {
//        RespBody<List<ClueDlrRsp>> clueInfoResult = agentClueFeign.queryClueInfoDlr(adapterClueRequest.queryClueInfoDlr(custPhone,
//                defeatFlag));
//        if (ObjectUtil.isEmpty(clueInfoResult) || ObjectUtil.isEmpty(clueInfoResult.getBody()) || !RespCode.OK.getCode().equals(clueInfoResult.getCode())) {
//            log.error("创建试乘试驾单获取对应线索失败");
//            throw new BusinessException(RespCode.FAIL.getCode(), "获取对应线索失败");
//        }
//        List<SacClueInfoDlrBO> result = BeanUtil.copyToList(clueInfoResult.getBody(), SacClueInfoDlrBO.class);
//        return result;
//    }

    /**
     * 查询用户旅程
     *
     * @param listCustId
     */
    @Override
    public List<ClueEventFlowBO> queryUserEventFlow(List<String> listCustId) {
        RespBody<List<ClueEventFlowRsp>> listRespBody =
                agentClueFeign.queryUserEventFlow(adapterClueRequest.queryClueEventFlowInfo(listCustId));
        if (ObjectUtil.isEmpty(listRespBody) || ObjectUtil.isEmpty(listRespBody.getBody())) {
            return Collections.emptyList();
        }
        return listRespBody.getBody().stream().map(clueEventFlowRsp -> BeanUtil.copyProperties(clueEventFlowRsp, ClueEventFlowBO.class)).collect(Collectors.toList());
    }

    /**
     * 查询线索的扩展信息
     *
     * @param listCustId
     * @return
     */
    @Override
    public List<SacOneCustRemarkBO> queryClueRemark(List<String> listCustId) {
        RespBody<List<SacOneCustRemarkRsp>> listRespBody =
                agentClueFeign.queryClueRemark(adapterClueRequest.queryClueRemark(listCustId));
        if (ObjectUtil.isEmpty(listRespBody) || ObjectUtil.isEmpty(listRespBody.getBody())) {
            return Collections.emptyList();
        }
        return listRespBody.getBody().stream().map(clueRemarkRsp -> BeanUtil.copyProperties(clueRemarkRsp, SacOneCustRemarkBO.class)).collect(Collectors.toList());
    }

    /**
     * 更新意向车型
     *
     * @param custId
     * @param newIntentionCar
     * @param lastTestdriverTime
     */
    @Override
    public String modifyIntentionCar(String custId, String newIntentionCar,
                                     LocalDateTime lastTestdriverTime, UserBusiEntity userBusiEntity) {
        if (ObjectUtil.isEmpty(userBusiEntity)) {
            userBusiEntity = UserInfoContext.get();
        }
        RespBody<String> stringRespBody = agentClueFeign.modifyIntentionCar(userBusiEntity.getToken(),
                adapterClueRequest.modifyIntentionCar(custId, newIntentionCar, lastTestdriverTime));
        if (ObjectUtil.isEmpty(stringRespBody) || ObjectUtil.isEmpty(stringRespBody.getBody()) || !RespCode.OK.getCode().equals(stringRespBody.getCode())) {
            log.error("更新意向车型异常：{}", JSONObject.toJSONString(stringRespBody));
            throw new BusinessException(RespCode.FAIL.getCode(), "更新意向车型失败");
        }
        log.info("更新意向车型结果：{}", JSONObject.toJSONString(stringRespBody));
        return stringRespBody.getBody();
    }
}
