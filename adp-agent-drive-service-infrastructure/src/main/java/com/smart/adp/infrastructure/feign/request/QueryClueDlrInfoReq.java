package com.smart.adp.infrastructure.feign.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 线索查询DTO
 * @Author: rik.ren
 * @Date: 2025/4/23 13:26
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "线索查询入参")
public class QueryClueDlrInfoReq {

    /**
     * 线索 ID
     */
    @Schema(description = "线索手机号", required = false)
    private String phone;

    /**
     * 线索手机号集合
     */
    @Schema(description = "线索手机号", required = false)
    private List<String> listPhone;

    /**
     * 线索标签
     *
     * @see com.smart.adp.domain.enums.DefeatFlagEnum
     */
    @Schema(description = "战败标签，0非战败，1全部")
    private Integer defeatFlag = 0;

}
