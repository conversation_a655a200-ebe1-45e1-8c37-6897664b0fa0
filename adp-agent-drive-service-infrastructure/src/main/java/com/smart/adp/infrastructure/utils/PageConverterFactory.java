package com.smart.adp.infrastructure.utils;

import com.smart.adp.domain.common.DomainPage;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 将 MyBatis Flex 的 Page 转换为 DomainPage
 * @Author: rik.ren
 * @Date: 2025/3/28 09:46
 **/
public final class PageConverterFactory {

    // 禁止实例化
    private PageConverterFactory() {
    }

    /**
     * 将 MyBatis Flex 的 Page 转换为 DomainPage
     *
     * @param flexPage        ORM 框架的分页对象
     * @param recordConverter 可选的记录转换器（用于领域对象转换）
     */
    public static <T, R> DomainPage<R> toDomainPage(
            com.mybatisflex.core.paginate.Page<T> flexPage,
            Function<T, R> recordConverter
    ) {
        List<R> convertedRecords = flexPage.getRecords().stream()
                .map(recordConverter)
                .collect(Collectors.toList());

        return new DomainPage<>(
                convertedRecords,
                (int) flexPage.getPageNumber(),
                (int) flexPage.getPageSize(),
                flexPage.getTotalRow()
        );
    }

    /**
     * 快速转换（当记录已经是领域对象时）
     */
//    public static <T> DomainPage<T> toDomainPage(com.mybatisflex.core.paginate.Page<T> flexPage) {
//        return toDomainPage(flexPage, Function.identity());
//    }

    /**
     * 直接转换记录类型（无需字段映射）
     */
    public static <T> DomainPage<T> toDomainPage(com.mybatisflex.core.paginate.Page<T> flexPage) {
        return new DomainPage<>(
                flexPage.getRecords(),
                (int) flexPage.getPageNumber(),
                (int) flexPage.getPageSize(),
                flexPage.getTotalRow()
        );
    }
}