package com.smart.adp.infrastructure.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.infrastructure.advice.SelfLogicForkJoinFunction;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.RecursiveTask;

/**
 * @Description: forkJoin的工具类，T1是入参类型，T2是出参类型
 * @Author: rik.ren
 * @Date: 2025/03/28 13:54
 **/
public class ForkJoinUtil<T1, T2> extends RecursiveTask<List<T2>> {
    private Object[] params;
    /**
     * 待处理的集合
     */
    private List<T1> pendList;
    /**
     * 拆分后需要执行的方法
     */
    private SelfLogicForkJoinFunction<T1, T2> function;
    /**
     * 默认拆分一个任务1000个元素，可以自定义
     */
    private Integer maxSize = 1000;

    /**
     * forkJoin的入口
     *
     * @param pendList 待处理集合
     * @param maxSize  自定义单个线程处理任务的集合大小
     * @param function 拆分集合后需要执行的任务
     * @param params   后置任务需要的入参
     */
    public ForkJoinUtil(List<T1> pendList, Integer maxSize, SelfLogicForkJoinFunction<T1, T2> function, Object... params) {
        this.pendList = pendList;
        if (ObjectUtil.isNotEmpty(maxSize)) {
            this.maxSize = maxSize;
        }
        this.function = function;
        this.params = params;
    }

    @Override
    protected List<T2> compute() {
        List<T2> result = new ArrayList<>();
        if (pendList.size() <= this.maxSize) {
            result = function.selfLogicForkJoinFunction(pendList, params);
        } else {
            int mid = pendList.size() / 2;
            ForkJoinUtil<T1, T2> leftTask = new ForkJoinUtil<T1, T2>(pendList.subList(0, mid), this.maxSize, function, params);
            ForkJoinUtil<T1, T2> rightTask = new ForkJoinUtil<T1, T2>(pendList.subList(mid, pendList.size()), this.maxSize,
                    function, params);
            leftTask.fork();
            rightTask.fork();
            if (ObjectUtil.isNotNull(result)) {
                List<T2> joinLeft = leftTask.join();
                List<T2> joinRight = rightTask.join();
                if (CollUtil.isNotEmpty(joinLeft)) {
                    result.addAll(joinLeft);
                }
                if (CollUtil.isNotEmpty(joinRight)) {
                    result.addAll(joinRight);
                }
            }
        }
        return result;
    }

}