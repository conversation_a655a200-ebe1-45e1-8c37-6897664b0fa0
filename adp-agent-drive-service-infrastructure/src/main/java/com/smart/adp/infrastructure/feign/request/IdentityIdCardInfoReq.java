package com.smart.adp.infrastructure.feign.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 姓名和身份证号匹配关系校验入参
 * @Author: rik.ren
 * @Date: 2025/6/24 16:24
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IdentityIdCardInfoReq {
    /**
     * 姓名
     */
    private String idCardName;
    /**
     * 身份证号
     */
    private String idCardNo;

}
