<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smart.adp.infrastructure.repository.drive.TestCarPrepareEntityMapper">

    <delete id="deleteByPhone">
        DELETE FROM
            csc.t_sac_clue_info_dlr
        WHERE
            PHONE = #{phone}
    </delete>

    <update id="updatePrepareForStop" parameterType="com.smart.adp.domain.model.drive.entity.TestCarPrepareEntity">
        UPDATE `mp`.`t_usc_bu_testcar_prepare`
        SET `MODIFIER`          = #{param.modifier},
            `LAST_UPDATED_DATE` = #{param.lastUpdatedDate},
            `TESTCAR_FREQUENCY` = TESTCAR_FREQUENCY + 1,
            `CAR_STATUS_CODE`   = #{param.carStatusCode},
            `CAR_STATUS_NAME` = #{param.carStatusName}
        WHERE `mp`.`t_usc_bu_testcar_prepare`.`CAR_LICENCE_NO` = #{param.carLicenceNo}
          AND `IS_ENABLE` = '1'
    </update>
</mapper>