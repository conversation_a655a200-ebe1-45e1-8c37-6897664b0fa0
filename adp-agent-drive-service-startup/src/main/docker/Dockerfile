FROM swr.cn-east-3.myhuaweicloud.com/smart-basic/skywalking-java-agent:8.5.0-jdk8_mss

RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

RUN echo "ip_resolve=4" >> /etc/yum.conf

RUN mkdir -p /adp

ENV APPLICATION_NAME adp-agent-drive-service.jar

ADD ./adp-agent-drive-service-startup/target/$APPLICATION_NAME /adp/

COPY ./adp-agent-drive-service-startup/src/main/docker/run.sh /adp/
# copy arthas
COPY --from=swr.cn-east-3.myhuaweicloud.com/adp/arthas:v1 /opt/arthas /adp/arthas

# CMD ["sh", "-c", "cd /adp/; sh run.sh $APPLICATION_NAME"]
ENTRYPOINT ["sh", "-c", "cd /adp/; sh run.sh $APPLICATION_NAME"]  
