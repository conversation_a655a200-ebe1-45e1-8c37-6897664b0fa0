feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 100000 # 连接超时时间
        readTimeout: 300000   # 读超时时间
        loggerLevel: full # 日志级别
      adp-agent-clue:
        url: http://*************:28083
#        url: localhost:8086
        connectTimeout: 10000 # 连接超时时间
        readTimeout: 30000   # 读超时时间
        loggerLevel: full # 日志级别
      adp-base-service:
        url: http://*************:9092
        connectTimeout: 10000 # 连接超时时间
        readTimeout: 30000   # 读超时时间
        loggerLevel: full # 日志级别
      adp-xapi-api-service:
        url: http://*************:8081
        connectTimeout: 100000 # 连接超时时间
        readTimeout: 300000   # 读超时时间
        loggerLevel: full # 日志级别
      tda:
        url: https://tda-uat.smart.cn
        connectTimeout: 100000 # 连接超时时间
        readTimeout: 300000   # 读超时时间
        loggerLevel: full # 日志级别
      mid:
        url: http://mss-infrastructure-service.mss-mid-sit.svc:28006
        connectTimeout: 100000 # 连接超时时间
        readTimeout: 300000   # 读超时时间
        loggerLevel: full # 日志级别
      csc-service:
        url: http://10.170.223.77:9080
        connectTimeout: 100000 # 连接超时时间
        readTimeout: 300000   # 读超时时间
        loggerLevel: full # 日志级别
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 20000

logging:
  level:
    org.springframework: info
    com.smart.cnec.mall.campaign.mapper: debug
    com.smart.cnec.mall.campaign.feign: debug
    com.smart.adp.infrastructure: debug
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    url: ****************************************************************************************************************************************************************************************************************************************
    username: appuser
    password: app@user!!
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 20
      max-wait: 10000
      validation-query: SELECT 1
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      # stat
      filters: stat
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: 123
  elasticsearch:
    rest:
      username: admin
      password: wM5kME6%dTx4
      uris: **************,**************,*************
      port: 9200
      read-timeout: 60s
      connection-timeout: 30s
  data:
    elasticsearch:
      repositories:
        enabled: true
  pool:
    corePoolSize: 2
    maxPoolSize: 10
    queueCapacity: 100000

  redis:
    host: redis-f9d94014-98f5-4f8c-a0fc-1b52383c9d86.cn-east-3.dcs.myhuaweicloud.com
    port: 6379
    timeout: 20000
    password: q@BBGmFQdk6Vp
  rabbitmq:
    host: **************
    port: 5672
    virtual-host: /adp/public
    username: adp
    password: adp@smart2021
    connection-timeout: 5000
    publisher-confirm-type: correlated  # 开启发送确认
    publisher-returns: true             # 开启路由失败回调
    template:
      mandatory: true                   # 必须设置才能触发returns回调
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000
rest:
  connection:
    connectTimeout: 1000
    connectionRequestTimeout: 200
    socketTimeout: 1500

mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true  # 启用下划线到驼峰的自动转换
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl # 可选，开启SQL日志

xxl.job:
  admin:
    addresses: http://tradein-api-uat.smart.cn/trade-in-job-admin
    accessToken: default_token
  executor:
    appname: adp-agent-drive
    address:
    ip:
    port: 9999
    logpath: /idea-project/smart-displace/smart-displace-job/jobhandler
    logretentiondays: 30

thread-pool:
  coreSizeMultiple: 8
  maxSizeMultiple: 16
  queueCapacity: 5000

system.meta.esIndex: uat

smartRetry:
  # 组织信息
  source: adp-drive
  # 运行环境
  profile: uat

#业务配置
businessConfig:
  # 开关
  operateSwitch:
    # 试乘试驾短信发送开关
    testDriveSMS: false
    # 匹配姓名和身份证号的接口
    matchNameAndID: true
  testDriveAgreement:
    # 试乘试驾用户协议模板路径
    testDriveAgreementTemplateUrl: testDriveAgreementFile/TemplateForTestDriveNotification.docx
    # 试乘试驾用户签署协议PDF文件上传路径
    testDriveAgreementOutputDir: testDriveAgreementFile-contracts
  aes:
    secretKey: -QuanShiJieWuChanZheLianHeQiLai-


#华为OBS配置
huawei:
  obs:
    endpoint: obs.cn-east-3.myhuaweicloud.com
    ak: EYBUDYZG76Y2GHBYDYCI
    sk: 2KKn8j2mQKoZEGTKNVHc3Pgg7kwbHRsgieyvWmGG
    bucket: obs-adp-uat
    customDomainName: https://adp-uat.smart.cn/