package com.smart.adp.domain.common.validation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 在抽象类中定义结果对象
 * @Author: rik.ren
 * @Date: 2025/5/20 16:38
 **/
public class ValidationResult {
    private boolean valid;
    private List<ErrorDetail> errors = new ArrayList<>();

    // 添加错误方法
    public void addError(String code, String message) {
        this.errors.add(new ErrorDetail(code, message));
        this.valid = false;
    }

    // 获取结果
    public boolean isValid() {
        return valid;
    }

    public static ValidationResult success() {
        ValidationResult result = new ValidationResult();
        result.valid = true;
        return result;
    }

    public static ValidationResult failure(ErrorDetail... errors) {
        ValidationResult result = new ValidationResult();
        result.valid = false;
        Collections.addAll(result.errors, errors);
        return result;
    }

    // 获取错误列表
    public List<ErrorDetail> getErrors() {
        return Collections.unmodifiableList(errors);
    }

    public void addError(ErrorDetail error) {
        this.errors.add(error);
        this.valid = false;
    }

    public void addErrors(List<ErrorDetail> errors) {
        this.errors.addAll(errors);
        this.valid = false;
    }
}
