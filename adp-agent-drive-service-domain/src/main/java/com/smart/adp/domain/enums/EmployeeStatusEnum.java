package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum EmployeeStatusEnum {

    WORKING("1", "在职"),

    RESIGN("2", "离职");

    private final String code;

    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static EmployeeStatusEnum getByCode(String code) {
        return Arrays.stream(EmployeeStatusEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(null);
    }
}
