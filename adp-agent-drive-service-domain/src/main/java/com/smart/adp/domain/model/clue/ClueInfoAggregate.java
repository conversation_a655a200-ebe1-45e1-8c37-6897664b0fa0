package com.smart.adp.domain.model.clue;

import cn.hutool.core.collection.CollectionUtil;
import com.smart.adp.domain.adapter.IAdapterClueRequestEntity;
import com.smart.adp.domain.enums.DefeatFlagEnum;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.bo.ClueEventFlowBO;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.clue.bo.SacOneCustRemarkBO;
import com.smart.adp.domain.model.clue.gateway.DlrClueGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 线索的聚合根
 * @Author: rik.ren
 * @Date: 2025/5/24 11:54
 **/
@Slf4j
@Service
public class ClueInfoAggregate {

    @Autowired
    private DlrClueGateway dlrClueGateway;
    @Resource
    private IAdapterClueRequestEntity adapterClueRequest;

    /**
     * 根据手机号查询线索
     *
     * @param custPhone
     * @return
     */
    public SacClueInfoDlrBO queryClueInfoDlr(String custPhone) {
        if (StringUtils.isEmpty(custPhone)) {
            return null;
        }
        SacClueInfoDlrBO clueDlrVO = dlrClueGateway.queryClueInfoDlr(custPhone);
        return clueDlrVO;
    }

    /**
     * 根据手机号集合查询线索
     *
     * @param listCustPhone
     * @return
     */
//    public List<SacClueInfoDlrBO> queryListClueInfoDlr(List<String> listCustPhone) {
//        if (CollectionUtil.isEmpty(listCustPhone)) {
//            return null;
//        }
//        List<SacClueInfoDlrBO> clueDlrVO = dlrClueGateway.queryListClueInfoDlr(listCustPhone, DefeatFlagEnum.DEFEAT.getCode());
//        return clueDlrVO;
//    }

    /**
     * 创建线索
     *
     * @param param
     * @return
     */
    public SacClueInfoDlrBO saveAgentDlrClue(Object param, UserBusiEntity userBusiEntity) {
        SacClueInfoDlrBO sacClueInfoDlrBO = dlrClueGateway.saveAgentDlrClue(param, userBusiEntity);
        return sacClueInfoDlrBO;
    }

    /**
     * 查询线索的时间节点
     *
     * @param listCustId
     * @return
     */
    public List<ClueEventFlowBO> queryUserEventFlow(List<String> listCustId) {
        if (CollectionUtil.isEmpty(listCustId)) {
            return null;
        }
        List<ClueEventFlowBO> clueEventFlowBOS = dlrClueGateway.queryUserEventFlow(listCustId);
        return clueEventFlowBOS;
    }

    /**
     * 查询线索的扩展信息
     *
     * @param listCustId
     * @return
     */
    public List<SacOneCustRemarkBO> queryClueRemark(List<String> listCustId) {
        if (CollectionUtil.isEmpty(listCustId)) {
            return null;
        }
        List<SacOneCustRemarkBO> sacOneCustRemarkBOS = dlrClueGateway.queryClueRemark(listCustId);
        return sacOneCustRemarkBOS;
    }

    /**
     * 更新新的意向车型
     *
     * @param custId
     * @param newIntentionCar
     * @return
     */
    public String modifyIntentionCar(String custId, String newIntentionCar, UserBusiEntity userBusiEntity) {
        String newIntentionCarResult = dlrClueGateway.modifyIntentionCar(custId, newIntentionCar, LocalDateTime.now(), userBusiEntity);
        return newIntentionCarResult;
    }
}
