package com.smart.adp.domain.model.drive.validation.createTestDrive;

import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeParseException;

/**
 * @Description: 创建预约单检查预约时间是否合法
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Service
@Slf4j
public class AppointmentSheetCheckTimeValidation extends AbstractValidationHandler<SacTestDriveSheetBO> {

    @Override
    protected void doValidate(SacTestDriveSheetBO context) throws BusicenException {
        // 检查试驾类型是否为不允许的类型
        if (TestDriveTypeEnum.TEST_RIDE.getCode().equals(context.getTestType())
                || TestDriveTypeEnum.TEST_DRIVE.getCode().equals(context.getTestType())) {
            // 校验普通试驾
            checkNormalTest(context);
        } else if (TestDriveTypeEnum.DEEP_TEST.getCode().equals(context.getTestType())) {
            // 校验深度试驾
            checkDeepTest(context);
        }
    }

    /**
     * 普通试驾时间校验
     */
    private void checkNormalTest(SacTestDriveSheetBO context) {
        // 1. 验证预约日期不能早于今天
        validateDateNotBeforeToday(
                context.getAppointmentTestDate(),
                "请选择一个新的日期，预约日期只能大于等于今天日期"
        );

        // 2. 如果不是今天，直接通过
        LocalDate today = LocalDate.now();
        LocalDate appointmentDate = LocalDate.parse(context.getAppointmentTestDate(), TimeConstant.TIME_D_FORMATTER);

        if (!appointmentDate.equals(today)) {
            return;
        }

        // 3. 如果是今天，验证时间段规则
        boolean canBookCurrentSlot = !"0".equals(context.getConfigValueSwitch());
        validateTodayTimeSlot(
                context.getAppointmentTestTime(),
                canBookCurrentSlot
        );

    }

    /**
     * 深度试驾时间校验
     */
    private void checkDeepTest(SacTestDriveSheetBO context) {
        validateStartEndTime(
                context.getAppointmentStartTime(),
                context.getAppointmentEndTime()
        );
    }

    /**
     * 验证日期不能早于今天
     */
    private void validateDateNotBeforeToday(String dateStr, String errorMessage) {
        try {
            LocalDate appointmentDate = LocalDate.parse(dateStr, TimeConstant.TIME_D_FORMATTER);
            LocalDate today = LocalDate.now();

            if (appointmentDate.isBefore(today)) {
                throw new BusicenException(errorMessage);
            }
        } catch (DateTimeParseException e) {
            throw new BusicenException("日期格式错误: " + dateStr);
        }
    }

    /**
     * 验证开始时间和结束时间的合理性
     */
    private void validateStartEndTime(String startTimeStr, String endTimeStr) {
        try {
            LocalDateTime startTime = LocalDateTime.parse(startTimeStr, TimeConstant.DEFAULT_FORMATTER);
            LocalDateTime endTime = LocalDateTime.parse(endTimeStr, TimeConstant.DEFAULT_FORMATTER);
            LocalDateTime now = LocalDateTime.now();

            if (startTime.isBefore(now)) {
                throw new BusicenException("请选择合法的时间，开始时间与结束时间均不能小于当前时间");
            }

            if (!startTime.isBefore(endTime)) {
                throw new BusicenException("开始时间不能大于结束时间");
            }
        } catch (DateTimeParseException e) {
            throw new BusicenException("时间格式错误");
        }
    }

    /**
     * 验证当日时间段预约规则
     */
    private void validateTodayTimeSlot(String timeSlot, boolean canBookCurrentSlot) {
        try {
            String[] times = timeSlot.split("-");
            if (times.length != 2) {
                throw new BusicenException("时间段格式错误");
            }

            LocalTime now = LocalTime.now();
            LocalTime startTime = LocalTime.parse(times[0] + ":00", TimeConstant.H_M_S_FORMATTER);
            LocalTime endTime = LocalTime.parse(times[1] + ":00", TimeConstant.H_M_S_FORMATTER);

            // 当前时间不能大于等于时间段的结束时间
            if (!now.isBefore(endTime)) {
                throw new BusicenException("预约时间段的结束时间不能小于当前时间");
            }

            // 如果不允许预约当前时间段，当前时间不能在时间段内
            if (!canBookCurrentSlot && !now.isBefore(startTime)) {
                throw new BusicenException("不能预约当前时间段");
            }
        } catch (DateTimeParseException e) {
            throw new BusicenException("时间格式错误: " + timeSlot);
        }
    }

}