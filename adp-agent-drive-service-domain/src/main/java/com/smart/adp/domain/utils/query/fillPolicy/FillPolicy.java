package com.smart.adp.domain.utils.query.fillPolicy;

import java.util.function.Function;

/**
 * <p>
 * 填充策略
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/30
 */
public interface FillPolicy<K, V> {

    /**
     * 是否需要填充
     *
     * @return boolean
     */
    boolean shouldFill();

    /**
     * 是否异步
     *
     * @return boolean
     */
    boolean async();

    /**
     * null 是否填充
     *
     * @return boolean
     */
    boolean nullFill();

    /**
     * null 填充值 func
     *
     * @return func
     */
    Function<K, V> nullValueFunc();
}
