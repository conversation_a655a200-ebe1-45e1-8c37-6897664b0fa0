package com.smart.adp.domain.model.drive.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.TestDriveReviewRecordBO;
import com.smart.adp.domain.model.drive.entity.SacTestDriveReviewRecordEntity;

/**
 * @Description: 试乘试驾跟进记录gateway
 * @Author: rik.ren
 * @Date: 2025/5/27 17:46
 **/
public interface SacTestDriveReviewRecordGateway {
    /**
     * 查询试乘试驾跟进记录
     *
     * @param param
     * @return
     */
    SacTestDriveReviewRecordEntity queryTestDriveReviewRecord(TestDriveReviewRecordBO param, QueryColumn... needColumn);
}
