package com.smart.adp.domain.common.helper;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.enums.DataSourceType;

public class TypedDomainPage<T> extends DomainPage<T> implements ResultTypeAware {
    private final DataSourceType sourceType;

    public TypedDomainPage(DomainPage<T> page, DataSourceType sourceType) {
        super(ObjectUtil.isEmpty(page) ? null : page.getRecords(), ObjectUtil.isEmpty(page) ? 1 : page.getPageNumber(),
                ObjectUtil.isEmpty(page) ? 10 : page.getPageSize(), ObjectUtil.isEmpty(page) ? 0 : page.getTotalCount());
        this.sourceType = sourceType;
    }
    @Override
    public DataSourceType getSourceType() {
        return sourceType;
    }
}