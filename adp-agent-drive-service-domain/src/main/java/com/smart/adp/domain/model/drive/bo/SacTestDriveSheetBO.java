package com.smart.adp.domain.model.drive.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.validation.ValidationHandler;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.helper.StringHelper;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.entity.SacTestDriveSheetEntity;
import com.smart.adp.domain.model.drive.validation.createTestDrive.CreateTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.modifyTestDrive.ModifyTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.startTestDrive.StartTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.stopTestDrive.StopTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.testSheetDataRePush.RePushTestDriveSheetValidationChain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * 试乘试驾单表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Slf4j
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SacTestDriveSheetBO extends SacTestDriveSheetEntity {

    /**
     * 分页属性
     */
    private Integer pageNumber;

    private Integer pageSize;

    /**
     * 开始时间
     */
    private LocalDateTime dateBegin;
    /**
     * 结束时间
     */
    private LocalDateTime dateEnd;

    /**
     * 二级渠道
     */
    private String infoChanMCode;

    /**
     * 三级渠道
     */
    private String infoChanDCode;

    /**
     * 搜索内容
     */
    private String searchCondition;

    /**
     * 预约试乘试驾日期(普通试乘试驾)
     */
    private String appointmentTestDate;

    /**
     * 预约试乘试驾时间段(普通试乘试驾)
     */
    private String appointmentTestTime;

    /**
     * 预约超长试驾开始时间
     */
    private String appointmentStartTime;

    /**
     * 预约超长试驾结束时间
     */
    private String appointmentEndTime;

    /**
     * 试驾预约信息
     */
    private List<TestCarPrepareBO> listTestCarPrepare;

    /**
     * 系统配置信息的值
     */
    private String configValueSwitch;

    /**
     * 系统配置信息的值
     */
    private String configValueDriveNum;

    /**
     * 线索信息
     */
    private String clueInfo;

    /**
     * 更新标记，true是更新，false是新建
     */
    private Boolean updateFlag;

    /**
     * 预计开始日期
     */
    private String expectStartDate;

    /**
     * 是否需要发送试驾消息
     */
    private String isSendMessage;
    /**
     * 性别
     */
    private String sex;

    /**
     * 性别名称
     */
    private String CustomerSexName;
    /**
     * 热度
     */
    private String businessHeatCode;

    /**
     * 热度
     */
    private String businessHeatName;

    /**
     * 试驾状态集合
     */
    private List<String> listTestStatus;

    /**
     * 线索是否存在的标签
     */
    private Boolean clueExistFlag = Boolean.FALSE;

    /**
     * 是否需要预约信息
     */
    private Boolean needAppointmentSheet = Boolean.TRUE;

    /**
     * 试驾时长
     */
    private String duration;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 试驾小结
     */
    private String testDriveEndRemark;

    public SacTestDriveSheetBO(Boolean needAppointmentSheet) {
        this.needAppointmentSheet = needAppointmentSheet;
    }

    public SacTestDriveSheetBO(String testDriveSheetIdParam, Boolean needAppointmentSheet) {
        super(testDriveSheetIdParam);
        this.needAppointmentSheet = needAppointmentSheet;
    }

    /**
     * 对象转换
     *
     * @param entity
     * @return
     */
    public static SacTestDriveSheetBO conventBO(SacTestDriveSheetBO entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        SacTestDriveSheetBO sacTestDriveSheetBO = new SacTestDriveSheetBO();
        sacTestDriveSheetBO.setTestDriveSheetId(entity.getTestDriveSheetId());
        sacTestDriveSheetBO.setTestDriveOrderNo(entity.getTestDriveOrderNo());
        sacTestDriveSheetBO.setAppointmentId(entity.getAppointmentId());
        sacTestDriveSheetBO.setTestStatus(entity.getTestStatus());
        sacTestDriveSheetBO.setRecordId(entity.getRecordId());
        sacTestDriveSheetBO.setDlrCode(entity.getDlrCode());
        sacTestDriveSheetBO.setDlrName(entity.getDlrName());
        sacTestDriveSheetBO.setSalesConsultantName(entity.getSalesConsultantName());
        sacTestDriveSheetBO.setSalesConsultantId(entity.getSalesConsultantId());
        sacTestDriveSheetBO.setIntenLevelCode(entity.getIntenLevelCode());
        sacTestDriveSheetBO.setIntenLevelName(entity.getIntenLevelName());
        sacTestDriveSheetBO.setDriverCustomerRelation(entity.getDriverCustomerRelation());
        sacTestDriveSheetBO.setDriverName(entity.getDriverName());
        sacTestDriveSheetBO.setDriverPhone(entity.getDriverPhone());
        sacTestDriveSheetBO.setDrivingLicenceType(entity.getDrivingLicenceType());
        sacTestDriveSheetBO.setDrivingLicenceNumber(entity.getDrivingLicenceNumber());
        sacTestDriveSheetBO.setAddress(entity.getAddress());
        sacTestDriveSheetBO.setDrivingLicencePhoto(entity.getDrivingLicencePhoto());
        sacTestDriveSheetBO.setTestRoadHaul(entity.getTestRoadHaul());
        sacTestDriveSheetBO.setTestStartRoadHaul(entity.getTestStartRoadHaul());
        sacTestDriveSheetBO.setTestEndRoadHaul(entity.getTestEndRoadHaul());
        sacTestDriveSheetBO.setDlrClueOrderNo(entity.getDlrClueOrderNo());
        sacTestDriveSheetBO.setCustomerName(entity.getCustomerName());
        sacTestDriveSheetBO.setCustomerId(entity.getCustomerId());
        sacTestDriveSheetBO.setCustomerPhone(entity.getCustomerPhone());
        sacTestDriveSheetBO.setCustomerSex(entity.getCustomerSex());
        sacTestDriveSheetBO.setSmallCarTypeCode(entity.getSmallCarTypeCode());
        sacTestDriveSheetBO.setSmallCarTypeName(entity.getSmallCarTypeName());
        sacTestDriveSheetBO.setPlateNumber(entity.getPlateNumber());
        sacTestDriveSheetBO.setCarVin(entity.getCarVin());
        sacTestDriveSheetBO.setTestType(entity.getTestType());
        sacTestDriveSheetBO.setAppointmentChannel(entity.getAppointmentChannel());
        sacTestDriveSheetBO.setStartTime(entity.getStartTime());
        sacTestDriveSheetBO.setEndTime(entity.getEndTime());
        sacTestDriveSheetBO.setTestDriveAgreement(entity.getTestDriveAgreement());
        sacTestDriveSheetBO.setCustomerIdNumberAgreement(entity.getCustomerIdNumberAgreement());
        sacTestDriveSheetBO.setCustomerSignatureAgreement(entity.getCustomerSignatureAgreement());
        sacTestDriveSheetBO.setDeposit(entity.getDeposit());
        sacTestDriveSheetBO.setOldTestDriveSheetId(entity.getOldTestDriveSheetId());
        sacTestDriveSheetBO.setReceiver(entity.getReceiver());
        sacTestDriveSheetBO.setReceiverCode(entity.getReceiverCode());
        sacTestDriveSheetBO.setReceiverTime(entity.getReceiverTime());
        sacTestDriveSheetBO.setOemId(entity.getOemId());
        sacTestDriveSheetBO.setGroupId(entity.getGroupId());
        sacTestDriveSheetBO.setCreator(entity.getCreator());
        sacTestDriveSheetBO.setCreatedName(entity.getCreatedName());
        sacTestDriveSheetBO.setCreatedDate(entity.getCreatedDate());
        sacTestDriveSheetBO.setModifier(entity.getModifier());
        sacTestDriveSheetBO.setModifyName(entity.getModifyName());
        sacTestDriveSheetBO.setLastUpdatedDate(entity.getLastUpdatedDate());
        sacTestDriveSheetBO.setUpdateControlId(entity.getUpdateControlId());
        sacTestDriveSheetBO.setIsEnable(entity.getIsEnable());
        sacTestDriveSheetBO.setColumn1(entity.getColumn1());
        sacTestDriveSheetBO.setColumn2(entity.getColumn2());
        sacTestDriveSheetBO.setColumn3(entity.getColumn3());
        sacTestDriveSheetBO.setColumn4(entity.getColumn4());
        sacTestDriveSheetBO.setColumn5(entity.getColumn5());
        sacTestDriveSheetBO.setColumn6(entity.getColumn6());
        sacTestDriveSheetBO.setColumn7(entity.getColumn7());
        sacTestDriveSheetBO.setColumn8(entity.getColumn8());
        sacTestDriveSheetBO.setColumn9(entity.getColumn9());
        sacTestDriveSheetBO.setColumn10(entity.getColumn10());
        sacTestDriveSheetBO.setIsCanChange(entity.getIsCanChange());
        sacTestDriveSheetBO.setOtherAgreement(entity.getOtherAgreement());
        sacTestDriveSheetBO.setImportFlag(entity.getImportFlag());
        sacTestDriveSheetBO.setRepeatFlag(entity.getRepeatFlag());
        sacTestDriveSheetBO.setEvaluateFlag(entity.getEvaluateFlag());
        sacTestDriveSheetBO.setTestDriveMethod(entity.getTestDriveMethod());
        sacTestDriveSheetBO.setAppointmentTestDate(entity.getAppointmentTestDate());
        sacTestDriveSheetBO.setAppointmentTestTime(entity.getAppointmentTestTime());
        sacTestDriveSheetBO.setAppointmentStartTime(entity.getAppointmentStartTime());
        sacTestDriveSheetBO.setAppointmentEndTime(entity.getAppointmentEndTime());
        return sacTestDriveSheetBO;
    }

    /**
     * 构建创建试驾单时，预约试乘试驾单的对象bo
     *
     * @return
     */
    public SacAppointmentSheetBO buildCreateAppointBo() {
        SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
        appointmentSheetBO.setAppointmentId(StringHelper.GetGUID());
        appointmentSheetBO.setIsTestDrive("0");
        appointmentSheetBO.setDlrCode(UserInfoContext.get().getDlrCode());
        appointmentSheetBO.setDlrName(UserInfoContext.get().getDlrName());
        appointmentSheetBO.setDlrClueOrderNo(getDlrClueOrderNo());
        appointmentSheetBO.setCustomerName(getCustomerName());
        appointmentSheetBO.setCustomerId(getCustomerId());
        appointmentSheetBO.setCustomerPhone(getCustomerPhone());
        appointmentSheetBO.setCustomerSex(getCustomerSex());
        appointmentSheetBO.setAppointmentOrderNo("单号生成器");
        appointmentSheetBO.setAppointmentTime(TimeConstant.DEFAULT_FORMATTER.format(LocalDateTime.now()));
        appointmentSheetBO.setSmallCarTypeCode(getSmallCarTypeCode());
        appointmentSheetBO.setSmallCarTypeName(getSmallCarTypeName());
        appointmentSheetBO.setPlateNumber(getPlateNumber());
        appointmentSheetBO.setTestType(getTestType());
        appointmentSheetBO.setAppointmentTestDate(getAppointmentTestDate());
        appointmentSheetBO.setAppointmentTestTime(getAppointmentTestTime());
        appointmentSheetBO.setAppointmentStartTime(getAppointmentStartTime());
        appointmentSheetBO.setAppointmentEndTime(getAppointmentEndTime());
        appointmentSheetBO.setAppointmentChannel(getAppointmentChannel());
        appointmentSheetBO.setOemId(UserInfoContext.get().getOemID());
        appointmentSheetBO.setGroupId(UserInfoContext.get().getGroupID());
        appointmentSheetBO.setCreator(UserInfoContext.get().getUserID());
        appointmentSheetBO.setCreatedName(UserInfoContext.get().getEmpName());
        appointmentSheetBO.setCreatedDate(LocalDateTime.now());
        appointmentSheetBO.setModifier(appointmentSheetBO.getCreator());
        appointmentSheetBO.setModifyName(appointmentSheetBO.getCreatedName());
        appointmentSheetBO.setLastUpdatedDate(appointmentSheetBO.getCreatedDate());
        appointmentSheetBO.setUpdateControlId(StringHelper.GetGUID());
        appointmentSheetBO.setIsEnable("1");
        appointmentSheetBO.setColumn1(getColumn1());
        appointmentSheetBO.setColumn2(getColumn2());
        appointmentSheetBO.setColumn3(getColumn3());
        appointmentSheetBO.setColumn4(getColumn4());
        appointmentSheetBO.setColumn5(getColumn5());
        appointmentSheetBO.setColumn6(getColumn6());
        appointmentSheetBO.setColumn7(getColumn7());
        appointmentSheetBO.setColumn8(getColumn8());
        appointmentSheetBO.setColumn9(getColumn9());
        appointmentSheetBO.setColumn10(getColumn10());
        appointmentSheetBO.setCarVin(getCarVin());
        appointmentSheetBO.setExpectStartDate(getExpectStartDate());
        return appointmentSheetBO;
    }

    public static List<SacTestDriveSheetBO> conventBO(List<SacTestDriveSheetBO> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(entity -> {
                    return conventBO(entity);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacTestDriveSheetEntity buildTestDriveCondition(QueryWrapper wrapper) {
        wrapper.and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID.eq(getTestDriveSheetId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_ORDER_NO.eq(getTestDriveOrderNo(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID.eq(getAppointmentId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS.eq(getTestStatus(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS.in(getListTestStatus(), CollectionUtil::isNotEmpty))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.RECORD_ID.eq(getRecordId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CLUE_ORDER_NO.eq(getDlrClueOrderNo(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID.eq(getCustomerId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_NAME.like(getSearchCondition(), StringUtil::hasText).or(
                        SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_PHONE.like(getSearchCondition(), StringUtil::hasText)
                ))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.PLATE_NUMBER.eq(getPlateNumber(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.CAR_VIN.eq(getCarVin(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.DRIVER_NAME.eq(getDriverName(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.DRIVER_PHONE.eq(getDriverPhone(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_TYPE.eq(getTestType(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID.eq(getSalesConsultantId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE.ge(getDateBegin(), Objects::nonNull))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE.le(getDateEnd(), Objects::nonNull))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_DATE.ge(getAppointmentStartTime(), Objects::nonNull))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_DATE.le(getAppointmentEndTime(), Objects::nonNull));
        return this;
    }

    /**
     * 构建创建试驾单时的校验链
     */
    public void buildCheckChainForCreate(CreateTestDriveSheetValidationChain chainParam) {
        // 构建校验链
        ValidationHandler<SacTestDriveSheetBO> chain = chainParam.buildValidationChain();
        try {
            // 调用校验链的入口开始校验
            chain.validate(this);
        } catch (Exception e) {
            log.error("创建试乘试驾单的校验失败 ", e);
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 构建开始试驾时的校验链各种类型的校验
     */
    public void buildCheckChainForStart(StartTestDriveSheetValidationChain chainParam, TestDriveAggregate.ValidationContext context) {
        // 构建校验链
        ValidationHandler<TestDriveAggregate.ValidationContext> chain = chainParam.buildValidationChain();
        try {
            // 调用校验链的入口开始校验
            chain.validate(context);
        } catch (Exception e) {
            log.error("开始试乘试驾的校验失败 {}", JSONObject.toJSONString(e));
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 构建结束试驾时的校验链各种类型的校验
     */
    public void buildCheckChainForStop(StopTestDriveSheetValidationChain chainParam, TestDriveAggregate.ValidationContext context) {
        // 构建校验链
        ValidationHandler<TestDriveAggregate.ValidationContext> chain = chainParam.buildValidationChain();
        try {
            // 调用校验链的入口开始校验
            chain.validate(context);
        } catch (Exception e) {
            log.error("结束试乘试驾的校验失败 ", e);
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 构建更新试驾单时的校验链
     */
    public void buildCheckChainForModify(ModifyTestDriveSheetValidationChain chainParam) {
        // 构建校验链
        ValidationHandler<SacTestDriveSheetBO> chain = chainParam.buildValidationChain();
        try {
            // 调用校验链的入口开始校验
            chain.validate(this);
        } catch (Exception e) {
            log.error("更新试乘试驾单的校验失败 {}", JSONObject.toJSONString(e));
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 构建重推试驾时的校验链各种类型的校验
     */
    public void buildCheckChainForRePush(RePushTestDriveSheetValidationChain chainParam, TestDriveAggregate.ValidationContext context) {
        // 构建校验链
        ValidationHandler<TestDriveAggregate.ValidationContext> chain = chainParam.buildValidationChain();
        try {
            // 调用校验链的入口开始校验
            chain.validate(context);
        } catch (Exception e) {
            log.error("重推试乘试驾的校验失败 {}", JSONObject.toJSONString(e));
            throw new BusicenException(e.getMessage());
        }
    }

    /**
     * 构建修改试乘试驾单的对象bo
     *
     * @param updateControlId
     * @return
     */
    public SacTestDriveSheetBO buildModifyParam(String updateControlId) {
        SacTestDriveSheetBO modifySheetBO = new SacTestDriveSheetBO();
        modifySheetBO.setTestDriveSheetId(getTestDriveSheetId());
        modifySheetBO.setTestType(getTestType());
        modifySheetBO.setTestDriveMethod(getTestDriveMethod());
        modifySheetBO.setSmallCarTypeCode(getSmallCarTypeCode());
        modifySheetBO.setSmallCarTypeName(getSmallCarTypeName());
        modifySheetBO.setPlateNumber(getPlateNumber());
        modifySheetBO.setCarVin(getCarVin());
        modifySheetBO.setColumn3(getColumn3());
        modifySheetBO.setColumn4(getColumn4());
        modifySheetBO.setLastUpdatedDate(getLastUpdatedDate());
        modifySheetBO.setModifier(getModifier());
        modifySheetBO.setModifyName(getModifyName());
        modifySheetBO.setUpdateControlId(updateControlId);
        return modifySheetBO;
    }

    /**
     * 构建开始试驾试乘试驾单的对象bo
     *
     * @param startRoadHaul
     * @return
     */
    public SacTestDriveSheetBO buildStartDrivingParam(BigDecimal startRoadHaul) {
        SacTestDriveSheetBO startDrivingSheetBO = new SacTestDriveSheetBO();
        startDrivingSheetBO.setTestDriveSheetId(getTestDriveSheetId());
        startDrivingSheetBO.setTestStatus(getTestStatus());
        startDrivingSheetBO.setTestStartRoadHaul(startRoadHaul);
        startDrivingSheetBO.setTestDriveAgreement(getTestDriveAgreement());
        startDrivingSheetBO.setTestDriveAgreementPDF(getTestDriveAgreementPDF());
        startDrivingSheetBO.setOtherAgreement(getOtherAgreement());
        startDrivingSheetBO.setCustomerIdNumberAgreement(getCustomerIdNumberAgreement());
        startDrivingSheetBO.setCustomerSignatureAgreement(getCustomerSignatureAgreement());
        startDrivingSheetBO.setDrivingLicencePhoto(getDrivingLicencePhoto());
        startDrivingSheetBO.setLastUpdatedDate(getLastUpdatedDate());
        startDrivingSheetBO.setIdNumber(getIdNumber());
        startDrivingSheetBO.setRealName(getRealName());
        startDrivingSheetBO.setStartTime(getLastUpdatedDate().format(TimeConstant.DEFAULT_FORMATTER));
        startDrivingSheetBO.setModifier(getModifier());
        startDrivingSheetBO.setModifyName(getModifyName());
        return startDrivingSheetBO;
    }

    /**
     * 构建结束试驾试乘试驾单的对象bo
     *
     * @return
     */
    public SacTestDriveSheetBO buildStopDrivingParam() {
        SacTestDriveSheetBO stopDrivingSheetBO = new SacTestDriveSheetBO();
        stopDrivingSheetBO.setTestDriveSheetId(getTestDriveSheetId());
        stopDrivingSheetBO.setTestStatus(getTestStatus());
        stopDrivingSheetBO.setColumn1(getTestDriveEndRemark());
        stopDrivingSheetBO.setTestRoadHaul(getTestRoadHaul());
        stopDrivingSheetBO.setTestEndRoadHaul(getTestEndRoadHaul());
        stopDrivingSheetBO.setTestDriveEndRemark(getTestDriveEndRemark());
        stopDrivingSheetBO.setLastUpdatedDate(getLastUpdatedDate());
        stopDrivingSheetBO.setEndTime(getEndTime());
        stopDrivingSheetBO.setModifier(getModifier());
        stopDrivingSheetBO.setModifyName(getModifyName());
        return stopDrivingSheetBO;
    }

    /**
     * 构建开始试驾试乘试驾签到的对象bo
     *
     * @return
     */
    public SacTestDriveSheetBO buildSignDrivingParam() {
        SacTestDriveSheetBO startDrivingSheetBO = new SacTestDriveSheetBO();
        startDrivingSheetBO.setTestDriveSheetId(getTestDriveSheetId());
        startDrivingSheetBO.setOtherAgreement(getOtherAgreement());
        startDrivingSheetBO.setCustomerIdNumberAgreement(getCustomerIdNumberAgreement());
        startDrivingSheetBO.setDrivingLicencePhoto(getDrivingLicencePhoto());
        startDrivingSheetBO.setLastUpdatedDate(LocalDateTime.now());
        startDrivingSheetBO.setUpdateControlId(UUID.randomUUID().toString());
        startDrivingSheetBO.setReceiver(UserInfoContext.get().getEmpName());
        startDrivingSheetBO.setReceiverCode(UserInfoContext.get().getEmpCode());
        startDrivingSheetBO.setReceiverTime(LocalDateTime.now());
        startDrivingSheetBO.setCustomerIDNumber(getIdNumber());
        startDrivingSheetBO.setRealName(getRealName());
        return startDrivingSheetBO;
    }
}
