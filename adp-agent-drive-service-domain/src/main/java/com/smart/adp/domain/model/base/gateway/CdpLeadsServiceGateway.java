package com.smart.adp.domain.model.base.gateway;

import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsEventVO;
import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsVO;

/**
 * @Description: adp推送数据给cdp的gateway
 * @Author: rik.ren
 * @Date: 2025/5/29 17:58
 **/
public interface CdpLeadsServiceGateway {
    /**
     * 保存数据
     *
     * @param entity
     * @return
     */
    Boolean saveData(IfsBaseCdpLeadsVO entity);

    Boolean saveDataEvent(IfsBaseCdpLeadsEventVO entity);
}
