package com.smart.adp.domain.model.drive.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;

import java.util.List;

/**
 * @Description: 试乘试驾预约单gateway
 * @Author: rik.ren
 * @Date: 2025/5/22 15:24
 **/
public interface SacAppointmentSheetGateway {
    /**
     * 根据条件查询预约单信息
     *
     * @param param
     * @return
     */
    List<SacAppointmentSheetBO> queryAppointByCondition(SacAppointmentSheetBO param, QueryColumn... needColumn);

    /**
     * 根据条件查询预约单的个数
     *
     * @param param
     * @return
     */
    Long queryAppointByCondition(SacAppointmentSheetBO param);

    /**
     * 创建试乘试驾预约单
     *
     * @param param
     * @return
     */
    Boolean createAppointmentSheet(SacAppointmentSheetBO param);

    /**
     * 更新预约单信息
     *
     * @param param
     * @return
     */
    Boolean modifyAppointmentSheet(SacAppointmentSheetBO param);

    /**
     * 删除预约单信息
     *
     * @param param
     * @return
     */
    Boolean deleteAppointmentSheet(SacAppointmentSheetBO param);
}
