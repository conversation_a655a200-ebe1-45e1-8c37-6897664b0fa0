package com.smart.adp.domain.model.drive.validation.startTestDrive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.service.ISacTestDriveSheetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * @Description: 车辆是否在行驶中校验
 * @Author: rik.ren
 * @Date: 2025/5/29 14:30
 **/
@Slf4j
@Service
public class CarDrivingValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {

    @Autowired
    private ISacTestDriveSheetService sheetService;

    /**
     * 具体校验逻辑抽象方法（由子类实现）
     *
     * @param context 校验上下文对象
     */
    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        SacTestDriveSheetBO queryCarDriving = new SacTestDriveSheetBO(Boolean.FALSE);
        queryCarDriving.setPlateNumber(querySheetResult.getPlateNumber());
        queryCarDriving.setTestStatus(TestDriveStatusEnum.IN_PROGRESS.getCode());
        SacTestDriveSheetBO queryCarDrivingResult = sheetService.queryTestDriveSheetInfo(queryCarDriving,
                SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID, SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_NAME,
                SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_NAME);
        if (ObjectUtil.isNotEmpty(queryCarDrivingResult)) {
            throw new BusinessException(RespCode.FAIL.getCode(), String.format(
                    "当前车辆正在试驾中，试驾产品专家%s，客户为%s",
                    queryCarDrivingResult.getSalesConsultantName(),
                    queryCarDrivingResult.getCustomerName()
            ));
        }
    }
}
