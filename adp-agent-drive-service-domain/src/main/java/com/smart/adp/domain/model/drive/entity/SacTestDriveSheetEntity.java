package com.smart.adp.domain.model.drive.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import lombok.*;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * 试乘试驾单表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_test_drive_sheet", schema = "csc")
public class SacTestDriveSheetEntity {

    /**
     * 试乘试驾单ID
     */
    @Id(value = "TEST_DRIVE_SHEET_ID")
    private String testDriveSheetId;

    /**
     * 试乘试驾单号
     */
    @Column(value = "TEST_DRIVE_ORDER_NO")
    private String testDriveOrderNo;

    /**
     * 试乘试驾预约单ID
     */
    @Column(value = "APPOINTMENT_ID")
    private String appointmentId;

    /**
     * 试乘试驾状态
     *
     * @see TestDriveStatusEnum
     */
    @Column(value = "TEST_STATUS")
    private String testStatus;

    /**
     * 录音ID
     */
    @Column(value = "RECORD_ID")
    private String recordId;

    /**
     * 所属专营店编码
     */
    @Column(value = "DLR_CODE")
    private String dlrCode;

    /**
     * 所属专营店名称
     */
    @Column(value = "DLR_NAME")
    private String dlrName;

    /**
     * 销售顾问姓名
     */
    @Column(value = "SALES_CONSULTANT_NAME")
    private String salesConsultantName;

    /**
     * 销售顾问ID
     */
    @Column(value = "SALES_CONSULTANT_ID")
    private String salesConsultantId;

    /**
     * 意向级别编码
     */
    @Column(value = "INTEN_LEVEL_CODE")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @Column(value = "INTEN_LEVEL_NAME")
    private String intenLevelName;

    /**
     * 驾驶人与客户关系
     */
    @Column(value = "DRIVER_CUSTOMER_RELATION")
    private String driverCustomerRelation;

    /**
     * 驾驶人姓名
     */
    @Column(value = "DRIVER_NAME")
    private String driverName;

    /**
     * 驾驶人联系电话
     */
    @Column(value = "DRIVER_PHONE")
    private String driverPhone;

    /**
     * 驾驶证类型
     */
    @Column(value = "DRIVING_LICENCE_TYPE")
    private String drivingLicenceType;

    /**
     * 驾驶证号码
     */
    @Column(value = "DRIVING_LICENCE_NUMBER")
    private String drivingLicenceNumber;

    /**
     * 驾驶人地址
     */
    @Column(value = "ADDRESS")
    private String address;

    /**
     * 驾驶证附件
     */
    @Column(value = "DRIVING_LICENCE_PHOTO")
    private String drivingLicencePhoto;

    /**
     * 试乘试驾行驶里程
     */
    @Column(value = "TEST_ROAD_HAUL")
    private java.math.BigDecimal testRoadHaul;

    /**
     * 试乘试驾开始里程
     */
    @Column(value = "TEST_START_ROAD_HAUL")
    private java.math.BigDecimal testStartRoadHaul;

    /**
     * 试乘试驾结束里程
     */
    @Column(value = "TEST_END_ROAD_HAUL")
    private java.math.BigDecimal testEndRoadHaul;

    /**
     * 线索单号
     */
    @Column(value = "DLR_CLUE_ORDER_NO")
    private String dlrClueOrderNo;

    /**
     * 客户姓名
     */
    @Column(value = "CUSTOMER_NAME")
    private String customerName;

    /**
     * 客户ID
     */
    @Column(value = "CUSTOMER_ID")
    private String customerId;

    /**
     * 客户电话
     */
    @Column(value = "CUSTOMER_PHONE")
    private String customerPhone;

    /**
     * 性别
     */
    @Column(value = "CUSTOMER_SEX")
    private String customerSex;

    /**
     * 试乘试驾车型编码
     */
    @Column(value = "SMALL_CAR_TYPE_CODE")
    private String smallCarTypeCode;

    /**
     * 试乘试驾车型名称
     */
    @Column(value = "SMALL_CAR_TYPE_NAME")
    private String smallCarTypeName;

    /**
     * 试驾车牌
     */
    @Column(value = "PLATE_NUMBER")
    private String plateNumber;

    /**
     * VIN(车架号)
     */
    @Column(value = "CAR_VIN")
    private String carVin;

    /**
     * 试乘试驾类型(0：试乘，1：试驾，2：深度试驾)
     *
     * @see TestDriveTypeEnum
     */
    @Column(value = "TEST_TYPE")
    private String testType;

    /**
     * 预约渠道(0：门店自建，1：线上预约)
     */
    @Column(value = "APPOINTMENT_CHANNEL")
    private String appointmentChannel;

    /**
     * 开始时间
     */
    @Column(value = "START_TIME")
    private String startTime;

    /**
     * 结束时间
     */
    @Column(value = "END_TIME")
    private String endTime;

    /**
     * 协议附件
     */
    @Column(value = "TEST_DRIVE_AGREEMENT")
    private String testDriveAgreement;

    /**
     * 协议附件PDF
     */
    @Column(value = "TEST_DRIVE_AGREEMENT_PDF")
    private String testDriveAgreementPDF;

    /**
     * 顾客身份证附件
     */
    @Column(value = "CUSTOMER_ID_NUMBER_AGREEMENT")
    private String customerIdNumberAgreement;

    /**
     * 顾客签名附件
     */
    @Column(value = "CUSTOMER_SIGNATURE_AGREEMENT")
    private String customerSignatureAgreement;

    /**
     * 顾客身份证号码
     */
    @Column(value = "CUSTOMER_ID_NUMBER")
    private String customerIDNumber;

    /**
     * OCR最后识别的姓名
     */
    @Column(value = "REAL_NAME")
    private String realName;

    /**
     * 试驾押金
     */
    @Column(value = "DEPOSIT")
    private java.math.BigDecimal deposit;

    /**
     * 原试驾单id，多个逗号分割
     */
    @Column(value = "OLD_TEST_DRIVE_SHEET_ID")
    private String oldTestDriveSheetId;

    /**
     * 接待人
     */
    @Column(value = "RECEIVER")
    private String receiver;

    /**
     * 接待人编码
     */
    @Column(value = "RECEIVER_CODE")
    private String receiverCode;

    /**
     * 接待时间
     */
    @Column(value = "RECEIVER_TIME")
    private LocalDateTime receiverTime;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * 扩展字段1(试乘试驾结束备注)
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2 邀请码
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 试驾路线ID
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 路线分类名称
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 扩展字段8 (一级信息来源编码INFO_CHAN_M_CODE)
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 扩展字段9 (二级信息来源编码INFO_CHAN_D_CODE)
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10，试驾录音摘要
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 是否可以更换门店
     */
    @Column(value = "IS_CAN_CHANGE")
    private String isCanChange;

    /**
     * 其他附件
     */
    @Column(value = "OTHER_AGREEMENT")
    private String otherAgreement;

    /**
     * 试驾单补录标识（1 :补录）
     */
    @Column(value = "IMPORT_FLAG")
    private String importFlag;

    /**
     * 补录数据是否重复（1:重复）
     */
    @Column(value = "REPEAT_FLAG")
    private String repeatFlag;

    /**
     * 是否已发送评价短信给客户（1 :已发送）
     */
    @Column(value = "EVALUATE_FLAG")
    private String evaluateFlag;

    /**
     * 试驾方式
     */
    @Column(value = "TEST_DRIVE_METHOD")
    private String testDriveMethod;

    public SacTestDriveSheetEntity(String testDriveSheetId) {
        this.testDriveSheetId = testDriveSheetId;
    }
}
