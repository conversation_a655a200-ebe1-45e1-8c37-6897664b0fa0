package com.smart.adp.domain.model.drive;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.constant.SqlConsts;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.annotation.SmartDistributedLock;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.enums.TestDriveMethodEnum;
import com.smart.adp.domain.model.drive.bo.*;
import com.smart.adp.domain.model.drive.entity.SacTestDriveReviewRecordEntity;
import com.smart.adp.domain.model.drive.service.*;
import com.smart.adp.domain.model.drive.validation.createTestDrive.CreateTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.modifyTestDrive.ModifyTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.startTestDrive.StartTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.stopTestDrive.StopTestDriveSheetValidationChain;
import com.smart.adp.domain.model.drive.validation.testSheetDataRePush.RePushTestDriveSheetValidationChain;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveTaskEntityTableDef.SAC_TEST_DRIVE_TASK_ENTITY;

/**
 * @Description: 试乘试驾聚合根
 * 1. 维护试驾单的完整业务规则
 * 2. 协调试驾单相关实体协作
 * @Author: rik.ren
 * @Date: 2025/5/21 15:18
 **/
@Slf4j
@Service
public class TestDriveAggregate {

    @Autowired
    private ISacTestDriveSheetService sheetService;
    @Autowired
    private ITestCarPrepareService prepareService;
    @Autowired
    private SacAppointmentSheetService appointmentSheetService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private CreateTestDriveSheetValidationChain chainCreate;
    @Autowired
    private ModifyTestDriveSheetValidationChain chainModify;
    @Autowired
    private StartTestDriveSheetValidationChain chainStart;
    @Autowired
    private StopTestDriveSheetValidationChain chainStop;
    @Autowired
    private RePushTestDriveSheetValidationChain chainRePush;
    @Autowired
    private SacTestDriveTaskService testDriveTaskService;
    @Autowired
    private SacTestDriveReviewRecordService testDriveReviewRecordService;
    // 发送短信的开关，测试阶段需要关闭
    @org.springframework.beans.factory.annotation.Value("${businessConfig.operateSwitch.testDriveSMS:true}")
    private Boolean testDriveSMSSwitch;

    // 为每个业务操作创建具体结果类
    @Value
    public static class QueryResult implements TestDriveAggregateResult {
        DomainPage<SacTestDriveSheetBO> sheets;
        List<TestCarPrepareBO> preparations;
    }

    @Value
    public static class QueryTestDriveInfoResult implements TestDriveAggregateResult {
        SacTestDriveSheetBO sheets;
        TestDriveReviewRecordBO reviewRecordBO;
        TestCarPrepareBO testCarPrepare;
    }

    @Value
    public static class CreateTestDriveSheetResult implements TestDriveAggregateResult {
        Boolean result;
        SacAppointmentSheetBO appointmentSheetBO;
        SacTestDriveSheetBO testDriveSheetBO;
    }

    @Builder
    @Getter
    public static class ValidationContext {
        // 固定核心参数
        private final SacTestDriveSheetBO requestParam;

        // 动态存储校验结果（替代多个固定字段）
        private Map<String, Object> contextData = new HashMap<>();

        // 类型安全的存取方法
        public <T> T getResult(String key) {
            return (T) contextData.get(key);
        }

        public void putResult(String key, Object value) {
            contextData.put(key, value);
        }
    }

    /**
     * 查询试驾单列表
     *
     * @param param
     * @return
     */
    public QueryResult queryTestDriveSheetList(SacTestDriveSheetBO param) {
        // 1. 查询试驾单信息
        DomainPage<SacTestDriveSheetBO> testDriveDomainPage = sheetService.queryTestDriveSheetPage(param);
        if (ObjectUtil.isEmpty(testDriveDomainPage.getRecords()) || testDriveDomainPage.getTotalCount() == 0L) {
            return new QueryResult(null, null);
        }

        // 2. 查询所属门店信息
        List<String> listCarVin =
                testDriveDomainPage.getRecords().stream().map(SacTestDriveSheetBO::getCarVin).collect(Collectors.toList());
        List<TestCarPrepareBO> listTestCarPrepare =
                prepareService.queryTestCarPrepareList(TestCarPrepareBO.buildTestCarPrepareBO(listCarVin, null));
        return new QueryResult(testDriveDomainPage, listTestCarPrepare);
    }

    /**
     * 查询试驾单总数
     *
     * @param param
     * @return
     */
    public Long queryTestDriveSheetCount(SacTestDriveSheetBO param) {
        return sheetService.queryTestDriveSheetCount(param);
    }

    /**
     * 查询试驾单列表
     *
     * @param param
     * @return
     */
    public QueryTestDriveInfoResult queryTestDriveSheetInfo(SacTestDriveSheetBO param) {
        // 1. 查询试驾单信息
        SacTestDriveSheetBO testDriveSheetBO = sheetService.queryTestDriveSheetInfo(param);
        if (ObjectUtil.isEmpty(testDriveSheetBO) || StringUtils.isEmpty(testDriveSheetBO.getTestDriveSheetId())) {
            return null;
        }
        // 2. 查询试驾跟进内容
        TestDriveReviewRecordBO reviewRecordBO = new TestDriveReviewRecordBO();
        reviewRecordBO.setTestDriveSheetId(testDriveSheetBO.getTestDriveSheetId());
        SacTestDriveReviewRecordEntity resultEntity = testDriveReviewRecordService.queryTestDriveReviewRecordByCondition(reviewRecordBO);
        reviewRecordBO = BeanUtil.copyProperties(resultEntity, TestDriveReviewRecordBO.class);
        // 3. 查询整备信息
        List<TestCarPrepareBO> listTestCarPrepare =
                prepareService.queryTestCarPrepareList(TestCarPrepareBO.buildTestCarPrepareBO(testDriveSheetBO.getCarVin()));
        return new QueryTestDriveInfoResult(testDriveSheetBO, reviewRecordBO, CollectionUtil.isEmpty(listTestCarPrepare) ? null :
                listTestCarPrepare.get(0));
    }

    /**
     * 查询试驾单列表
     *
     * @param param
     * @return
     */
    public SacTestDriveSheetBO queryTestDriveSheetDlr(SacTestDriveSheetBO param) {
        // 1. 查询试驾单信息
        SacTestDriveSheetBO testDriveSheetBO = sheetService.queryTestDriveSheetInfo(param);
        if (ObjectUtil.isEmpty(testDriveSheetBO) || StringUtils.isEmpty(testDriveSheetBO.getTestDriveSheetId())) {
            return null;
        }
        return testDriveSheetBO;
    }

    /**
     * 创建试乘试驾单
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'operate_plateNumber_lock_' + #param.plateNumber", message = "该车辆正在操作试驾单，请稍后", block = true,
            waitTime = 2)
    public CreateTestDriveSheetResult createTestDriveSheet(SacTestDriveSheetBO param) {
        // 1. 执行校验链
        param.buildCheckChainForCreate(chainCreate);
        AtomicReference<CreateTestDriveSheetResult> result = new AtomicReference<>();
        // 2. 手动控制事务，插入预约表和试驾表
        transactionTemplate.execute(status -> {
            try {
                // 2.1 插入预约表
                SacAppointmentSheetBO appointmentSheetBO = param.buildCreateAppointBo();
                param.setAppointmentId(appointmentSheetBO.getAppointmentId());
                param.setTestDriveSheetId(param.getTestDriveSheetId());
                Boolean appointmentResult = appointmentSheetService.createAppointmentSheet(appointmentSheetBO);

                // 2.2 插入试驾表
                Boolean testDriveResult = sheetService.createTestDriveSheet(param);
                // 2.3 同时成功才提交
                if (appointmentResult && testDriveResult) {
                    result.set(new CreateTestDriveSheetResult(Boolean.TRUE, appointmentSheetBO, param));
                    return true;
                }
                log.info("创建试乘试驾单事务回滚");
                status.setRollbackOnly();
                result.set(new CreateTestDriveSheetResult(Boolean.FALSE, appointmentSheetBO, param));
                return false;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("创建试乘试驾单错误 ", e);
                throw new BusinessException(RespCode.FAIL.getCode(), "创建试乘试驾单错误");
            }
        });
        return result.get();
    }

    /**
     * 编辑试乘试驾预约单
     *
     * @param param
     * @return
     */
    public Boolean modifyAppointmentSheetByAppOrderOn(SacAppointmentSheetBO param) {
        log.info("异步更新试驾预约单");
        // 通过appointmentId去更新预约试驾单的线索单号和客户ID
        SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
        appointmentSheetBO.setAppointmentId(param.getAppointmentId());
        appointmentSheetBO.setDlrClueOrderNo(param.getDlrClueOrderNo());//对应线索的serverOrder
        appointmentSheetBO.setCustomerId(param.getCustomerId());
        appointmentSheetBO.setAppointmentOrderNo(param.getAppointmentOrderNo());
        return appointmentSheetService.modifyAppointmentSheet(appointmentSheetBO);
    }

    /**
     * 编辑试乘试驾单
     *
     * @param param
     * @return
     */
    public Boolean modifyTestDriveSheet(SacTestDriveSheetBO param) {
        // 通过appointmentId去更新试乘试驾单的DLR_CLUE_ORDER_NO和CUSTOMER_ID
        log.info("异步更新试驾单");
        SacTestDriveSheetBO sheetBO = new SacTestDriveSheetBO();
        sheetBO.setTestDriveSheetId(param.getTestDriveSheetId());
        sheetBO.setDlrClueOrderNo(param.getDlrClueOrderNo());
        sheetBO.setCustomerId(param.getCustomerId());
        sheetBO.setTestDriveOrderNo(param.getTestDriveOrderNo());
        return sheetService.modifyTestDriveSheet(sheetBO);
    }

    /**
     * 发送试驾消息
     *
     * @param paramBo
     */
    public void sendMessage(SacTestDriveSheetBO paramBo) {
        if (ObjectUtil.isEmpty(paramBo) || StringUtils.isEmpty(paramBo.getCustomerPhone()) || !testDriveSMSSwitch) {
            return;
        }
        log.info("发送试驾消息 " + JSONObject.toJSONString(paramBo));
        try {
            if ("1".equals(paramBo.getIsSendMessage()) && TestDriveMethodEnum.STORE.getCode().equals(paramBo.getTestDriveMethod())) {
                Map<String, Object> paramM = new HashMap<>();
                Map<String, Object> param = new HashMap<>();
                param.put("time", paramBo.getAppointmentTestDate() + " " + paramBo.getAppointmentTestTime());
                param.put("dlrShortName", paramBo.getDlrName());
                paramM.put("recNum", new String[]{paramBo.getCustomerPhone()});
                paramM.put("smsParam", param);
                paramM.put("smsTemplateId", "adp002");//模板id
                sheetService.sendTestDriveMsg(paramM);
            }
        } catch (Exception e) {
            log.error(String.format("appointmentSheetSave试驾预约发送短信异常%s", paramBo.getCustomerPhone()), e);
        }
    }

    /**
     * 删除试乘试驾单、预约单
     *
     * @param param
     * @return
     */
    public Boolean deleteTestDriveSheet(SacTestDriveSheetBO param) {
        // 删除试乘试驾单
        sheetService.deleteTestDriveSheet(param);
        // 删除预约单
        SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
        appointmentSheetBO.setAppointmentId(param.getAppointmentId());
        appointmentSheetService.deleteAppointmentSheet(appointmentSheetBO);
        return Boolean.TRUE;
    }

    /**
     * 更新试乘试驾单
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'operate_plateNumber_lock_' + #param.plateNumber", message = "该车辆正在操作试驾单，请稍后", block = true,
            waitTime = 2)
    public Boolean modifyTestDriverSheet(SacTestDriveSheetBO param) {
        // 1. 查询试驾单
        QueryColumn[] queryColumns = {SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID, SAC_TEST_DRIVE_SHEET_ENTITY.UPDATE_CONTROL_ID,
                SAC_TEST_DRIVE_SHEET_ENTITY.RECEIVER_TIME, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_PHONE};
        SacTestDriveSheetBO querySheetResult = getTestDriveSheet(param, queryColumns);
        param.setCustomerPhone(querySheetResult.getCustomerPhone());
        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.FALSE);
        // 2.校验链
        param.buildCheckChainForModify(chainModify);
        transactionTemplate.execute(status -> {
            try {
                // 2. 更新试驾单
                Boolean modifyDriveSheetResult =
                        sheetService.modifyTestDriveSheet(param.buildModifyParam(querySheetResult.getUpdateControlId()));

                // 3. 更新预约单信息
                Boolean modifyAppointResult =
                        appointmentSheetService.modifyAppointmentSheet(new SacAppointmentSheetBO().buildModifyBO(param));
                // 2和3 同时成功才提交
                if (modifyDriveSheetResult && modifyAppointResult) {
                    result.set(Boolean.TRUE);
                    return true;
                }
                log.info("更新试乘试驾单事务回滚");
                status.setRollbackOnly();
                return false;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("更新试乘试驾单错误 ", e);
                throw new BusinessException(RespCode.FAIL.getCode(), "更新试乘试驾单错误");
            }
        });
        return result.get();
    }

    /**
     * 开始试乘试驾
     * 1、根据试驾单号获取试驾单
     * 2、验证是否已经点击了签到
     * 3、判断这辆车是否正在试驾中，如果有，就返回"当前车辆正在试驾中，试驾产品专家%s，客户为%s",
     * 7、更新试乘试驾单
     * 8、更新整备表的车辆信息为试驾中CarStatusCode为1
     * 9、更新预约单isTestDrive为1
     *
     * @param boParam
     * @return
     */
    public Boolean testDriveStart(SacTestDriveSheetBO boParam) {

        ValidationContext context = ValidationContext.builder().requestParam(boParam).contextData(new HashMap<>()).build();

        // 1. 校验链
        boParam.buildCheckChainForStart(chainStart, context);
        // 用上下文中拿到结果
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        TestCarPrepareBO queryPrepareResult = context.getResult("queryPrepareResult");

        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.FALSE);
        transactionTemplate.execute(status -> {
            try {
                // 2. 更新试驾单表
                Boolean modifyTestDriveSheetResult =
                        sheetService.modifyTestDriveSheet(boParam.buildStartDrivingParam(queryPrepareResult.getTestcarKilometers()));

                // 3. 更新整备表的车辆信息为试驾中CarStatusCode为1
                Boolean modifyTestCarPrepareResult =
                        prepareService.modifyTestCarPrepare(new TestCarPrepareBO().buildStartDriveModifyBO(querySheetResult.getCarVin(),
                                querySheetResult.getPlateNumber()));

                // 4. 更新预约单isTestDrive为1
                boParam.setAppointmentId(querySheetResult.getAppointmentId());
                Boolean modifyAppSheetResult =
                        appointmentSheetService.modifyAppointmentSheet(new SacAppointmentSheetBO().buildStartDrivingModifyBO(boParam));
                // 同时成功才提交
                if (modifyTestDriveSheetResult && modifyTestCarPrepareResult && modifyAppSheetResult) {
                    result.set(Boolean.TRUE);
                    return true;
                }
                log.info("开始试乘试驾事务回滚");
                status.setRollbackOnly();
                return false;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("开始试乘试驾错误 ", e);
                throw new BusinessException(RespCode.FAIL.getCode(), "开始试乘试驾错误");
            }
        });
        return result.get();
    }

    /**
     * 结束试驾
     *
     * @param boParam
     * @return
     */
    public Boolean testDriveStop(SacTestDriveSheetBO boParam) {
        ValidationContext context = ValidationContext.builder().requestParam(boParam).contextData(new HashMap<>()).build();

        // 1. 校验链
        boParam.buildCheckChainForStop(chainStop, context);
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        AtomicReference<Boolean> result = new AtomicReference<>(Boolean.FALSE);
        // 2. 更新试驾单表
        transactionTemplate.execute(status -> {
            try {
                Boolean modifyTestDriveSheetResult = sheetService.modifyTestDriveSheet(boParam.buildStopDrivingParam());

                // 3. 更新整备表的车辆信息为试驾中CarStatusCode为1
                Boolean modifyTestCarPrepareResult =
                        prepareService.modifyTestCarPrepareForStop(new TestCarPrepareBO().buildStopDriveModifyBO(querySheetResult.getCarVin(),
                                querySheetResult.getPlateNumber(), boParam.getTestEndRoadHaul()));
                if (modifyTestDriveSheetResult && modifyTestCarPrepareResult) {
                    result.set(Boolean.TRUE);
                    return true;
                }
                log.info("结束试驾事务回滚");
                status.setRollbackOnly();
                return false;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("结束试乘试驾错误 ", e);
                throw new BusinessException(RespCode.FAIL.getCode(), "结束试乘试驾错误");
            }
        });
        return result.get();
    }

    /**
     * 试驾签到
     *
     * @param param
     * @return
     */
    public Boolean testDriveSign(SacTestDriveSheetBO param) {
        try {
            // 1. 查询试驾单
            return sheetService.modifyTestDriveSheet(param.buildSignDrivingParam());
        } catch (Exception e) {
            log.error("试驾签到错误 ", e);
            throw new BusinessException(RespCode.FAIL.getCode(), "试驾签到错误");
        }
    }

    /**
     * 获取试驾单
     *
     * @param param
     */
    public SacTestDriveSheetBO getTestDriveSheet(SacTestDriveSheetBO param, QueryColumn... columns) {
        SacTestDriveSheetBO querySheetResult =
                sheetService.queryTestDriveSheetInfoById(new SacTestDriveSheetBO(param.getTestDriveSheetId(),
                        param.getNeedAppointmentSheet()), columns);
        if (ObjectUtil.isEmpty(querySheetResult) || StringUtils.isEmpty(querySheetResult.getTestDriveSheetId())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "试乘试驾单不存在");
        }
        return querySheetResult;
    }

    /**
     * 试驾结束发送ZTMQ
     *
     * @param param
     * @return
     */
    public Boolean sendZTMQ(SacTestDriveSheetBO param) {
        Map<String, Object> driveEndMap = BeanUtil.beanToMap(param);
        log.info("试驾结束发送ZTMQ {}", driveEndMap);
        return sheetService.sendZTMQ(driveEndMap);
    }

    /**
     * 试驾结束时记录接口表，获取BI车机数据
     *
     * @param getTestDriveSheetResult
     */
    public Boolean insertIfVehicleData(SacTestDriveSheetBO getTestDriveSheetResult) {
        // 实际插入车机数据接口表的参数
        HashMap<String, Object> paramMap = new HashMap<>(3);
        paramMap.put("logsId", UUID.randomUUID().toString().replace("-", ""));
        paramMap.put("testDriveOrderNo", getTestDriveSheetResult.getTestDriveOrderNo());
        paramMap.put("insertDate", LocalDateTime.now());
        // 调用xapiInsert的公共入参
        Map<String, Object> param = new HashMap<>(1);
        param.put("mapParam", paramMap);
        return sheetService.vehicleData(param);
    }

    /**
     * 试驾单数据重推外部服务
     *
     * @param boParam
     * @return
     */
    public SacTestDriveSheetBO queryTestSheetDataForRePush(SacTestDriveSheetBO boParam) {
        // 1. 查询试驾单信息
        SacTestDriveSheetBO querySheetResult = sheetService.queryTestDriveSheetInfoById(boParam,
                SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_METHOD, SAC_TEST_DRIVE_SHEET_ENTITY.DLR_NAME,
                SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID,
                SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_ORDER_NO,
                SAC_TEST_DRIVE_SHEET_ENTITY.RECEIVER_TIME, SAC_TEST_DRIVE_SHEET_ENTITY.START_TIME,
                SAC_TEST_DRIVE_SHEET_ENTITY.SMALL_CAR_TYPE_CODE, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_NAME,
                SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_PHONE,
                SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_DATE.as("appointmentTestDate"),
                SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_TIME.as("appointmentTestTime"),
                SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME.as("appointmentStartTime"),
                SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME.as("appointmentEndTime"));
        if (ObjectUtil.isEmpty(querySheetResult) || StringUtils.isEmpty(querySheetResult.getTestDriveSheetId())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "试乘试驾单不存在");
        }
        return querySheetResult;
    }

    /**
     * 分页查询试驾任务
     *
     * @param boParam
     * @return
     */
    public DomainPage<SacTestDriveTaskBO> queryTestDriveTask(SacTestDriveTaskBO boParam) {
        QueryColumn[] QUERY_COLUMNS = {SAC_TEST_DRIVE_TASK_ENTITY.ID,
                SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_ID, SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_CODE,
                SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_NAME, SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_DLR_CODE,
                SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_DLR_NAME, SAC_TEST_DRIVE_TASK_ENTITY.TEST_TYPE,
                SAC_TEST_DRIVE_TASK_ENTITY.CUST_NAME, SAC_TEST_DRIVE_TASK_ENTITY.PHONE,
                SAC_TEST_DRIVE_TASK_ENTITY.SMALL_CAR_TYPE_NAME, SAC_TEST_DRIVE_TASK_ENTITY.SMALL_CAR_TYPE_NAME,
                SAC_TEST_DRIVE_TASK_ENTITY.APPOINTMENT_TEST_DATE, SAC_TEST_DRIVE_TASK_ENTITY.APPOINTMENT_TEST_TIME,
                SAC_TEST_DRIVE_TASK_ENTITY.CREATED_DATE, SAC_TEST_DRIVE_TASK_ENTITY.TASK_TITLE,
                SAC_TEST_DRIVE_TASK_ENTITY.UPDATE_CONTROL_ID};
        // 1. 查询试驾任务信息
        DomainPage<SacTestDriveTaskBO> testDriveTaskDomainPage = testDriveTaskService.queryTestDriveTaskPage(boParam,
                new QueryOrderBy(SAC_TEST_DRIVE_TASK_ENTITY.CREATED_DATE, SqlConsts.DESC), QUERY_COLUMNS);
        if (ObjectUtil.isEmpty(testDriveTaskDomainPage.getRecords()) || testDriveTaskDomainPage.getTotalCount() == 0L) {
            return null;
        }
        return testDriveTaskDomainPage;
    }

    /**
     * 查询满足条件的试驾任务个数
     *
     * @param boParam
     * @return
     */
    public Long queryTestDriveTaskCount(SacTestDriveTaskBO boParam) {
        // 1. 查询试驾任务信息
        Long result = testDriveTaskService.queryTestDriveTaskCount(boParam);
        return result;
    }

    /**
     * 根据试驾单ID查询试驾任务
     *
     * @param testDriveTaskId
     * @return
     */
    public SacTestDriveTaskBO getTestDriveTask(String testDriveTaskId) {
        SacTestDriveTaskBO taskBO = new SacTestDriveTaskBO();
        taskBO.setNewTestDriveSheetId(testDriveTaskId);
        QueryColumn[] queryColumns = {SAC_TEST_DRIVE_TASK_ENTITY.ID, SAC_TEST_DRIVE_TASK_ENTITY.UPDATE_CONTROL_ID};
        SacTestDriveTaskBO testDriveTask = testDriveTaskService.getTestDriveTask(taskBO, queryColumns);
        return testDriveTask;
    }

    /**
     * 更新试驾任务
     *
     * @param taskId
     * @param sheetBO
     * @return
     */
    public Boolean modifyTestDriveTask(String taskId, String oldUpdateControlId, SacTestDriveSheetBO sheetBO) {
        Boolean result = testDriveTaskService.modifyTestDriveTask(new SacTestDriveTaskBO().buildTaskBO(taskId, oldUpdateControlId,
                sheetBO));
        return result;
    }

    /**
     * 更新试驾任务
     *
     * @param sacTestDriveTaskBO
     * @return
     */
    public Boolean modifyTestDriveTask(SacTestDriveTaskBO sacTestDriveTaskBO) {
        Boolean result = testDriveTaskService.modifyTestDriveTask(sacTestDriveTaskBO);
        return result;
    }
}
