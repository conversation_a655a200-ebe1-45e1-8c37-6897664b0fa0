package com.smart.adp.domain.helper;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.helper.TypedDomainPage;
import lombok.Builder;

import java.util.List;

@Builder
public class TypeDomainPageConventHelp<T> {
    public DomainPage<T> convent(TypedDomainPage<?> page) {
        if (ObjectUtil.isEmpty(page)) {
            return null;
        }
        return new DomainPage<T>((List<T>) page.getRecords(), page.getPageNumber(), page.getPageSize(), page.getTotalCount());
    }
}
