package com.smart.adp.domain.common;

import cn.hutool.core.collection.CollUtil;
import lombok.Builder;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 领域层的分页，领域层不涉及基础设施层的ORM
 * @Author: rik.ren
 * @Date: 2025/3/15 18:11
 **/
@Builder
public class DomainPage<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    // 核心分页属性
    private List<T> records;
    private int pageNumber;
    private int pageSize;
    private long totalCount;

    // 业务相关构造函数
    public DomainPage(List<T> records, int pageNumber, int pageSize, long totalCount) {
        if (pageNumber < 1) {
            throw new IllegalArgumentException("页码最小是1");
        }
        if (pageSize < 1) {
            throw new IllegalArgumentException("页大小最小是1");
        }

        this.records = records != null ? new ArrayList<>(records) : Collections.emptyList();
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
    }

    public DomainPage(int pageNumber, int pageSize) {
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
    }

    public DomainPage() {
    }

    /**
     * 创建合并分页的空数据分页对象
     *
     * @param pages 多个分页对象
     * @return 合并后的分页对象（records为空集合）,records自己set
     */
    public static <T> DomainPage<T> mergePages(DomainPage<?>... pages) {
        if (pages == null || pages.length == 0) {
            return new DomainPage<>(Collections.emptyList(), 1, 10, 0);
        }
        // 过滤null值并收集非空分页
        List<DomainPage<?>> validPages = pages == null
                ? Collections.emptyList()
                : Arrays.stream(pages)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 如果没有有效分页，返回默认值
        if (validPages.isEmpty()) {
            return new DomainPage<>(
                    Collections.emptyList(),
                    1, 10, 0
            );
        }
        // 计算最大分页参数
        int maxPageNumber = Arrays.stream(pages)
                .mapToInt(DomainPage::getPageNumber)
                .max()
                .orElse(1);

        int maxPageSize = Arrays.stream(pages)
                .mapToInt(DomainPage::getPageSize)
                .max()
                .orElse(10);

        long maxTotalCount = Arrays.stream(pages)
                .mapToLong(DomainPage::getTotalCount)
                .max()
                .orElse(0);

        return new DomainPage<>(
                Collections.emptyList(),
                maxPageNumber,
                maxPageSize,
                maxTotalCount
        );
    }

    /**
     * 合并多个分页对象
     *
     * @param mergedRecords 合并后的记录集合
     * @param pages         要合并的分页对象
     * @return 合并后的分页对象（使用最大的分页参数）
     */
    public static <R> DomainPage<R> mergePages(List<R> mergedRecords, DomainPage<?>... pages) {
        if (pages == null || pages.length == 0) {
            return new DomainPage<>(mergedRecords, 1, 10, 0);
        }
        // 过滤null值并收集非空分页
        List<DomainPage<?>> validPages = pages == null
                ? Collections.emptyList()
                : Arrays.stream(pages)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 如果没有有效分页，返回默认值
        if (validPages.isEmpty()) {
            return new DomainPage<>(
                    mergedRecords != null ? mergedRecords : Collections.emptyList(),
                    1, 10, 0
            );
        }
        // 计算最大分页参数
        int maxPageNumber = validPages.stream()
                .mapToInt(DomainPage::getPageNumber)
                .max()
                .orElse(1);

        int maxPageSize = validPages.stream()
                .mapToInt(DomainPage::getPageSize)
                .max()
                .orElse(10);

        long maxTotalCount = validPages.stream()
                .mapToLong(DomainPage::getTotalCount)
                .max()
                .orElse(0);

        return new DomainPage<>(
                CollUtil.isNotEmpty(mergedRecords) ? mergedRecords : Collections.emptyList(),
                maxPageNumber,
                maxPageSize,
                maxTotalCount
        );
    }

    // 业务相关构造函数
    public DomainPage initEmpty() {
        this.records = Collections.emptyList();
        this.pageNumber = 1;
        this.pageSize = 10;
        this.totalCount = 0;
        return this;
    }

    // 核心业务方法
    public int getTotalPages() {
        if (totalCount <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) totalCount / pageSize);
    }

    public boolean hasNext() {
        return pageNumber < getTotalPages();
    }

    public boolean hasPrevious() {
        return pageNumber > 1;
    }

    // Getters
    public List<T> getRecords() {
        return Collections.unmodifiableList(records);
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public int getPageSize() {
        return pageSize;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }
}