package com.smart.adp.domain.model.drive.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;

import java.util.List;

/**
 * @Description: 试驾gateway
 * @Author: rik.ren
 * @Date: 2025/3/15 15:26
 **/
public interface TestCarPrepareGateway {

    /**
     * 查询试驾车
     *
     * @param param
     * @param columns
     * @return
     */
    List<TestCarPrepareBO> queryTestCarPrepareList(TestCarPrepareBO param, QueryColumn... columns);

    /**
     * 更新试驾车信息
     *
     * @param param
     * @return
     */
    Boolean modifyTestCarPrepare(TestCarPrepareBO param);

    /**
     * 更新试驾车信息
     *
     * @param param
     * @return
     */
    Boolean modifyTestCarPrepareForStop(TestCarPrepareBO param);

    /**
     * 根据条件获取试驾车信息
     *
     * @param param
     * @param columns
     * @return
     */
    TestCarPrepareBO queryTestCarPrepare(TestCarPrepareBO param, QueryColumn... columns);

}
