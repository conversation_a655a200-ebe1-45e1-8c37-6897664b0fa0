package com.smart.adp.domain.common.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 分布式锁注解
 * @Author: rik.ren
 * @Date: 2025/6/03 15:43
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SmartDistributedLock {
    /**
     * 锁的key，支持SpEL表达式
     * SpEL中的值必须不可为空
     */
    String key();

    /**
     * 锁的过期时间，默认10秒
     */
    long expire() default 5;

    /**
     * 过期时间单位，默认秒
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 获取锁失败时的提示信息
     */
    String message() default "系统繁忙，请稍后再试";

    /**
     * 是否阻塞等待锁，阻塞就是不断尝试获取锁，非阻塞就是尝试获取一次，没拿到就返回不等待
     */
    boolean block() default false;

    /**
     * 如果上面block是true时，该属性生效，阻塞等待超时时间（秒），默认2秒
     */
    long waitTime() default 2;

    /**
     * 和业务无关，只是为了方便日志打印，记录日志时，会打印该字段，方便定位问题
     *
     * @return
     */
    String logFlag() default "尝试获取锁";
}