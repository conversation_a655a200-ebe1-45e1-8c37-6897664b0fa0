package com.smart.adp.domain.model.drive.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.model.drive.entity.SacTestDriveLongApplyEntity;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveLongApplyGateway;
import com.smart.adp.domain.model.drive.service.SacTestDriveLongApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 超长试驾申请表service实现
 * @Author: rik.ren
 * @Date: 2025/5/22 17:11
 **/
@Service
public class SacTestDriveLongApplyServiceImpl implements SacTestDriveLongApplyService {
    @Autowired
    private SacTestDriveLongApplyGateway sacTestDriveLongApplyGateway;

    /**
     * 根据条件查询超长试驾申请信息
     *
     * @param param
     * @return
     */
    @Override
    public List<SacTestDriveLongApplyEntity> queryTestDriveLongApplyByCondition(SacTestDriveLongApplyEntity param) {
        if(ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        return sacTestDriveLongApplyGateway.queryTestDriveLongApplyByCondition(param);
    }

    /**
     * 根据条件查询超长试驾申的个数
     *
     * @param param
     * @return
     */
    @Override
    public Long queryTestDriveLongApplyCountByCondition(SacTestDriveLongApplyEntity param) {
        if(ObjectUtil.isEmpty(param)) {
            return 0L;
        }
        return sacTestDriveLongApplyGateway.queryTestDriveLongApplyCountByCondition(param);
    }
}
