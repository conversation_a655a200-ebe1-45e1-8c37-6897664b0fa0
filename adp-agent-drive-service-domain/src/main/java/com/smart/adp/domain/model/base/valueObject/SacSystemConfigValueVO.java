package com.smart.adp.domain.model.base.valueObject;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/6 13:23
 * @description 系统配置值表
 **/
@Table(value = "t_sac_system_config_value", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SacSystemConfigValueVO {
    /**
     * 配置值ID
     */
    @Column(value = "CONFIG_VALUE_ID")
    private String configValueId;

    /**
     * 配置ID
     */
    @Column(value = "CONFIG_ID")
    private String configId;

    /**
     * 所属组织编码，厂家为PV，店端为网点编码
     */
    @Column(value = "ORG_CODE")
    private String orgCode;

    /**
     * 所属组织名称
     */
    @Column(value = "ORG_NAME")
    private String orgName;

    /**
     * 值编码
     */
    @Column(value = "VALUE_CODE")
    private String valueCode;

    /**
     * 值名称
     */
    @Column(value = "VALUE_NAME")
    private String valueName;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnabled;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;
}