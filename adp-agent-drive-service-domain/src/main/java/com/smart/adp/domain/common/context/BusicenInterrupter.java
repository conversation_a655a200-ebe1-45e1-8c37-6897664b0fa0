package com.smart.adp.domain.common.context;

import java.util.Map;

public class BusicenInterrupter {
	
	@SuppressWarnings("rawtypes")
	public static IBusicenInterrupter INTERRUPTER;
	
	@SuppressWarnings("unchecked")
	public static Object enter(String resource,String type,Map<String,Object> context) throws Exception {
		if(INTERRUPTER!=null) {
			return INTERRUPTER.enter(resource, type, context);
		}else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	public static void exit(Object entry) {
		if(INTERRUPTER!=null) {
			INTERRUPTER.exit(entry);
		}
	}
	
	@SuppressWarnings("unchecked")
	public static void error(Object entry,Throwable throwable) {
		if(INTERRUPTER!=null) {
			INTERRUPTER.error(entry,throwable);
		}
	}
	
	public static interface IBusicenInterrupter<E>{
		
		E enter(String resource,String type,Map<String,Object> context) throws Exception;
		
		void exit(E e);
		
		void error(E e,Throwable throwable);
	}
	

}
