package com.smart.adp.domain.model.drive.validation.modifyTestDrive;

import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.entity.SacTestDriveLongApplyEntity;
import com.smart.adp.domain.model.drive.service.SacAppointmentSheetService;
import com.smart.adp.domain.model.drive.service.SacTestDriveLongApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @Description: 修改预约单预约人数阈值校验
 * 试驾参数校验处理器（具体校验实现示例）
 * 功能：校验试驾类型参数是否合法
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Service
@Slf4j
public class ModifyAppointmentSheetCheckThresholdValueValidation extends AbstractValidationHandler<SacTestDriveSheetBO> {

    @Resource
    private SacAppointmentSheetService sacAppointmentSheetService;
    @Resource
    private SacTestDriveLongApplyService sacTestDriveLongApplyService;

    @Override
    protected void doValidate(SacTestDriveSheetBO context) throws BusicenException {
        checkThresholdValue(context);
    }

    /**
     * 预约人数阈值校验
     *
     * @param context
     */
    private void checkThresholdValue(SacTestDriveSheetBO context) {
        try {
            // 查找同一时间这辆车已经被预约了多少人
            SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
            appointmentSheetBO.setDlrCode(context.getDlrCode());
            appointmentSheetBO.setPlateNumber(context.getPlateNumber());
            appointmentSheetBO.setListTestStatus(Arrays.asList(TestDriveStatusEnum.NOT_STARTED.getCode(),
                    TestDriveStatusEnum.IN_PROGRESS.getCode()));
            appointmentSheetBO.setAppointmentStartTime(context.getAppointmentStartTime());
            appointmentSheetBO.setAppointmentEndTime(context.getAppointmentEndTime());
            appointmentSheetBO.setNotEqAppointmentId(context.getAppointmentId());
            Long sacAppointmentSheetBOS =
                    sacAppointmentSheetService.queryAppointCountByCondition(appointmentSheetBO);
            log.info("修改试驾单查询该时间段已预约的信息 {}条", sacAppointmentSheetBOS);
            if (sacAppointmentSheetBOS >= Long.parseLong(context.getConfigValueDriveNum())) {
                throw new BusicenException("该时间段已约满，请重新选择");
            }

            //查看这辆车这段时间内有没有被超长出库
            SacTestDriveLongApplyEntity longApplyEntity =
                    SacTestDriveLongApplyEntity.builder().carLicenceNo(context.getPlateNumber()).build();
            Long longResult = sacTestDriveLongApplyService.queryTestDriveLongApplyCountByCondition(longApplyEntity);
            log.info("查询该时间段该车辆被超长出库的信息 {}条", longResult);
            if (longResult > 0L) {
                throw new BusicenException("该车辆这段时间被超长出库，不能预约");
            }
        } catch (Exception e) {
            log.error("修改试乘试驾单校验预约阈值报错", e);
            throw e;
        }
    }
}