package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 试驾类状态枚举
 * @Author: rik.ren
 * @Date: 2025/4/17 14:55
 */
@Getter
@AllArgsConstructor
public enum TestDriveStatusEnum {

    /**
     * 已取消
     */
    CANCELLED("-1", "已取消"),
    /**
     * 未开始
     */
    NOT_STARTED("0", "未开始"),
    /**
     * 试乘试驾中
     */
    IN_PROGRESS("1", "试乘试驾中"),
    /**
     * 已结束
     */
    COMPLETED("2", "已结束"),
    /**
     * 已过期
     */
    EXPIRED("3", "已过期"),
    ;

    private final String code;
    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static TestDriveStatusEnum getByCode(String code) {
        return Arrays.stream(TestDriveStatusEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
