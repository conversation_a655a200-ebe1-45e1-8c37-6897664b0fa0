package com.smart.adp.domain.model.base.service;

import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsBO;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsEventBO;

/**
 * @Description: adp推送信息给cdp
 * @Author: rik.ren
 * @Date: 2025/5/29 17:56
 **/
public interface CdpLeadsService {
    /**
     * 保存数据
     *
     * @param entity
     * @return
     */
    Boolean saveData(IfsBaseCdpLeadsBO entity);

    Boolean saveDataEvent(IfsBaseCdpLeadsEventBO boParam);
}
