package com.smart.adp.domain.utils;

import com.mybatisflex.core.util.CollectionUtil;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;

import java.util.Objects;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/10
 */
public class UserUtil {

    public static final Set<String> STORE_MANAGER_STATION_ID_SET =
            CollectionUtil.newHashSet("smart_bm_0005", "smart_bm_0016");

    public static final Set<String> PRODUCT_EXPERT_STATION_ID_SET =
            CollectionUtil.newHashSet("smart_bm_0007", "smart_bm_0018", "smart_bm_0061", "smart_bm_0064");

    public static final Set<String> NORMAL_PRODUCT_EXPERT_STATION_ID_SET =
            CollectionUtil.newHashSet("smart_bm_0007", "smart_bm_0018");

    /**
     * 是否店长
     *
     * @param stationId 岗位 id
     * @return boolean
     * 对应字典配置lookUpTypeCode:ADP_CLUE_045
     */
    public static boolean isStoreManager(String stationId) {
        return STORE_MANAGER_STATION_ID_SET.contains(stationId);
    }

    /**
     * 是否产品专家
     *
     * @param stationId 岗位 id
     * @return boolean
     */
    public static boolean isProductExpert(String stationId) {
        return PRODUCT_EXPERT_STATION_ID_SET.contains(stationId);
    }

    /**
     * 是否普通产品专家
     *
     * @param stationId 岗位 id
     * @return boolean
     */
    public static boolean isNormalProductExpert(String stationId) {
        return NORMAL_PRODUCT_EXPERT_STATION_ID_SET.contains(stationId);
    }

    /**
     * 检查是否店端
     */
    public static void checkDlr() {
        checkDlr(UserInfoContext.get());
    }

    /**
     * 检查是否店端
     *
     * @param user 用户信息
     */
    public static void checkDlr(UserBusiEntity user) {
        RespCode respCode = RespCode.UNAUTHORIZED;
        if (StringUtil.noText(user.getDlrCode())) {
            throw new BusinessException(respCode.getCode(), "门店编码为空");
        }
        if (!STORE_MANAGER_STATION_ID_SET.contains(user.getStationId())
                && !PRODUCT_EXPERT_STATION_ID_SET.contains(user.getStationId())) {
            throw new BusinessException(respCode.getCode(), "非店端岗位");
        }
    }

    /**
     * 当前用户是否产品专家
     *
     */
    public static boolean productExpertValid() {
        UserBusiEntity user = UserInfoContext.get();
        return isProductExpert(user.getStationId()) && Objects.nonNull(user.getUserID()) && !user.getUserID().isEmpty();
    }
}
