package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 试驾车状态
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023-05-16 16:11
 */
@Getter
@AllArgsConstructor
public enum TestCarStatusEnum {

    /**
     * 整备审批中
     */
    PREPARATION_PENDING("0", "整备审批中"),
    /**
     * 服役中
     */
    IN_SERVICE("1", "服役中"),
    /**
     * 整备驳回
     */
    PREPARATION_REJECTED("2", "整备驳回"),
    /**
     * 退役审批中
     */
    RETIREMENT_PENDING("3", "退役审批中"),
    /**
     * 退役通过
     */
    RETIREMENT_APPROVED("4", "退役通过"),
    /**
     * 退役驳回
     */
    RETIREMENT_REJECTED("5", "退役驳回"),
    /**
     * 特殊退役审批中
     */
    SPECIAL_RETIREMENT_PENDING("6", "特殊退役审批中"),
    /**
     * 特殊退役驳回
     */
    SPECIAL_RETIREMENT_REJECTED("7", "特殊退役驳回"),
    /**
     * 特殊退役通过
     */
    SPECIAL_RETIREMENT_APPROVED("8", "特殊退役通过");;

    private final String code;
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static TestCarStatusEnum getByCode(String code) {
        return Arrays.stream(TestCarStatusEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
