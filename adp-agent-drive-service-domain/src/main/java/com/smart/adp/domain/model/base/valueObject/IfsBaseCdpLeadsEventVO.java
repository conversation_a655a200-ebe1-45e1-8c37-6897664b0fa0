package com.smart.adp.domain.model.base.valueObject;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * adp事件创建发送cdp（adp->cdp）
 *
 * @TableName t_ifs_base_cdp_leads_event
 */
@Table(value = "t_ifs_base_cdp_leads_event", schema = "interfacecenter")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IfsBaseCdpLeadsEventVO {
    /**
     * 主键ID
     */
    @Column(value = "logs_id")
    private String logsId;

    /**
     * 主键 手机号
     */
    @Column(value = "bk")
    private String bk;

    /**
     * 事件名称
     */
    @Column(value = "event")
    private String event;

    /**
     * 事件产生时间
     */
    @Column(value = "date")
    private String date;

    /**
     * 试驾时间段
     */
    @Column(value = "c_time_period")
    private String cTimePeriod;

    /**
     * 试驾时间
     */
    @Column(value = "c_book_date")
    private String cBookDate;

    /**
     * 试驾车型
     */
    @Column(value = "c_car_model")
    private String cCarModel;

    /**
     * 事件类型
     */
    @Column(value = "c_driver_type")
    private String cDriverType;

    /**
     * 跟进人员名称
     */
    @Column(value = "c_staff_name")
    private String cStaffName;

    /**
     * 作业日期
     */
    @Column(value = "c_process_date")
    private String cProcessDate;

    /**
     * 数据所属门店名称
     */
    @Column(value = "c_store_name")
    private String cStoreName;

    /**
     * 订单编号
     */
    @Column(value = "c_orderNo")
    private String cOrderNo;

    /**
     * 一级渠道
     */
    @Column(value = "c_first_channel")
    private String cFirstChannel;

    /**
     * 二级渠道
     */
    @Column(value = "c_second_channel")
    private String cSecondChannel;

    /**
     * 三级渠道
     */
    @Column(value = "c_third_channel")
    private String cThirdChannel;

    /**
     * 门店名称
     */
    @Column(value = "c_store")
    private String cStore;

    /**
     * 门店编码
     */
    @Column(value = "c_store_code")
    private String cStoreCode;

    /**
     * 创建时间
     */
    @Column(value = "c_create_time")
    private String cCreateTime;

    /**
     * 插入日期
     */
    @Column(value = "insert_date")
    private LocalDateTime insertDate;

    /**
     * 发送状态 默认0
     */
    @Column(value = "send_flag")
    private String sendFlag;

    /**
     * 错误日志
     */
    @Column(value = "err_log")
    private String errLog;

    /**
     * 发送日期
     */
    @Column(value = "send_date")
    private LocalDateTime sendDate;

    /**
     * 原因
     */
    @Column(value = "c_reason")
    private String cReason;

    /**
     * 活动名称
     */
    @Column(value = "c_activity_name")
    private String cActivityName;

    /**
     * 车主角色
     */
    @Column(value = "c_activity_variety")
    private String cActivityVariety;

    /**
     * 试驾车型
     */
    @Column(value = "c_car_type")
    private String cCarType;
}