package com.smart.adp.domain.model.base.bo;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.model.base.valueObject.SacSystemConfigVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import static com.smart.adp.domain.model.base.valueObject.table.SacSystemConfigVOTableDef.SAC_SYSTEM_CONFIG_VO;

/**
 * <AUTHOR>
 * date 2025/3/6 13:23
 * @description 系统配置表
 **/
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SacSystemConfigBO extends SacSystemConfigVO {

    private static final long serialVersionUID = 1L;

    /**
     * 所属组织编码，厂家为PV，店端为网点编码
     */
    private String orgCode;

    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 值编码
     */
    private String valueCode;

    /**
     * 值名称
     */
    private String valueName;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacSystemConfigBO buildConditions(QueryWrapper wrapper) {
        wrapper.and(SAC_SYSTEM_CONFIG_VO.CONFIG_ID.eq(getConfigId(), StringUtil::hasText))
                .and(SAC_SYSTEM_CONFIG_VO.CONFIG_RANGE.eq(getConfigRange(), StringUtil::hasText))
                .and(SAC_SYSTEM_CONFIG_VO.ORG_CODE.eq(getOrgCode(), StringUtil::hasText))
                .and(SAC_SYSTEM_CONFIG_VO.CONFIG_CODE.eq(getConfigCode(), StringUtil::hasText));
        return this;
    }

    public SacSystemConfigBO buildBO(String configCode) {
        setConfigCode(configCode);
        return this;
    }
}
