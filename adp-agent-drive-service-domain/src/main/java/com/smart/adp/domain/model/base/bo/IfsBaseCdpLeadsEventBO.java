package com.smart.adp.domain.model.base.bo;

import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsEventVO;
import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * adp自建线索创建发送cdp（adp->cdp）
 *
 * @TableName t_ifs_base_cdp_leads_event
 */
@Data
public class IfsBaseCdpLeadsEventBO extends IfsBaseCdpLeadsEventVO {

    /**
     * 构建取消试驾任务试驾时，推送cdp消息的对象bo
     *
     * @param custPhone
     *
     * @return
     */
    public IfsBaseCdpLeadsEventBO buildCancelTestTaskCdpLeadBO(String custPhone) {
        setBk(custPhone);
        setEvent("c_cancel_drive");
        setLogsId(UUID.randomUUID().toString());
        setInsertDate(LocalDateTime.now());
        setSendDate(LocalDateTime.now());
        setSendFlag("0");
        return this;
    }
}