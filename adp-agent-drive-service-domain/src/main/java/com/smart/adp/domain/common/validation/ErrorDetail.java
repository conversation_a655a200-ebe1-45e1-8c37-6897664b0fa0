package com.smart.adp.domain.common.validation;

import lombok.Data;

/**
 * @Description: 错误详情封装类
 * @Author: rik.ren
 * @Date: 2025/5/20 16:38
 **/
@Data
public class ErrorDetail {
    private String errorCode;
    private String errorMessage;
    private Object[] params; // 可选参数，用于消息格式化

    public ErrorDetail(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    // 带格式化参数的构造方法
    public ErrorDetail(String errorCode, String errorMessage, Object... params) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.params = params;
    }
}