package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 试驾任务状态枚举
 * @Author: rik.ren
 * @Date: 2025/3/16 14:55
 **/
@Getter
@AllArgsConstructor
public enum TestTaskStatusCodeEnum {

    /**
     * 未开始
     */
    UN_FINISHED("0", "未完成"),

    /**
     * 已完成
     */
    FINISHED("1", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("2", "已取消"),

    /**
     * 未分配
     */
    UN_DISTRIBUTE("3", "未分配"),

    /**
     * 已分配
     */
    DISTRIBUTE("4", "已分配"),
    ;

    private final String code;
    private final String desc;
}
