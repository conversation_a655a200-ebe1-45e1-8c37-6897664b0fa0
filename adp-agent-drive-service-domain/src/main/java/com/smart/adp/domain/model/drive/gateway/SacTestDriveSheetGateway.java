package com.smart.adp.domain.model.drive.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.drive.entity.SacTestDriveSheetEntity;

import java.util.List;
import java.util.Map;

/**
 * @Description: 试驾gateway
 * @Author: rik.ren
 * @Date: 2025/3/15 15:26
 **/
public interface SacTestDriveSheetGateway {
    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<SacTestDriveSheetBO> queryTestDriveSheet(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                        QueryOrderBy orderBy,
                                                        QueryColumn... columns);

    /**
     * 查询试驾单
     *
     * @param boParam
     * @param boClueParam
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<SacTestDriveSheetBO> queryTestDriveSheet(SacTestDriveSheetBO boParam, SacClueInfoDlrBO boClueParam,
                                                        QueryOrderBy orderBy, QueryColumn... columns);

    /**
     * 根据条件查询试驾单满足条件的总数
     *
     * @param boParam
     * @param boClueParam
     * @return
     */
    Long queryTestDriveSheetCount(SacTestDriveSheetBO boParam, SacClueInfoDlrBO boClueParam);

    /**
     * 根据条件查询一条试乘试驾单
     *
     * @param boParam
     * @param columns
     * @return
     */
    SacTestDriveSheetBO queryTestDriveSheet(SacTestDriveSheetBO boParam, QueryColumn... columns);

    /**
     * 查询试驾单
     *
     * @param param
     * @param boParam
     * @param orderBy
     * @param columns
     * @return
     */
    List<SacTestDriveSheetBO> queryTestDriveSheetList(SacTestDriveSheetEntity param, SacTestDriveSheetBO boParam,
                                                      QueryOrderBy orderBy,
                                                      QueryColumn... columns);

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    Boolean updateTestDriveSheet(SacTestDriveSheetBO param);

    /**
     * 插入试驾单
     *
     * @param param
     * @return
     */
    Boolean insertTestDriveSheet(SacTestDriveSheetEntity param);

    /**
     * 未完成试驾任务数量
     *
     * @param user 当前用户
     * @return long
     */
    long toDoTestDriveTaskNum(UserBusiEntity user);

    /**
     * 未完成试驾单数量
     *
     * @param user 当前用户
     * @return long
     */
    long toDoTestDriveNum(UserBusiEntity user);

    /**
     * 发送试驾短信
     *
     * @param param
     * @return
     */
    Boolean sendTestDriveMsg(Map<String, Object> param);

    /**
     * 删除试乘试驾单
     *
     * @param param
     * @return
     */
    Boolean delTestDriveSheet(SacTestDriveSheetBO param);

    /**
     * 试驾结束时记录接口表，获取BI车机数据
     *
     * @param param
     * @return
     */
    Boolean vehicleData(Map<String, Object> param);

    /**
     * 试驾结束发送ZTMQ
     *
     * @param param
     * @return
     */
    Boolean sendZTMQ(Map<String, Object> param);
}
