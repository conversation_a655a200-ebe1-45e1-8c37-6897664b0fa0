package com.smart.adp.domain.model.drive.gateway;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import com.smart.adp.domain.qry.DriveTaskQry;
import com.smart.adp.domain.vo.DriveTaskVO;

import java.util.List;

/**
 * @Description: 试驾任务gateway
 * @Author: rik.ren
 * @Date: 2025/6/30 13:54
 **/
public interface SacTestDriveTaskGateway {
    /**
     * 创建试驾任务信息
     *
     * @param param
     * @return
     */
    Boolean createTestDriveTask(SacTestDriveTaskBO param);

    /**
     * 修改试驾任务信息
     *
     * @param param
     * @return
     */
    Boolean modifyTestDriveTask(SacTestDriveTaskBO param);

    /**
     * 根据条件查询一条试驾任务
     *
     * @param param
     * @return
     */
    SacTestDriveTaskBO getTestDriveTask(SacTestDriveTaskBO param, QueryColumn... columns);

    /**
     * 根据条件查询试驾任务集合
     *
     * @param param
     * @return
     */
    List<SacTestDriveTaskBO> queryTestDriveTaskList(SacTestDriveTaskBO param, QueryColumn... columns);

    /**
     * 分页查询
     *
     * @param param
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<SacTestDriveTaskBO> queryTestDriveTaskPage(SacTestDriveTaskBO param, QueryOrderBy orderBy, QueryColumn... columns);

    /**
     * 查询满足条件的试驾任务个数
     *
     * @param param
     * @return
     */
    Long queryTestDriveTaskCount(SacTestDriveTaskBO param);

    /**
     * 试驾任务分页查询
     *
     * @param qry -
     * @return page
     */
    Page<DriveTaskVO> page(DriveTaskQry qry);

    /**
     * 试驾任务批量查询
     *
     * @param qry -
     * @return list
     */
    List<DriveTaskVO> list(DriveTaskQry qry);

    /**
     * 试驾任务是否存在
     *
     * @param qry -
     * @return boolean
     */
    boolean existByQry(DriveTaskQry qry);
}
