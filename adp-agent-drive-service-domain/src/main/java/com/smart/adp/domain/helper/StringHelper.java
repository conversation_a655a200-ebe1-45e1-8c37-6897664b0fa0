package com.smart.adp.domain.helper;

import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> huangjun
 * @Description: 字符处理工具类
 * @date Date : 2019/08/29
 */
public class StringHelper {

    /**
     * @Description: 将形如：1,2,3的字符串转为'1','2','3'
     * <AUTHOR> huangjun
     * @date Date : 2019/08/29
     */
    public static String convert(String sourceStr) {
        StringBuilder realId = new StringBuilder();
        if (!StringUtils.isEmpty(sourceStr)) {
            for (String orgId : sourceStr.split(",")) {
                if (realId.length() > 0) {
                    realId.append(",").append("'").append(orgId).append("'");
                } else {
                    realId.append("'").append(orgId).append("'");
                }
            }
        }
        return realId.toString();
    }

    public static String GetGUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static Boolean IsEmptyOrNull(Object obj) {
        if (obj == null) {
            return true;
        } else if ("".equals(obj)) {
            return true;
        } else {
            if (obj instanceof String) {
                if (((String) obj).length() == 0) {
                    return true;
                }
            } else if (obj instanceof Collection) {
                if (((Collection) obj).size() == 0) {
                    return true;
                }
            } else if (obj instanceof Map && ((Map) obj).size() == 0) {
                return true;
            }

            return false;
        }
    }

}
