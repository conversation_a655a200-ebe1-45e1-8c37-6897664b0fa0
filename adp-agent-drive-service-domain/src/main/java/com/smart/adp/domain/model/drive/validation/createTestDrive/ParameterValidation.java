package com.smart.adp.domain.model.drive.validation.createTestDrive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveMethodEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Description: 试乘试驾聚合根的返回结果基础接口
 * 试驾参数校验处理器（具体校验实现示例）
 * 功能：校验试驾类型参数是否合法
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Slf4j
@Service
public class ParameterValidation extends AbstractValidationHandler<SacTestDriveSheetBO> {

    @Override
    protected void doValidate(SacTestDriveSheetBO context) throws BusicenException {
        if (ObjectUtil.isEmpty(TestDriveTypeEnum.getByCode(context.getTestType()))) {
            throw new BusicenException("预约类型错误");
        }
        if (ObjectUtil.isEmpty(TestDriveMethodEnum.getByCode(context.getTestDriveMethod()))) {
            throw new BusicenException("试驾方式错误");
        }
        if (TestDriveTypeEnum.TEST_RIDE.getCode().equals(context.getTestType())
                || TestDriveTypeEnum.TEST_DRIVE.getCode().equals(context.getTestType())) {
            if (StringUtils.isEmpty(context.getAppointmentTestDate()) || StringUtils.isEmpty(context.getAppointmentTestTime())) {
                throw new BusicenException("预约日期或时间不能为空");
            }
        } else {
            if (StringUtils.isEmpty(context.getAppointmentStartTime()) || StringUtils.isEmpty(context.getAppointmentEndTime())) {
                throw new BusicenException("预约时间不能为空");
            }
        }
    }
}