package com.smart.adp.domain.model.drive.bo;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.model.drive.entity.SacAppointmentSheetEntity;
import lombok.*;

import java.util.List;
import java.util.Objects;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * 试乘试驾预约单表的BO。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SacAppointmentSheetBO extends SacAppointmentSheetEntity {

    /**
     * 试乘试驾单状态集合
     */
    private List<String> listTestStatus;

    /**
     * 试乘试驾单状态
     */
    private String testStatus;

    /**
     * 不等于试乘试驾预约单id
     */
    private String notEqAppointmentId;

    /**
     * 预约超长试驾开始时间
     */
    private String appointmentStartTimeRepeat;

    /**
     * 预约超长试驾结束时间
     */
    private String appointmentEndTimeRepeat;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacAppointmentSheetBO buildCondition(QueryWrapper wrapper) {
        wrapper.and(SAC_APPOINTMENT_SHEET_ENTITY.DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.DLR_CLUE_ORDER_NO.eq(getDlrClueOrderNo(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID.eq(getAppointmentId(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.CUSTOMER_ID.eq(getCustomerId(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.CUSTOMER_PHONE.eq(getCustomerPhone(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ORDER_NO.eq(getAppointmentOrderNo(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.PLATE_NUMBER.eq(getPlateNumber(), StringUtil::hasText))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.CAR_VIN.eq(getCarVin(), StringUtil::hasText))
                .and((SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME.ge(getAppointmentStartTime(), Objects::nonNull))
                        .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME.le(getAppointmentEndTime(), Objects::nonNull)))
                .and(SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS.in(getListTestStatus(), CollectionUtil::isNotEmpty))
                .and((SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME.lt(getAppointmentEndTimeRepeat(), Objects::nonNull))
                        .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME.gt(getAppointmentStartTimeRepeat(), Objects::nonNull)))
                .and(SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID.ne(getNotEqAppointmentId(), StringUtil::hasText));
        return this;
    }

    /**
     * 构建修改预约单信息对象
     *
     * @param driveSheetBO
     * @return
     */
    public SacAppointmentSheetBO buildModifyBO(SacTestDriveSheetBO driveSheetBO) {
        SacAppointmentSheetBO modifyAppointmentSheetBO = new SacAppointmentSheetBO();
        modifyAppointmentSheetBO.setAppointmentId(driveSheetBO.getAppointmentId());
        modifyAppointmentSheetBO.setTestType(driveSheetBO.getTestType());//对应线索的serverOrder
        modifyAppointmentSheetBO.setSmallCarTypeCode(driveSheetBO.getSmallCarTypeCode());
        modifyAppointmentSheetBO.setSmallCarTypeName(driveSheetBO.getSmallCarTypeName());
        modifyAppointmentSheetBO.setAppointmentTestDate(driveSheetBO.getAppointmentTestDate());
        modifyAppointmentSheetBO.setAppointmentTestTime(driveSheetBO.getAppointmentTestTime());
        modifyAppointmentSheetBO.setAppointmentStartTime(driveSheetBO.getAppointmentStartTime());
        modifyAppointmentSheetBO.setAppointmentEndTime(driveSheetBO.getAppointmentEndTime());
        modifyAppointmentSheetBO.setPlateNumber(driveSheetBO.getPlateNumber());
        modifyAppointmentSheetBO.setCarVin(driveSheetBO.getCarVin());
        modifyAppointmentSheetBO.setLastUpdatedDate(driveSheetBO.getLastUpdatedDate());
        modifyAppointmentSheetBO.setModifier(driveSheetBO.getModifier());
        modifyAppointmentSheetBO.setModifyName(driveSheetBO.getModifyName());
        return modifyAppointmentSheetBO;
    }

    /**
     * 构建开始试驾预对约单操作的字段对象
     *
     * @param driveSheetBO
     * @return
     */
    public SacAppointmentSheetBO buildStartDrivingModifyBO(SacTestDriveSheetBO driveSheetBO) {
        SacAppointmentSheetBO modifyAppointmentSheetBO = new SacAppointmentSheetBO();
        modifyAppointmentSheetBO.setAppointmentId(driveSheetBO.getAppointmentId());
        modifyAppointmentSheetBO.setIsTestDrive("1");
        modifyAppointmentSheetBO.setLastUpdatedDate(driveSheetBO.getLastUpdatedDate());
        modifyAppointmentSheetBO.setModifier(driveSheetBO.getModifier());
        modifyAppointmentSheetBO.setModifyName(driveSheetBO.getModifyName());
        return modifyAppointmentSheetBO;
    }
}
