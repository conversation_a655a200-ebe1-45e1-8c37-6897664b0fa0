package com.smart.adp.domain.common;

import com.mybatisflex.core.paginate.Page;
import lombok.*;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
@Getter
@Setter
@ToString
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class PageVO<T> {

    /**
     * data
     */
    private List<T> records = Collections.emptyList();


    private long pageNumber = 1;
    private long pageSize = 10;
    private long totalPage = -1;
    private long totalRow = -1;

    public static <T> PageVO<T> of(Page<T> flexPage) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setRecords(flexPage.getRecords());
        pageVO.setPageNumber(flexPage.getPageNumber());
        pageVO.setPageSize(flexPage.getPageSize());
        pageVO.setTotalRow(flexPage.getTotalRow());
        pageVO.setTotalPage(flexPage.getTotalPage());
        return pageVO;
    }

    public static <T> PageVO<T> of(List<T> records) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setRecords(records);
        return pageVO;
    }

    public static <T> PageVO<T> of(List<T> records, long pageNumber, long pageSize) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setRecords(records);
        pageVO.setPageNumber(pageNumber);
        pageVO.setPageSize(pageSize);
        return pageVO;
    }

    public static <T> PageVO<T> of(List<T> records, long pageNumber, long pageSize, long totalRow) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setRecords(records);
        pageVO.setPageNumber(pageNumber);
        pageVO.setPageSize(pageSize);
        pageVO.setTotalRow(totalRow);
        return pageVO;
    }

    private void calcTotalPage() {
        if (pageSize < 0 || totalRow < 0) {
            totalPage = -1;
        } else {
            totalPage = (totalRow + pageSize - 1) / pageSize;
        }
    }

    public long getTotalPage() {
        calcTotalPage();
        return this.totalPage;
    }
}
