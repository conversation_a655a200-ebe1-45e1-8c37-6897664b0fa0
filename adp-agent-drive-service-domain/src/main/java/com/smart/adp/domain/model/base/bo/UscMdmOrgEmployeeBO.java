package com.smart.adp.domain.model.base.bo;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.model.base.entity.UscMdmOrgEmployeeEntity;
import lombok.Data;

import java.util.List;

import static com.smart.adp.domain.model.base.entity.table.UscMdmOrgEmployeeEntityTableDef.USC_MDM_ORG_EMPLOYEE_ENTITY;

/**
 * 职员信息 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
public class UscMdmOrgEmployeeBO extends UscMdmOrgEmployeeEntity {

    public UscMdmOrgEmployeeBO(List<String> listUserId) {
        this.listUserId = listUserId;
    }

    /**
     * 用户ID集合
     */
    private List<String> listUserId;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public UscMdmOrgEmployeeEntity buildCondition(QueryWrapper wrapper) {
        wrapper.and(USC_MDM_ORG_EMPLOYEE_ENTITY.USER_ID.eq(getUserId(), StringUtil::hasText))
                .and(USC_MDM_ORG_EMPLOYEE_ENTITY.USER_ID.in(getListUserId(), CollectionUtil::isNotEmpty))
                .and(USC_MDM_ORG_EMPLOYEE_ENTITY.EMP_CODE.eq(getEmpCode(), StringUtil::hasText))
                .and(USC_MDM_ORG_EMPLOYEE_ENTITY.DLR_CODE.eq(getDlrCode(), StringUtil::hasText));
        return this;
    }

}
