package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 试驾车状态
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023-05-16 16:11
 */
@Getter
@AllArgsConstructor
public enum TestCarStatusCodeEnum {

    /**
     * 整备审批中
     */
    FREE(0, "空闲中"),
    /**
     * 服役中
     */
    DRIVING(1, "试驾中"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static TestCarStatusCodeEnum getByCode(Integer code) {
        return Arrays.stream(TestCarStatusCodeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
