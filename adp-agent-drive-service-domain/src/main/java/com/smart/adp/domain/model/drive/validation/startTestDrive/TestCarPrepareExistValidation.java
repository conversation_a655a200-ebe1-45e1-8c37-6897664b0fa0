package com.smart.adp.domain.model.drive.validation.startTestDrive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;
import com.smart.adp.domain.model.drive.service.ITestCarPrepareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 校验试乘试驾单是否存在
 * @Author: rik.ren
 * @Date: 2025/5/30 11:23
 **/
@Slf4j
@Service
public class TestCarPrepareExistValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {

    @Autowired
    private ITestCarPrepareService prepareService;

    /**
     * 具体校验逻辑抽象方法（由子类实现）
     *
     * @param context 校验上下文对象
     */
    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        TestCarPrepareBO queryPrepareResult =
                prepareService.queryTestCarPrepare(new TestCarPrepareBO().buildStartDrivingQueryBO(querySheetResult.getPlateNumber()));
        if (ObjectUtil.isEmpty(queryPrepareResult)) {
            throw new BusinessException(RespCode.FAIL.getCode(), "试驾车不存在");
        }
        context.putResult("queryPrepareResult", queryPrepareResult);
    }
}
