package com.smart.adp.domain.model.base;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.base.bo.*;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.base.service.BaseDataService;
import com.smart.adp.domain.model.base.service.CdpLeadsService;
import com.smart.adp.domain.model.base.service.LookUpInfoService;
import com.smart.adp.domain.model.base.service.SacSystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.smart.adp.domain.model.base.entity.table.UscMdmOrgEmployeeEntityTableDef.USC_MDM_ORG_EMPLOYEE_ENTITY;

/**
 * @Description: 基础配置、基础信息的聚合根
 * @Author: rik.ren
 * @Date: 2025/5/22 11:05
 **/
@Slf4j
@Service
public class BaseInfoAggregate {
    @Resource
    private SacSystemConfigService sacSystemConfigService;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private CdpLeadsService cdpLeadsService;
    @Resource
    private LookUpInfoService lookUpInfoService;

    public List<LookUpInfoBO> lookUpInfo(String lookUpTypeCode, String lookUpValueCode) {
        LookUpInfoBO bo = new LookUpInfoBO();
        bo.setLookUpTypeCode(lookUpTypeCode);
        bo.setLookUpValueCode(lookUpValueCode);
        List<LookUpInfoBO> result = lookUpInfoService.queryLookUpInfo(bo);
        return result;
    }

    public String ValueCode(String configCode, String defaultVal) {
        List<SacSystemConfigBO> sacSystemConfigBOS = queryConfigInfoByConfigCode(configCode);
        // 默认值
        return sacSystemConfigBOS.stream()
                .findFirst()
                .map(SacSystemConfigBO::getValueCode)
                .orElse(defaultVal);
    }

    /**
     * 根据条件查询系统配置信息
     *
     * @param configCode
     * @return
     */
    public List<SacSystemConfigBO> queryConfigInfoByConfigCode(String configCode) {
        if (StringUtils.isEmpty(configCode)) {
            return Collections.emptyList();
        }
        return sacSystemConfigService.queryConfigInfoByCondition(new SacSystemConfigBO().buildBO(configCode));
    }

    /**
     * 根据业务生成单号
     *
     * @param billTypeId
     * @return
     */
    public String generateOrderCode(String billTypeId, UserBusiEntity userBusiEntity) {
        if (StringUtils.isEmpty(billTypeId)) {
            return null;
        }
        return baseDataService.generateOrderCode(billTypeId, userBusiEntity);
    }

    /**
     * adp向cdp发送消息的表数据写入
     *
     * @param boParam
     * @return
     */
    public Boolean saveCdpLeads(IfsBaseCdpLeadsBO boParam) {
        boParam.setLogsId(UUID.randomUUID().toString());
        return cdpLeadsService.saveData(boParam);
    }

    /**
     * adp向cdp发送消息的表数据写入
     *
     * @param boParam
     * @return
     */
    public Boolean saveCdpLeadsEvent(IfsBaseCdpLeadsEventBO boParam) {
        return cdpLeadsService.saveDataEvent(boParam);
    }

    /**
     * 批量查询用户信息
     *
     * @param listUserId
     * @return
     */
    public List<UscMdmOrgEmployeeBO> queryUscMdmOrgEmp(List<String> listUserId) {
        QueryColumn[] needColumn = {USC_MDM_ORG_EMPLOYEE_ENTITY.EMP_CODE, USC_MDM_ORG_EMPLOYEE_ENTITY.USER_ID};
        return baseDataService.queryUscMdmOrgEmp(new UscMdmOrgEmployeeBO(listUserId), needColumn);
    }
}
