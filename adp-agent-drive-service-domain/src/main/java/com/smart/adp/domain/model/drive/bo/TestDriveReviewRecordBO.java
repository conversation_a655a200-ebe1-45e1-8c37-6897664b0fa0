package com.smart.adp.domain.model.drive.bo;


import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.model.drive.entity.SacTestDriveReviewRecordEntity;
import com.smart.adp.domain.model.drive.entity.SacTestDriveSheetEntity;
import lombok.*;

import java.util.List;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveReviewRecordEntityTableDef.SAC_TEST_DRIVE_REVIEW_RECORD_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.TestCarPrepareEntityTableDef.TEST_CAR_PREPARE_ENTITY;

/**
 * 试乘试驾跟进记录表
 */

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class TestDriveReviewRecordBO extends SacTestDriveReviewRecordEntity {

    private List<String> listTestDriveSheetId;

    public SacTestDriveReviewRecordEntity buildTestDriveReviewConditions(QueryWrapper wrapper) {
        wrapper.and(SAC_TEST_DRIVE_REVIEW_RECORD_ENTITY.TEST_DRIVE_SHEET_ID.eq(getTestDriveSheetId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_REVIEW_RECORD_ENTITY.TEST_DRIVE_SHEET_ID.in(getListTestDriveSheetId(), CollectionUtil::isNotEmpty));
        return this;
    }
}