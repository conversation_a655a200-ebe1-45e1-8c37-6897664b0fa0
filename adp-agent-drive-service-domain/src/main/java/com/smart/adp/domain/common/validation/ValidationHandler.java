package com.smart.adp.domain.common.validation;

import com.smart.adp.domain.common.context.BusicenException;

/**
 * @Description: 校验处理器接口，定义了责任链入口，
 * 由抽象类实现，具体校验逻辑由继承抽象类的子类来实现
 * @Author: rik.ren
 * @Date: 2025/5/20 16:38
 **/
public interface ValidationHandler<T> {
    /**
     * 执行校验逻辑（需实现类具体实现）
     *
     * @param context 校验上下文对象，包含需要校验的业务数据
     * @throws BusicenException 校验失败时抛出业务异常
     */
    void validate(T context) throws BusicenException;

    /**
     * 设置责任链的下一个处理器
     *
     * @param handler 下一个处理器实例
     * @return 返回下一个处理器实例，支持链式调用
     */
    ValidationHandler<T> setNext(ValidationHandler<T> handler);
}