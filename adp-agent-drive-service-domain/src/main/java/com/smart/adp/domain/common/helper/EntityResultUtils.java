package com.smart.adp.domain.common.helper;

import com.smart.adp.domain.common.resp.EntityResult;

public class EntityResultUtils {

    public static <T> EntityResult<T> success(T rows) {
        EntityResult<T> result = new EntityResult<>();
        result.setResult("1");
        result.setMsg("操作成功");
        result.setRows(rows);
        return result;
    }

    public static <T> EntityResult<T> fail(String msg) {
        EntityResult<T> result = new EntityResult<>();
        result.setResult("0");
        result.setMsg(msg);
        result.setRows(null);
        return result;
    }
}