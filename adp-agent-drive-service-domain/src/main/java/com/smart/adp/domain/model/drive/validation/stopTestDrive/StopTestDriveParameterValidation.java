package com.smart.adp.domain.model.drive.validation.stopTestDrive;

import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @Description: 结束试驾参数校验
 * 试驾参数校验处理器（具体校验实现示例）
 * 功能：校验试驾类型参数是否合法
 * @Author: rik.ren
 * @Date: 2025/6/03 13:24
 **/
@Slf4j
@Service
public class StopTestDriveParameterValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {

    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        // 如果开始试驾里程大于等于结束试驾里程，则抛异常
        if (querySheetResult.getTestStartRoadHaul().compareTo(context.getRequestParam().getTestEndRoadHaul()) >= 0) {
            throw new BusicenException("结束里程必须大于开始里程");
        }
        BigDecimal testRoadHaul = context.getRequestParam().getTestEndRoadHaul().subtract(querySheetResult.getTestStartRoadHaul());
        querySheetResult.setTestRoadHaul(testRoadHaul);
        context.putResult("querySheetResult", querySheetResult);
    }
}