package com.smart.adp.domain.model.drive.validation.stopTestDrive;

import com.smart.adp.domain.common.validation.ValidationHandler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.validation.common.TestDriveSheetExistValidation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 结束试驾试乘试驾校验链
 * 试驾校验链构建器
 * 创建预约单的校验链
 * @Author: rik.ren
 * @Date: 2025/6/03 18:04
 **/
@Slf4j
@Service
public class StopTestDriveSheetValidationChain {
    @Autowired
    TestDriveSheetExistValidation testDriveSheetExistValidation;
    @Autowired
    private StopDriveCarDrivingValidation stopDriveCarDrivingValidation;
    @Autowired
    StopTestDriveParameterValidation stopTestDriveParameterValidation;

    /**
     * 构建试驾校验责任链
     *
     * @return 返回组装好的校验处理器链（当前顺序：参数校验 -> 车辆是否在行驶中校验 -> 结束试驾参数校验）
     */
    public ValidationHandler<TestDriveAggregate.ValidationContext> buildValidationChain() {
        // 构建链式结构
        testDriveSheetExistValidation.setNext(stopDriveCarDrivingValidation);
        return testDriveSheetExistValidation;
    }
}