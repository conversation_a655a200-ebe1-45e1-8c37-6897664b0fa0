package com.smart.adp.domain.model.drive.validation.createTestDrive;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.service.SacAppointmentSheetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;

/**
 * @Description: 创建预约单查重
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Service
@Slf4j
public class AppointmentSheetCheckRepeatValidation extends AbstractValidationHandler<SacTestDriveSheetBO> {

    @Autowired
    private SacAppointmentSheetService sacAppointmentSheetService;

    @Override
    protected void doValidate(SacTestDriveSheetBO context) throws BusicenException {
        checkRepeat(context);
    }

    /**
     * 查重
     *
     * @param context
     */
    private void checkRepeat(SacTestDriveSheetBO context) {
        try {
            // 普通试乘试驾拼接开始时间与结束时间
            if (!TestDriveTypeEnum.DEEP_TEST.getCode().toString().equals(context.getTestType())) {
                String[] timeStrings = context.getAppointmentTestTime().split("-");
                String appointmentStartTime = context.getAppointmentTestDate().concat(" ").concat(timeStrings[0]).concat(":00");
                String appointmentEndTime = context.getAppointmentTestDate().concat(" ").concat(timeStrings[1]).concat(":00");
                context.setAppointmentStartTime(appointmentStartTime);
                context.setAppointmentEndTime(appointmentEndTime);
            } else {
                // 深度试驾处理
                // 提取日期和时间
                String appointmentTestDate = "";
                String appointmentTestTime = "";

                // 获取开始和结束时间
                String startTimeStr = context.getAppointmentStartTime();
                String endTimeStr = context.getAppointmentEndTime();

                if (startTimeStr != null && endTimeStr != null) {
                    // 解析开始和结束时间
                    LocalDateTime startTime = LocalDateTime.parse(startTimeStr, TimeConstant.DEFAULT_FORMATTER);
                    LocalDateTime endTime = LocalDateTime.parse(endTimeStr, TimeConstant.DEFAULT_FORMATTER);

                    appointmentTestDate = startTime.toLocalDate().toString();
                    appointmentTestTime = startTime.toLocalTime().toString() + "-" + endTime.toLocalTime().toString();
                }
                context.setAppointmentTestDate(appointmentTestDate);
                context.setAppointmentTestTime(appointmentTestTime);
//                appointmentMap.put("appointmentTestDate", appointmentTestDate);
//                appointmentMap.put("appointmentTestTime", appointmentTestTime);
            }
            // 判断是新增还是修改
            Boolean updateFlag = false;
//            if (!StringHelper.IsEmptyOrNull(mapParam.get("appointmentId"))) {
//                // 数据库查询是否存在此id
//                QueryWrapper<SacAppointmentSheet> queryWrapper = new QueryWrapper<SacAppointmentSheet>();
//                queryWrapper.eq("APPOINTMENT_ID", mapParam.get("appointmentId"));
//                queryWrapper.lambda().select(SacAppointmentSheet::getIsTestDrive, SacAppointmentSheet::getAppointmentStartTime);
//                List<SacAppointmentSheet> list = baseMapper.selectList(queryWrapper);
//                if (!list.isEmpty()) {
//                    updateFlag = true;
//                    // 当该试乘试驾单已经开始试乘试驾了就不能修改了
//                    if ("1".equals(list.get(0).getIsTestDrive())) {
//                        throw new BusicenException(message.get("APPOINTMENT-SHEET-13"));
//                    }
//                    // 已经过期的试乘试驾预约单不能修改(跟进时不限制)
//                    Calendar date = Calendar.getInstance();
//                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    String dateString = simpleDateFormat.format(date.getTime());
//                    if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(list.get(0).getAppointmentStartTime()))
//                            && "0".equals(mapParam.get("isFollow"))) {
//                        throw new BusicenException(message.get("APPOINTMENT-SHEET-14"));
//                    }
//                }
//            }

            // 客户预约单查重:根据电话、预约试乘试驾日期、预约试乘试驾时间段、专营店编码查重
            // 普通试乘试驾查重
//            mapParam.put("isCheckRepeak", true);
//            mapParam.put("updateFlag", updateFlag);
            // 查询用户是否已预约当前时间段
            SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
            appointmentSheetBO.setDlrCode(context.getDlrCode());
            appointmentSheetBO.setPlateNumber(context.getPlateNumber());
            appointmentSheetBO.setListTestStatus(Arrays.asList(TestDriveStatusEnum.NOT_STARTED.getCode(),
                    TestDriveStatusEnum.IN_PROGRESS.getCode()));
            appointmentSheetBO.setCustomerPhone(context.getCustomerPhone());
            List<SacAppointmentSheetBO> sacAppointmentSheetBOS = sacAppointmentSheetService.queryAppointByCondition(appointmentSheetBO,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID, SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME);
            log.info("创建试乘试驾预约单校验是否存在未开始的预约单 {}", JSONObject.toJSONString(sacAppointmentSheetBOS));
            // 这里判断用户是否已经预约过，需要优化成：当预约的时候就放到redis中，然后从redis获取，开始试驾就删除redis
            // Object checkUserAppoint = redisUtil.get(String.format("TestAppointmentSheet-%s-%s", mapParam.get("dlrCode"),mapParam.get
            // ("customerPhone")));
            // 校验数据库中已存在预约单信息是否和本次要创建的时间段重合，重合就返回错误信息
            if (CollectionUtil.isNotEmpty(sacAppointmentSheetBOS)) {

                // 解析当前预约时间段
                LocalDateTime currentStart = LocalDateTime.parse(context.getAppointmentStartTime(), TimeConstant.DEFAULT_FORMATTER);
                LocalDateTime currentEnd = LocalDateTime.parse(context.getAppointmentEndTime(), TimeConstant.DEFAULT_FORMATTER);

                boolean hasOverlap = sacAppointmentSheetBOS.stream()
                        .filter(Objects::nonNull)
                        .anyMatch(existing -> {
                            LocalDateTime existingStart = LocalDateTime.parse(existing.getAppointmentStartTime(), TimeConstant.DEFAULT_FORMATTER);
                            LocalDateTime existingEnd = LocalDateTime.parse(existing.getAppointmentEndTime(), TimeConstant.DEFAULT_FORMATTER);
                            // 时间重叠判断条件：现有开始 < 当前结束 且 现有结束 > 当前开始
                            return existingStart.isBefore(currentEnd) && existingEnd.isAfter(currentStart);
                        });
                if (hasOverlap) {
                    log.warn("在此时间段里您已预约试驾");
                    throw new BusicenException("在此时间段里您已预约试驾");
                }
            }
        } catch (Exception e) {
            log.error("checkRepeat异常", e);
            throw new BusicenException(e.getMessage());
        }
    }
}