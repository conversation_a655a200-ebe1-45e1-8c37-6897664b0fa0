package com.smart.adp.domain.model.base.service;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;

import java.util.List;

/**
 * @Description: 基础服务数据
 * @Author: rik.ren
 * @Date: 2025/5/24 14:18
 **/
public interface BaseDataService {
    /**
     * 根据业务生成单号
     *
     * @param billTypeId
     * @return
     */
    String generateOrderCode(String billTypeId, UserBusiEntity userBusiEntity);


    /**
     * 根据条件查询员工信息
     *
     * @param param
     * @param columns
     * @return
     */
    List<UscMdmOrgEmployeeBO> queryUscMdmOrgEmp(UscMdmOrgEmployeeBO param, QueryColumn... columns);
}
