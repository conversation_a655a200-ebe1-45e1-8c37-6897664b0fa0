package com.smart.adp.domain.model.drive.service;

import com.smart.adp.domain.model.drive.bo.TestDriveReviewRecordBO;
import com.smart.adp.domain.model.drive.entity.SacTestDriveReviewRecordEntity;

/**
 * @Description: 试乘试驾单跟进记录service
 * @Author: rik.ren
 * @Date: 2025/5/27 17:59
 **/
public interface SacTestDriveReviewRecordService {
    /**
     * 查询试乘试驾单跟进记录
     *
     * @param param
     * @return
     */
    SacTestDriveReviewRecordEntity queryTestDriveReviewRecordByCondition(TestDriveReviewRecordBO param);
}
