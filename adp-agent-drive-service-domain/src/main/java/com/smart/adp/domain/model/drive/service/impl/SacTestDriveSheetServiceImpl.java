package com.smart.adp.domain.model.drive.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveSheetGateway;
import com.smart.adp.domain.model.drive.service.ISacTestDriveSheetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * 试乘试驾单表 服务层实现。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Slf4j
@Service
public class SacTestDriveSheetServiceImpl implements ISacTestDriveSheetService {
    @Autowired
    private SacTestDriveSheetGateway testDriveSheetGateway;
    private static final QueryColumn[] DEFAULT_QUERY_COLUMNS = {SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_PHONE, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_TYPE,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS, SAC_TEST_DRIVE_SHEET_ENTITY.PLATE_NUMBER,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_METHOD, SAC_TEST_DRIVE_SHEET_ENTITY.SMALL_CAR_TYPE_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.START_TIME, SAC_TEST_DRIVE_SHEET_ENTITY.END_TIME,
            SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CODE, SAC_TEST_DRIVE_SHEET_ENTITY.DLR_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.UPDATE_CONTROL_ID, SAC_TEST_DRIVE_SHEET_ENTITY.RECEIVER_TIME,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_ROAD_HAUL, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_END_ROAD_HAUL,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_START_ROAD_HAUL,
            SAC_TEST_DRIVE_SHEET_ENTITY.REAL_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_IDNUMBER,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_AGREEMENT_PDF,
            SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN1.as("testDriveEndRemark")};

    private static final QueryColumn[] PAGELIST_QUERY_COLUMNS_WITH_APPOINT = {SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CREATED_DATE, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_PHONE, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_TYPE,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS, SAC_TEST_DRIVE_SHEET_ENTITY.PLATE_NUMBER,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_METHOD, SAC_TEST_DRIVE_SHEET_ENTITY.SMALL_CAR_TYPE_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.START_TIME, SAC_TEST_DRIVE_SHEET_ENTITY.END_TIME,
            SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CODE, SAC_TEST_DRIVE_SHEET_ENTITY.DLR_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_ORDER_NO, SAC_TEST_DRIVE_SHEET_ENTITY.UPDATE_CONTROL_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_ROAD_HAUL, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_END_ROAD_HAUL,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_START_ROAD_HAUL, SAC_TEST_DRIVE_SHEET_ENTITY.CAR_VIN,
            SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN1.as("testDriveEndRemark"),
            SAC_TEST_DRIVE_SHEET_ENTITY.RECORD_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.REAL_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_IDNUMBER,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_AGREEMENT_PDF,
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_DATE.as("appointmentTestDate"),
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_TIME.as("appointmentTestTime"),
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME.as("appointmentStartTime"),
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME.as("appointmentEndTime")};

    private static final QueryColumn[] ALL_COLUMNS_WITH_APPOINT = {SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_SHEET_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.APPOINTMENT_ID, SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CLUE_ORDER_NO,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_ORDER_NO, SAC_TEST_DRIVE_SHEET_ENTITY.CAR_VIN,
            SAC_TEST_DRIVE_SHEET_ENTITY.DLR_CODE, SAC_TEST_DRIVE_SHEET_ENTITY.DLR_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.OLD_TEST_DRIVE_SHEET_ID, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_START_ROAD_HAUL,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_END_ROAD_HAUL, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_ROAD_HAUL,
            SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN3, SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN4,
            SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_ID, SAC_TEST_DRIVE_SHEET_ENTITY.SALES_CONSULTANT_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.START_TIME, SAC_TEST_DRIVE_SHEET_ENTITY.END_TIME,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_TYPE,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_PHONE, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_SEX,
            SAC_TEST_DRIVE_SHEET_ENTITY.SMALL_CAR_TYPE_CODE, SAC_TEST_DRIVE_SHEET_ENTITY.SMALL_CAR_TYPE_NAME,
            SAC_TEST_DRIVE_SHEET_ENTITY.PLATE_NUMBER, SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN2,
            SAC_TEST_DRIVE_SHEET_ENTITY.DRIVING_LICENCE_PHOTO, SAC_TEST_DRIVE_SHEET_ENTITY.RECEIVER_TIME,
            SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_ID_NUMBER_AGREEMENT, SAC_TEST_DRIVE_SHEET_ENTITY.OTHER_AGREEMENT,
            SAC_TEST_DRIVE_SHEET_ENTITY.IS_CAN_CHANGE, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_SIGNATURE_AGREEMENT,
            SAC_TEST_DRIVE_SHEET_ENTITY.UPDATE_CONTROL_ID, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_AGREEMENT,
            SAC_TEST_DRIVE_SHEET_ENTITY.COLUMN1.as("testDriveEndRemark"),
            SAC_TEST_DRIVE_SHEET_ENTITY.REAL_NAME, SAC_TEST_DRIVE_SHEET_ENTITY.CUSTOMER_IDNUMBER,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_AGREEMENT_PDF,
            SAC_TEST_DRIVE_SHEET_ENTITY.TEST_DRIVE_METHOD,
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_DATE.as("appointmentTestDate"),
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_TIME.as("appointmentTestTime"),
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME.as("appointmentStartTime"),
            SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME.as("appointmentEndTime")};

    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    @Override
    public List<SacTestDriveSheetBO> queryTestDriveSheetList(SacTestDriveSheetBO param) {
        return null;
    }

    /**
     * 根据条件查询试驾单
     *
     * @param sheetBoParam
     * @return
     */
    @Override
    public DomainPage<SacTestDriveSheetBO> queryTestDriveSheetPage(SacTestDriveSheetBO sheetBoParam) {
        SacClueInfoDlrBO clueInfoDlrBOParam = BeanUtil.copyProperties(sheetBoParam, SacClueInfoDlrBO.class);
        // 1.查询试驾单信息
        DomainPage<SacTestDriveSheetBO> pageResult = testDriveSheetGateway.queryTestDriveSheet(sheetBoParam, clueInfoDlrBOParam, null,
                PAGELIST_QUERY_COLUMNS_WITH_APPOINT);
        if (pageResult.getTotalCount() == 0 || CollectionUtil.isEmpty(pageResult.getRecords())) {
            return new DomainPage<>(null, sheetBoParam.getPageNumber(), sheetBoParam.getPageSize(), 0L);
        }

        DomainPage<SacTestDriveSheetBO> result =
                new DomainPage<>(SacTestDriveSheetBO.conventBO(pageResult.getRecords()),
                        pageResult.getPageNumber(),
                        pageResult.getPageSize(), pageResult.getTotalCount());
        return result;
    }

    /**
     * 根据条件查询试驾单满足条件的总数
     *
     * @param sheetBoParam
     * @return
     */
    @Override
    public Long queryTestDriveSheetCount(SacTestDriveSheetBO sheetBoParam) {
        SacClueInfoDlrBO clueInfoDlrBOParam = BeanUtil.copyProperties(sheetBoParam, SacClueInfoDlrBO.class);
        return testDriveSheetGateway.queryTestDriveSheetCount(sheetBoParam, clueInfoDlrBOParam);
    }

    @Override
    public SacTestDriveSheetBO queryTestDriveSheetInfoById(SacTestDriveSheetBO boParam, QueryColumn... columns) {
        // 1.查询试驾单信息
        QueryColumn[] needColumn = null;
        if (ObjectUtil.isNotEmpty(columns)) {
            needColumn = columns;
        } else {
            if (boParam.getNeedAppointmentSheet()) {
                needColumn = ALL_COLUMNS_WITH_APPOINT;
            } else {
                needColumn = DEFAULT_QUERY_COLUMNS;
            }
        }
        SacTestDriveSheetBO sheetBo = new SacTestDriveSheetBO(boParam.getTestDriveSheetId(), boParam.getNeedAppointmentSheet());
        sheetBo = testDriveSheetGateway.queryTestDriveSheet(sheetBo, needColumn);
        if (ObjectUtil.isEmpty(sheetBo) || StringUtils.isEmpty(sheetBo.getTestDriveSheetId())) {
            return null;
        }
        return sheetBo;
    }

    /**
     * 根据条件查询一条试乘试驾单
     *
     * @param sheetBoParam
     * @return
     */
    @Override
    public SacTestDriveSheetBO queryTestDriveSheetInfo(SacTestDriveSheetBO sheetBoParam, QueryColumn... columns) {
        // 1.查询试驾单信息
        QueryColumn[] needColumn = null;
        if (ObjectUtil.isNotEmpty(columns)) {
            needColumn = columns;
        } else {
            if (sheetBoParam.getNeedAppointmentSheet()) {
                needColumn = ALL_COLUMNS_WITH_APPOINT;
            } else {
                needColumn = DEFAULT_QUERY_COLUMNS;
            }
        }
        SacTestDriveSheetBO queryResult = testDriveSheetGateway.queryTestDriveSheet(sheetBoParam, needColumn);
        if (ObjectUtil.isEmpty(queryResult) || StringUtils.isEmpty(queryResult.getTestDriveSheetId())) {
            return null;
        }
        return queryResult;
    }

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestDriveSheet(SacTestDriveSheetBO param) {
        return testDriveSheetGateway.updateTestDriveSheet(param);
    }

    /**
     * 创建试乘试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean createTestDriveSheet(SacTestDriveSheetBO param) {
        return testDriveSheetGateway.insertTestDriveSheet(param);
    }

    /**
     * 发送试驾短信
     *
     * @param param
     * @return
     */
    @Override
    public Boolean sendTestDriveMsg(Map<String, Object> param) {
        return testDriveSheetGateway.sendTestDriveMsg(param);
    }

    /**
     * 删除试乘试驾单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean deleteTestDriveSheet(SacTestDriveSheetBO param) {
        return testDriveSheetGateway.delTestDriveSheet(param);
    }

    /**
     * 试驾结束时记录接口表，获取BI车机数据
     *
     * @param param
     * @return
     */
    @Override
    public Boolean vehicleData(Map<String, Object> param) {
        return testDriveSheetGateway.vehicleData(param);
    }

    /**
     * 试驾结束发送ZTMQ
     *
     * @param param
     * @return
     */
    @Override
    public Boolean sendZTMQ(Map<String, Object> param) {
        return testDriveSheetGateway.sendZTMQ(param);
    }
}