package com.smart.adp.domain.model.drive.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.entity.SacTestDriveLongApplyEntity;

import java.util.List;

/**
 * @Description: 超长试驾申请gateway
 * @Author: rik.ren
 * @Date: 2025/5/22 17:11
 **/
public interface SacTestDriveLongApplyGateway {
    /**
     * 根据条件查询超长试驾申请信息
     *
     * @param param
     * @return
     */
    List<SacTestDriveLongApplyEntity> queryTestDriveLongApplyByCondition(SacTestDriveLongApplyEntity param, QueryColumn... needColumns);

    /**
     * 根据条件查询超长试驾申的个数
     *
     * @param param
     * @return
     */
    Long queryTestDriveLongApplyCountByCondition(SacTestDriveLongApplyEntity param);
}
