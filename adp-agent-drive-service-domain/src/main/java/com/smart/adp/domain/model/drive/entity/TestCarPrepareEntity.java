package com.smart.adp.domain.model.drive.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.TestCarStatusCodeEnum;
import lombok.*;

import java.math.BigDecimal;

import java.lang.String;
import java.lang.Integer;
import java.time.LocalDateTime;

/**
 * 试驾车整备表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_usc_bu_testcar_prepare", schema = "mp")
public class TestCarPrepareEntity {

    /**
     * 试驾整备ID
     */
    @Id(value = "TESTCAR_PREPARE_ID")
    private String testcarPrepareId;

    /**
     * 试驾车申请ID
     */
    @Column(value = "TEST_CARAPPLY_ID")
    private String testCarapplyId;

    /**
     * 零售单号
     */
    @Column(value = "SALE_ORDER_CODE")
    private String saleOrderCode;

    /**
     * 车牌号
     */
    @Column(value = "CAR_LICENCE_NO")
    private String carLicenceNo;

    /**
     * 上牌时间
     */
    @Column(value = "CAR_LICENCE_DATE")
    private LocalDateTime carLicenceDate;

    /**
     * 申请门店
     */
    @Column(value = "APPLY_DLR_CODE")
    private String applyDlrCode;

    /**
     * 审批状态
     */
    @Column(value = "RESPONSE_ORDER_STATUS")
    private String responseOrderStatus;

    /**
     * 审批人
     */
    @Column(value = "AUTHER_MAN")
    private String autherMan;

    /**
     * 审批日期
     */
    @Column(value = "AUTHER_DATE")
    private LocalDateTime autherDate;

    /**
     * 驳回原因
     */
    @Column(value = "REJECT_RESON")
    private String rejectReson;

    /**
     * 创建人
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 最后更新时间
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * 并发控制字段
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * VIN码
     */
    @Column(value = "VIN")
    private String vin;

    /**
     * 车型
     */
    @Column(value = "APPLY_CAR_TYPE_CODE")
    private String applyCarTypeCode;

    /**
     * 配置
     */
    @Column(value = "APPLY_CARTYPE_CONFIG")
    private String applyCartypeConfig;

    /**
     * 颜色
     */
    @Column(value = "APPLY_CAR_COLOR_CODE")
    private String applyCarColorCode;

    /**
     * 内饰
     */
    @Column(value = "APPLY_CAR_INCOLOR_CODE")
    private String applyCarIncolorCode;

    /**
     * 申请类型
     */
    @Column(value = "APPLY_TESTCAR_TYPE")
    private String applyTestcarType;

    /**
     * 车主
     */
    @Column(value = "CAR_OWNER")
    private String carOwner;

    /**
     * 联系电话
     */
    @Column(value = "CAR_OWNER_PHONE")
    private String carOwnerPhone;

    /**
     * 备注
     */
    @Column(value = "APPLY_REMARK")
    private String applyRemark;

    /**
     * 替换车辆
     */
    @Column(value = "REPLACE_VIN")
    private String replaceVin;

    /**
     * 上架日期
     */
    @Column(value = "TEST_CAR_ONLINE")
    private LocalDateTime testCarOnline;

    /**
     * 试驾次数
     */
    @Column(value = "TESTCAR_FREQUENCY")
    private Integer testcarFrequency;

    /**
     * 公里数
     */
    @Column(value = "TESTCAR_KILOMETERS")
    private BigDecimal testcarKilometers;

    /**
     * 试驾线索状态
     *
     * @see TestCarStatusCodeEnum
     */
    @Column(value = "CAR_STATUS_CODE")
    private Integer carStatusCode;

    /**
     * 试驾线索名称
     */
    @Column(value = "CAR_STATUS_NAME")
    private String carStatusName;

    /**
     * 使用时长
     */
    @Column(value = "TEST_CAR_EXPIRE")
    private String testCarExpire;

    /**
     * 代付凭证
     */
    @Column(value = "PAYMENT_VOUCHER")
    private String paymentVoucher;

    /**
     * 申请单状态
     */
    @Column(value = "TEST_APPLY_STATUS")
    private String testApplyStatus;

    /**
     * 试驾车状态
     */
    @Column(value = "TEST_DRIVE_STATUS")
    private String testDriveStatus;

}
