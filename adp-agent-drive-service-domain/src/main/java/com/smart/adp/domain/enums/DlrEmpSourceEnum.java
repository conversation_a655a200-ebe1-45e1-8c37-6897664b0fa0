package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum DlrEmpSourceEnum {

    DISTRIBUTE("1", "店长分配线索获取产品专家"),

    QUERY("2", "线索列表获取产品专家"),

    ;

    private final String code;

    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static DlrEmpSourceEnum getByCode(String code) {
        return Arrays.stream(DlrEmpSourceEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(null);
    }
}
