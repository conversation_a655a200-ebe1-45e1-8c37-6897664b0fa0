package com.smart.adp.domain.model.base.valueObject;

/**
 * <AUTHOR>
 * date 2025/3/5 16:20
 * @description TODO:
 **/

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 值列表
 * t_prc_mds_lookup_value
 */
@Table(value = "t_prc_mds_lookup_value", schema = "mp")
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class LookUpInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 值名称
     */
    @Column("LOOKUP_VALUE_NAME")
    private String lookUpValueName;

    /**
     * 值类型编码
     */
    @Column("LOOKUP_TYPE_CODE")
    private String lookUpTypeCode;

    /**
     * 值类型编码
     */
    @Column("LOOKUP_TYPE_NAME")
    private String lookUpTypeName;

    /**
     * 值编码
     */
    @Column("LOOKUP_VALUE_CODE")
    private String lookUpValueCode;

    /**
     * 序号
     */
    @Column("ORDER_NO")
    private String orderNo;

    /**
     * 专营店ID
     */
    @Column("DLR_ID")
    private String dlrId;

    /**
     * 备注
     */
    @Column("REMARK")
    private String remark;

    /**
     * 厂商标识id
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识id
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识code
     */
    @Column("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识code
     */
    @Column("GROUP_CODE")
    private String groupCode;

    /**
     * 属性1
     */
    @Column("ATTRIBUTE1")
    private String attribute1;

    /**
     * 属性2
     */
    @Column("ATTRIBUTE2")
    private String attribute2;
    /**
     * 属性3
     */
    @Column("ATTRIBUTE3")
    private String attribute3;
    /**
     * 属性4
     */
    @Column("ATTRIBUTE4")
    private String attribute4;
    /**
     * 属性5
     */
    @Column("ATTRIBUTE5")
    private String attribute5;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;


    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
//    public LookUpInfoVO conditions(QueryWrapper wrapper) {
//        wrapper.and(LOOK_UP_INFO.LOOK_UP_TYPE_CODE.eq(getLookUpTypeCode(), StringUtil::hasText))
//                .and(LOOK_UP_INFO.LOOK_UP_VALUE_CODE.eq(getLookUpValueCode(), StringUtil::hasText));
//        return this;
//    }
}
