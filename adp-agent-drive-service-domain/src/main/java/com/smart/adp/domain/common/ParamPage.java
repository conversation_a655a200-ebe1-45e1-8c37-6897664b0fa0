//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.smart.adp.domain.common;

public class ParamPage<T> extends ParamBase<T> {
    int pageIndex = -1;
    int pageSize = -1;

    public ParamPage() {
    }

    public Integer getPageIndex() {
        return this.pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        if (pageIndex < 0 && pageIndex != -1) {
            this.pageIndex = 1;
        } else {
            this.pageIndex = pageIndex;
        }

    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        if (pageSize < 0 && pageSize != -1) {
            this.pageSize = 10;
        } else {
            this.pageSize = pageSize;
        }

    }
}
