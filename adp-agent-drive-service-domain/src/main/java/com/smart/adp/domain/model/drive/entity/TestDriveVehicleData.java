package com.smart.adp.domain.model.drive.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;

import java.time.LocalDateTime;

@Table(value = "t_sac_test_drive_vehicle_data", schema = "csc")
public class TestDriveVehicleData {

    /**
     * 车机数据id
     */
    @Id("vehicle_data_id")
    private String vehicleDataId;

    /**
     * 试驾单号
     */
    @Column("test_drive_order_no")
    private String testDriveOrderNo;

    /**
     * 是否有效试驾 1-是，0-否
     */
    @Column("is_valid_test_drive")
    private int isValidTestDrive;

    /**
     * 开始试驾电量（车机）
     */
    @Column("veh_start_voc")
    private String vehStartVoc;

    /**
     * 试驾开始里程 (车机)
     */
    @Column("veh_start_mileage")
    private String vehStartMileage;

    /**
     * 试驾结束里程 (车机)
     */
    @Column("veh_end_mileage")
    private String vehEndMileage;

    /**
     * 试驾行驶里程（车机）
     */
    @Column("veh_total_mileage")
    private String vehTotalMileage;

    /**
     * 试驾行驶时长（车机）
     */
    @Column("veh_total_dur")
    private String vehTotalDur;

    /**
     * 试驾最高时速（车机）
     */
    @Column("veh_max_speed")
    private String vehMaxSpeed;

    /**
     * 试驾平均时速（车机）
     */
    @Column("test_drive_avg_speed")
    private String testDriveAvgSpeed;

    /**
     * 是否可用 1-可用、0-禁用
     */
    @Column("is_enable")
    private int isEnable;

    /**
     * 创建人
     */
    @Column("creator")
    private String creator;

    /**
     * 创建时间
     */
    @Column("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @Column("modifier")
    private String modifier;

    /**
     * 最后更新时间
     */
    @Column("last_updated_time")
    private LocalDateTime lastUpdatedTime;

    /**
     * 查询BI次数
     */
    @Column("query_bi_times")
    private int queryBiTimes;
}