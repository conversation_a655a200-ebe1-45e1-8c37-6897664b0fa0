package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 性别枚举
 * @Author: rik.ren
 * @Date: 2025/3/16 14:55
 **/
@Getter
@AllArgsConstructor
public enum SexEnum {

    MEN("1", "男"),

    WOMEN("0", "女"),

    ;

    private final String code;

    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static SexEnum getByCode(String code) {
        return Arrays.stream(SexEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(null);
    }
}
