package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum CareTypeEnum {

    HX11("HX11", "#1"),

    HXY("HXY", "#1"),

    HC11("HC11", "#3"),

    HY11("HY11", "#5"),
    ;

    private final String code;

    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static CareTypeEnum getByCode(String code) {
        return Arrays.stream(CareTypeEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findAny()
                .orElse(null);
    }
}
