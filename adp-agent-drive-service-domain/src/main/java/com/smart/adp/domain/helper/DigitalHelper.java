package com.smart.adp.domain.helper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;

/**
* <AUTHOR>
* @version 创建时间 ：2018年10月10日 下午6:24:35
*/
public class DigitalHelper {

	/**
	 * 加法,默认保留二位小数【2位后面部分舍去】
	 * 
	 * <AUTHOR>
	 * @version 创建时间 ：2018年10月10日 下午6:39:49
	 */
	public static Double add(Double addend, Double augend) {
		addend = null == addend ? 0 : addend;
		augend = null == augend ? 0 : augend;
		BigDecimal bd1 = new BigDecimal(Double.toString(addend));
		BigDecimal bd2 = new BigDecimal(Double.toString(augend));
		//.setScale(2, RoundingMode.DOWN).doubleValue();
		return bd1.add(bd2).doubleValue();
	}
	
	/**
	 * 减法,默认保留二位小数【2位后面部分舍去】
	 * 
	 * <AUTHOR>
	 * @version 创建时间 ：2018年10月10日 下午6:40:27
	 */
	public static Double subtract(Double minuend, Double subtrahend) {
		minuend = null == minuend ? 0 : minuend;
		subtrahend = null == subtrahend ? 0 : subtrahend;
		BigDecimal bd1 = new BigDecimal(Double.toString(minuend));
		BigDecimal bd2 = new BigDecimal(Double.toString(subtrahend));
		return bd1.subtract(bd2).setScale(2, RoundingMode.DOWN).doubleValue();
	}
	
	/**
	 * 乘法,默认保留二位小数【2位后面部分舍去】
	 * 
	 * <AUTHOR>
	 * @version 创建时间 ：2018年10月10日 下午6:40:35
	 */
	public static Double multiply(Double multiplier, Double multiplicand) {
		multiplier = null == multiplier ? 0 : multiplier;
		multiplicand = null == multiplicand ? 0 : multiplicand;
		BigDecimal bd1 = new BigDecimal(Double.toString(multiplier));
		BigDecimal bd2 = new BigDecimal(Double.toString(multiplicand));
		return bd1.multiply(bd2).setScale(2, RoundingMode.DOWN).doubleValue();
	}
	
	/**
	 * 除法,默认保留二位小数【2位后面部分舍去】
	 * 
	 * <AUTHOR>
	 * @version 创建时间 ：2018年10月10日 下午6:40:48
	 */
	public static Double divide(Double dividend, Double divisor) {
		dividend = null == dividend ? 0 : dividend;
		divisor = null == divisor ? 0 : divisor;
		BigDecimal bd1 = new BigDecimal(Double.toString(dividend));
		BigDecimal bd2 = new BigDecimal(Double.toString(divisor));
		return bd1.divide(bd2, 2, RoundingMode.DOWN).doubleValue();
	}
	
	/**
	 * 转换成最多2位小数的百分比【2位后面部分舍去】
	 * @param number 被转换的数
	 * 
	 * <AUTHOR>
	 * @version 创建时间 ：2018年10月11日 上午8:53:05
	 */
	public static String toPercent(double number) {
		NumberFormat percent = NumberFormat.getPercentInstance();
	    percent.setMaximumFractionDigits(2);
	    return percent.format(number);
	}
	
	/***
	 * 千分位，保留两位小数
	 * 
	 * <AUTHOR>
	 * @version 创建时间 ：2018年11月13日 下午4:53:30
	 */
	public static String fmtMicrometer(Double amount) {
		amount = null == amount ? 0 : amount;
		BigDecimal a = new BigDecimal(String.valueOf(amount)); 
		DecimalFormat df = new DecimalFormat(",###,##0.00"); //保留二位小数   
		return df.format(a); 
	}
}
