package com.smart.adp.domain.common.constants;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/7
 */
public interface MQConstants {

    /**
     * 客户事件 exchange
     */
    String CUST_EVENT_EXCHANGE = "smart.ex.adp.custEvent";

    /**
     * 客户事件 queue
     */
    String CUST_EVENT_QUEUE = "smart.queue.adp.custEvent";

    /**
     * 客户事件队列最大量
     */
    int CUST_EVENT_MAX_LENGTH = 100000;

    /**
     * 客户事件 DLQ exchange
     */
    String CUST_EVENT_DLQ_EXCHANGE = "smart.ex.adp.custEvent.dlq";

    /**
     * 客户事件 DLQ queue
     */
    String CUST_EVENT_DLQ_QUEUE = "smart.queue.adp.custEvent.dlq";


    /**
     * 创建线索 queue
     */
    String CREATE_CLUE_QUEUE = "smart.queue.adp.createClueQueue";

    /**
     * 创建线索 交换机
     */
    String CREATE_CLUE_EXCHANGE = "smart.ex.adp.createClue.exchange";

    /**
     * 开始试驾时更新线索信息 交换机
     */
    String START_DRIVING_MODIFY_CLUE_EXCHANGE = "smart.ex.adp.startDriving.modifyClue.exchange";


    /**
     * 店长线索分配 queue
     */
    String DISTRIBUTE_CLUE_QUEUE = "smart.queue.adp.distributeClueQueue";

    /**
     * 店长线索分配 交换机
     */
    String DISTRIBUTE_CLUE_EXCHANGE = "smart.ex.adp.distribute.exchange";

    /**
     * 企微消息交换机
     */
    String WECOM_MSG_EXCHANGE = "smart.ex.adp.wecom.msg";

    String WECOM_MSG_ROUTING_KEY = "smart.ex.adp.wecom.msg.routingKey";

    String WECOM_MSG_QUEUE = "smart.ex.adp.wecom.msg.queue";
}
