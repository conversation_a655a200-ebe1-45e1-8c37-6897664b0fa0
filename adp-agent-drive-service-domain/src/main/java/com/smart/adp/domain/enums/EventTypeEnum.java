package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 互动数据查询用户事件类型
 * @Author: rik.ren
 * @Date: 2025/4/09 13:32
 **/
@Getter
@AllArgsConstructor
public enum EventTypeEnum {
    /**
     * 全部
     */
    ALL("all", "全部"),
    /**
     * 关键事件
     */
    CRUX("crux", "关键事件"),
    /**
     * 其他活跃事件
     */
    OTHER("other", "其他活跃事件"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static EventTypeEnum getByCode(String code) {
        return Arrays.stream(EventTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
