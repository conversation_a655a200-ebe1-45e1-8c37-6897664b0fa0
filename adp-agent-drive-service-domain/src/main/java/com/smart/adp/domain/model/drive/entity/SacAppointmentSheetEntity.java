package com.smart.adp.domain.model.drive.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.time.LocalDateTime;
import java.lang.String;

/**
 * 试乘试驾预约单表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_appointment_sheet", schema = "csc")
public class SacAppointmentSheetEntity {

    /**
     * 预约单ID
     */
    @Id(value = "APPOINTMENT_ID")
    private String appointmentId;

    /**
     * 是否已试乘试驾
     */
    @Column(value = "IS_TEST_DRIVE")
    private String isTestDrive;

    /**
     * 所属专营店编码
     */
    @Column(value = "DLR_CODE")
    private String dlrCode;

    /**
     * 所属专营店名称
     */
    @Column(value = "DLR_NAME")
    private String dlrName;

    /**
     * 线索单号
     */
    @Column(value = "DLR_CLUE_ORDER_NO")
    private String dlrClueOrderNo;

    /**
     * 客户姓名
     */
    @Column(value = "CUSTOMER_NAME")
    private String customerName;

    /**
     * 客户ID
     */
    @Column(value = "CUSTOMER_ID")
    private String customerId;

    /**
     * 客户电话
     */
    @Column(value = "CUSTOMER_PHONE")
    private String customerPhone;

    /**
     * 性别
     */
    @Column(value = "CUSTOMER_SEX")
    private String customerSex;

    /**
     * 预约单号
     */
    @Column(value = "APPOINTMENT_ORDER_NO")
    private String appointmentOrderNo;

    /**
     * 预约时间(什么时候预约)
     */
    @Column(value = "APPOINTMENT_TIME")
    private String appointmentTime;

    /**
     * 试乘试驾车型编码
     */
    @Column(value = "SMALL_CAR_TYPE_CODE")
    private String smallCarTypeCode;

    /**
     * 试乘试驾车型名称
     */
    @Column(value = "SMALL_CAR_TYPE_NAME")
    private String smallCarTypeName;

    /**
     * 试驾车牌
     */
    @Column(value = "PLATE_NUMBER")
    private String plateNumber;

    /**
     * 试乘试驾类型(0：试乘，1：试驾,2:超长试乘试驾)
     */
    @Column(value = "TEST_TYPE")
    private String testType;

    /**
     * 预约试乘试驾日期(普通试乘试驾)
     */
    @Column(value = "APPOINTMENT_TEST_DATE")
    private String appointmentTestDate;

    /**
     * 预约试乘试驾时间段(普通试乘试驾)
     */
    @Column(value = "APPOINTMENT_TEST_TIME")
    private String appointmentTestTime;

    /**
     * 预约超长试驾开始时间
     */
    @Column(value = "APPOINTMENT_START_TIME")
    private String appointmentStartTime;

    /**
     * 预约超长试驾结束时间
     */
    @Column(value = "APPOINTMENT_END_TIME")
    private String appointmentEndTime;

    /**
     * 预约渠道(0：门店自建，1：线上预约)
     */
    @Column(value = "APPOINTMENT_CHANNEL")
    private String appointmentChannel;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 车架号
     */
    @Column(value = "CAR_VIN")
    private String carVin;

    /**
     * 新门店预约试乘试驾开始时间
     */
    @Column(value = "NEW_DLR_APPOINTMENT_START_TIME")
    private String newDlrAppointmentStartTime;

    /**
     * 新门店预约试乘试驾结束时间
     */
    @Column(value = "NEW_DLR_APPOINTMENT_END_TIME")
    private String newDlrAppointmentEndTime;

    /**
     * 预计到店时间
     */
    @Column(value = "EXPECT_START_DATE")
    private String expectStartDate;

}
