package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 试驾数据重推类型枚举
 * @Author: rik.ren
 * @Date: 2025/7/09 17:29
 **/
@Getter
@AllArgsConstructor
public enum RePushTestDriveTypeEnum {
    /**
     * 创建试驾单发送试驾消息
     */
    SEND_CREATED_SMS(0, "创建试驾单发送试驾短信"),
    /**
     * 开始试驾通知CDP
     */
    SEND_START_CDP(1, "开始试驾通知CDP"),
    /**
     * 开始试驾修改线索agent-clue意向车型
     */
    SEND_START_INTENTIONCAR(2, "开始试驾修改线索agent-clue意向车型"),
    /**
     * 结束试驾记录接口表，获取BI车机数据
     */
    SEND_STOP_BI(3, "结束试驾记录接口表，获取BI车机数据"),
    /**
     * 结束试驾发送ZTMQ
     */
    SEND_STOP_ZTMQ(4, "结束试驾发送ZTMQ"),
    /**
     * 结束试驾发送TDA
     */
    SEND_STOP_TDA(5, "结束试驾发送TDA"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static RePushTestDriveTypeEnum getByCode(Integer code) {
        return Arrays.stream(RePushTestDriveTypeEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
