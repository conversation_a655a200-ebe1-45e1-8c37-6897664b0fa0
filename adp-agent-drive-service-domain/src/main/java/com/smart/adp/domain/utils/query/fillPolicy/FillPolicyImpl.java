package com.smart.adp.domain.utils.query.fillPolicy;

import lombok.AllArgsConstructor;

import java.util.function.Function;

@AllArgsConstructor
public class FillPolicyImpl<K, V> implements FillPolicy<K, V> {

    private final boolean shouldFill;
    private final boolean async;
    private final boolean nullFill;
    private final Function<K, V> nullValueFunc;

    @Override
    public boolean shouldFill() {
        return shouldFill;
    }

    @Override
    public boolean async() {
        return async;
    }

    @Override
    public boolean nullFill() {
        return nullFill;
    }

    @Override
    public Function<K, V> nullValueFunc() {
        return nullValueFunc;
    }
}