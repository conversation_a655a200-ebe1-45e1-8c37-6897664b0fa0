package com.smart.adp.domain.model.drive.service.impl;

import com.smart.adp.domain.model.drive.bo.TestDriveReviewRecordBO;
import com.smart.adp.domain.model.drive.entity.SacTestDriveReviewRecordEntity;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveReviewRecordGateway;
import com.smart.adp.domain.model.drive.service.SacTestDriveReviewRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 试乘试驾单跟进记录service实现
 * @Author: rik.ren
 * @Date: 2025/5/27 18:00
 **/
@Service
public class SacTestDriveReviewRecordServiceImpl implements SacTestDriveReviewRecordService {
    @Autowired
    private SacTestDriveReviewRecordGateway sacTestDriveReviewRecordGateway;

    /**
     * 查询试乘试驾单跟进记录
     *
     * @param param
     * @return
     */
    @Override
    public SacTestDriveReviewRecordEntity queryTestDriveReviewRecordByCondition(TestDriveReviewRecordBO param) {
        return sacTestDriveReviewRecordGateway.queryTestDriveReviewRecord(param);
    }
}
