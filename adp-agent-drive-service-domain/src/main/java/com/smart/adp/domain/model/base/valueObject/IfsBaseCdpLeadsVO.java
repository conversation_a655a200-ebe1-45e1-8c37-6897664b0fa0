package com.smart.adp.domain.model.base.valueObject;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * adp自建线索创建发送cdp（adp->cdp）
 *
 * @TableName t_ifs_base_cdp_leads
 */
@Table(value = "t_ifs_base_cdp_leads", schema = "interfacecenter")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IfsBaseCdpLeadsVO {
    /**
     * 主键ID
     */
    @Column(value = "logs_id")
    private String logsId;

    /**
     * 主键 手机号
     */
    @Column(value = "bk")
    private String bk;

    /**
     * smartid
     */
    @Column(value = "c_smartid")
    private String cSmartid;

    /**
     * 姓名
     */
    @Column(value = "name")
    private String name;

    /**
     * 昵称
     */
    @Column(value = "c_nickname")
    private String cNickname;

    /**
     * 手机号码
     */
    @Column(value = "mobile")
    private String mobile;

    /**
     * 备用手机号1
     */
    @Column(value = "c_standby_mobile1")
    private String cStandbyMobile1;

    /**
     * 备用手机号2
     */
    @Column(value = "c_standby_mobile2")
    private String cStandbyMobile2;

    /**
     * 微信账号
     */
    @Column(value = "c_wechat")
    private String cWechat;

    /**
     * 邮箱
     */
    @Column(value = "email")
    private String email;

    /**
     * 省份
     */
    @Column(value = "c_province")
    private String cProvince;

    /**
     * 城市
     */
    @Column(value = "c_city")
    private String cCity;

    /**
     * 地区
     */
    @Column(value = "c_county")
    private String cCounty;

    /**
     * 性别
     */
    @Column(value = "c_gender")
    private String cGender;

    /**
     * 生日 yyyy-MM-dd
     */
    @Column(value = "c_birthday")
    private String cBirthday;

    /**
     * 职业
     */
    @Column(value = "c_job")
    private String cJob;

    /**
     * 年龄
     */
    @Column(value = "c_age_group")
    private String cAgeGroup;

    /**
     * 学历
     */
    @Column(value = "c_education")
    private String cEducation;

    /**
     * 婚姻状况
     */
    @Column(value = "c_marital_status")
    private String cMaritalStatus;

    /**
     * 家庭情况
     */
    @Column(value = "c_family_status")
    private String cFamilyStatus;

    /**
     * 兴趣爱好
     */
    @Column(value = "c_interests")
    private String cInterests;

    /**
     * 收入水平
     */
    @Column(value = "c_income_level")
    private String cIncomeLevel;

    /**
     * 用户账号属性
     */
    @Column(value = "c_account_type")
    private String cAccountType;

    /**
     * 是否smart燃油车老车主
     */
    @Column(value = "c_is_smart_petrol_based")
    private String cIsSmartPetrolBased;

    /**
     * 车主角色
     */
    @Column(value = "c_owner_role")
    private String cOwnerRole;

    /**
     * 用户来源（=二级来源）
     */
    @Column(value = "source")
    private String source;

    /**
     * 推荐人用户ID
     */
    @Column(value = "c_referrer_id")
    private String cReferrerId;

    /**
     * 用户注册/留资最早来源渠道（=一级来源）
     */
    @Column(value = "c_register_channel")
    private String cRegisterChannel;

    /**
     * 活动来源内容
     */
    @Column(value = "c_campaign_source_content")
    private String cCampaignSourceContent;

    /**
     * 最后更新系统来源
     */
    @Column(value = "c_lastupdate_system")
    private String cLastupdateSystem;

    /**
     * 销售大使
     */
    @Column(value = "c_sales_ambassador")
    private String cSalesAmbassador;

    /**
     * 所属城市公司
     */
    @Column(value = "c_city_company")
    private String cCityCompany;

    /**
     * 所属代理商
     */
    @Column(value = "c_agent")
    private String cAgent;

    /**
     * 所属销售门店
     */
    @Column(value = "c_store")
    private String cStore;

    /**
     * 用户了解品牌渠道
     */
    @Column(value = "c_know_channel")
    private String cKnowChannel;

    /**
     * 门店编码
     */
    @Column(value = "c_store_code")
    private String cStoreCode;

    /**
     * 门店名称
     */
    @Column(value = "c_store_name")
    private String cStoreName;

    /**
     * 线索等级=意向等级
     */
    @Column(value = "c_stage")
    private String cStage;

    /**
     * 热度名称
     */
    @Column(value = "c_heat_name")
    private String cHeatName;

    /**
     * 热度编码
     */
    @Column(value = "c_heat_code")
    private String cHeatCode;

    /**
     * 购车因素
     */
    @Column(value = "c_considering")
    private String cConsidering;

    /**
     * 是否首辆车 是/否
     */
    @Column(value = "c_first_car")
    private String cFirstCar;

    /**
     * 拥有机动车辆数量
     */
    @Column(value = "c_car_nums")
    private String cCarNums;

    /**
     * 意向车型
     */
    @Column(value = "c_interested_car_model")
    private String cInterestedCarModel;

    /**
     * 意向车型颜色
     */
    @Column(value = "c_interested_car_colour")
    private String cInterestedCarColour;

    /**
     * 计划购车时间
     */
    @Column(value = "c_planned_purchase_time")
    private String cPlannedPurchaseTime;

    /**
     * 意向车身配色
     */
    @Column(value = "c_intersted_car_body_colour")
    private String cInterstedCarBodyColour;

    /**
     * 备选车型
     */
    @Column(value = "c_alternative_car_model")
    private String cAlternativeCarModel;

    /**
     * 备选品牌
     */
    @Column(value = "c_alternative_car_brand")
    private String cAlternativeCarBrand;

    /**
     * 是否增换购
     */
    @Column(value = "c_is_additional_purchase")
    private String cIsAdditionalPurchase;

    /**
     * 前车车型
     */
    @Column(value = "c_owned_car_model")
    private String cOwnedCarModel;

    /**
     * 前车品牌
     */
    @Column(value = "c_owned_car_brand")
    private String cOwnedCarBrand;

    /**
     * 考虑的备选竞品品牌车型
     */
    @Column(value = "c_competitive_brand")
    private String cCompetitiveBrand;

    /**
     * CEC沟通次数
     */
    @Column(value = "c_cec_communication_times")
    private String cCecCommunicationTimes;

    /**
     * 销售顾问
     */
    @Column(value = "c_seller")
    private String cSeller;

    /**
     * 购买意向
     */
    @Column(value = "c_car_purchase_intention")
    private String cCarPurchaseIntention;

    /**
     * 购买用途
     */
    @Column(value = "c_purpose_of_purchase")
    private String cPurposeOfPurchase;

    /**
     * 购车预算
     */
    @Column(value = "c_car_purchase_budget")
    private String cCarPurchaseBudget;

    /**
     * 插入日期
     */
    @Column(value = "insert_date")
    private LocalDateTime insertDate;

    /**
     * 发送状态
     */
    @Column(value = "send_flag")
    private String sendFlag;

    /**
     * 错误日志
     */
    @Column(value = "err_log")
    private String errLog;

    /**
     * 发送日期
     */
    @Column(value = "send_date")
    private LocalDateTime sendDate;

    /**
     * 备注
     */
    @Column(value = "remark")
    private String remark;

    /**
     * 销售顾问电话
     */
    @Column(value = "c_seller_phone")
    private String cSellerPhone;

    /**
     * 销售顾问ID
     */
    @Column(value = "c_seller_id")
    private String cSellerId;

    /**
     * 销售顾问头像url
     */
    @Column(value = "c_seller_url")
    private String cSellerUrl;

    /**
     * 一级来源
     */
    @Column(value = "c_first_channel")
    private String cFirstChannel;

    /**
     * 二级来源
     */
    @Column(value = "c_second_channel")
    private String cSecondChannel;

    /**
     * 三级来源
     */
    @Column(value = "c_third_channel")
    private String cThirdChannel;

    /**
     * 渠道描述
     */
    @Column(value = "c_cus_source")
    private String cCusSource;
}