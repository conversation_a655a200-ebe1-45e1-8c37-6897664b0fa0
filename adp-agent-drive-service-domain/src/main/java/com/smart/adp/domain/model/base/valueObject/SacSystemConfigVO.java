package com.smart.adp.domain.model.base.valueObject;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.model.drive.entity.SacTestDriveSheetEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.smart.adp.domain.model.base.valueObject.table.SacSystemConfigVOTableDef.SAC_SYSTEM_CONFIG_VO;
import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * <AUTHOR>
 * date 2025/3/6 13:23
 * @description 系统配置表
 **/
@Table(value = "t_sac_system_config", schema = "csc")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class SacSystemConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @Column(value = "CONFIG_ID")
    private String configId;

    /**
     * 配置范围,GLOBAL:全局，HOSTDLR:总部和店端，HOST:总部，DLR:店端
     */
    @Column(value = "CONFIG_RANGE")
    private String configRange;

    /**
     * 所属组织编码,-1则为公共配置项
     */
    @Column(value = "ORG_CODE")
    private String orgCode;

    /**
     * 所属组织名称
     */
    @Column(value = "ORG_NAME")
    private String orgName;

    /**
     * 配置项编码
     */
    @Column(value = "CONFIG_CODE")
    private String configCode;

    /**
     * 配置项名称
     */
    @Column(value = "CONFIG_NAME")
    private String configName;

    /**
     * 说明
     */
    @Column(value = "CONFIG_DESC")
    private String configDesc;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnabled;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 构建查询条件
     * @param wrapper
     * @return
     */
    public SacSystemConfigVO buildConditions(QueryWrapper wrapper) {
        wrapper.and(SAC_SYSTEM_CONFIG_VO.CONFIG_ID.eq(getConfigId(), StringUtil::hasText))
                .and(SAC_SYSTEM_CONFIG_VO.CONFIG_RANGE.eq(getConfigRange(), StringUtil::hasText))
                .and(SAC_SYSTEM_CONFIG_VO.ORG_CODE.eq(getOrgCode(), StringUtil::hasText))
                .and(SAC_SYSTEM_CONFIG_VO.CONFIG_CODE.eq(getConfigCode(), StringUtil::hasText));
        return this;
    }
}
