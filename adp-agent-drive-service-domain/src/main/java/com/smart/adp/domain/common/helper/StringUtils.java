/**
 * Copyright (c) 2017, NTTDATA All Rights Reserved.
 */

package com.smart.adp.domain.common.helper;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Function: 字符串常用操作. <br>
 * Date: 2023年3月1日 下午5:58:47 <br>
 *
 * @version 1.0
 * @since 1.0
 */
public class StringUtils extends org.springframework.util.StringUtils {

    private static final int PAD_LIMIT = 8192;

    final static int BUFFER_SIZE = 4096;

    public static final String EMPTY = "";

    private static final String SPACE = " ";

    public static SecureRandom random;

    static {
        try {
            random = SecureRandom.getInstanceStrong();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private static String[] chars = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "m", "n",
            "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "2", "3", "4", "5", "6", "7", "8",
            "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "M", "N", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"};

    private static String[] UPPER_CHARS = new String[]{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "M", "N", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"};

    private static String[] LOWER_CHARS = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "m", "n",
            "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};

    private static String[] NUMBER_CHARS = new String[]{"2", "3", "4", "5", "6", "7", "8", "9"};

    private static final String REGEX_SCRIPT = "<script[^>]*?>[\\s\\S]*?<\\/script>"; // 定义script的正则表达式
    private static final String REGEX_STYLE = "<style[^>]*?>[\\s\\S]*?<\\/style>"; // 定义style的正则表达式
    private static final String REGEX_HTML = "<[^>]+>"; // 定义HTML标签的正则表达式
    private static final String REGEX_SPACE = "\\s*|\t|\r|\n";//定义空格回车换行符

    /**
     * getRandStr:(生成一个随机字符串). <br>
     *
     * @param length
     * @return
     * @since 1.0
     */
    public static String getRandStr(int length) {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            int y = x % 0x3E;
            if (y >= 56) {
                y = 55;
            }
            shortBuffer.append(chars[y]);
        }
        return shortBuffer.toString();
    }

    public static String getRandPwdStr() {
        StringBuilder sb = new StringBuilder("");
        sb.append(UPPER_CHARS[random.nextInt(UPPER_CHARS.length)]);

        sb.append(LOWER_CHARS[random.nextInt(LOWER_CHARS.length)]);

        for (int i = 0; i < 6; i++) {
            sb.append(NUMBER_CHARS[random.nextInt(NUMBER_CHARS.length)]);
        }
        return sb.toString();
    }

    public static String getRandNumberCode(Integer length) {
        StringBuilder sb = new StringBuilder("");
        for (int i = 0; i < length; i++) {
            sb.append(NUMBER_CHARS[random.nextInt(NUMBER_CHARS.length)]);
        }
        return sb.toString();
    }

    public static String getUUID() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replaceAll("-", "");
    }

    public static String leftPad(final String str, final int size, final char padChar) {
        if (str == null) {
            return null;
        }
        final int pads = size - str.length();
        if (pads <= 0) {
            return str; // returns original String when possible
        }
        if (pads > PAD_LIMIT) {
            return leftPad(str, size, String.valueOf(padChar));
        }
        return repeat(padChar, pads).concat(str);
    }

    public static String leftPad(final String str, final int size, String padStr) {
        if (str == null) {
            return null;
        }
        if (isEmpty(padStr)) {
            padStr = SPACE;
        }
        final int padLen = padStr.length();
        final int strLen = str.length();
        final int pads = size - strLen;
        if (pads <= 0) {
            return str; // returns original String when possible
        }
        if (padLen == 1 && pads <= PAD_LIMIT) {
            return leftPad(str, size, padStr.charAt(0));
        }

        if (pads == padLen) {
            return padStr.concat(str);
        } else if (pads < padLen) {
            return padStr.substring(0, pads).concat(str);
        } else {
            final char[] padding = new char[pads];
            final char[] padChars = padStr.toCharArray();
            for (int i = 0; i < pads; i++) {
                padding[i] = padChars[i % padLen];
            }
            return new String(padding).concat(str);
        }
    }

    public static String leftPad(final String str, final int size) {
        return leftPad(str, size, ' ');
    }

    public static String repeat(final String str, final int repeat) {
        // Performance tuned for 2.0 (JDK1.4)

        if (str == null) {
            return null;
        }
        if (repeat <= 0) {
            return EMPTY;
        }
        final int inputLength = str.length();
        if (repeat == 1 || inputLength == 0) {
            return str;
        }
        if (inputLength == 1 && repeat <= PAD_LIMIT) {
            return repeat(str.charAt(0), repeat);
        }

        final int outputLength = inputLength * repeat;
        switch (inputLength) {
            case 1:
                return repeat(str.charAt(0), repeat);
            case 2:
                final char ch0 = str.charAt(0);
                final char ch1 = str.charAt(1);
                final char[] output2 = new char[outputLength];
                for (int i = repeat * 2 - 2; i >= 0; i--, i--) {
                    output2[i] = ch0;
                    output2[i + 1] = ch1;
                }
                return new String(output2);
            default:
                final StringBuilder buf = new StringBuilder(outputLength);
                for (int i = 0; i < repeat; i++) {
                    buf.append(str);
                }
                return buf.toString();
        }
    }

    public static String repeat(final char ch, final int repeat) {
        if (repeat <= 0) {
            return EMPTY;
        }
        final char[] buf = new char[repeat];
        for (int i = repeat - 1; i >= 0; i--) {
            buf[i] = ch;
        }
        return new String(buf);
    }

    /**
     * 将InputStream转换成某种字符编码的String
     *
     * @param in
     * @return
     * @throws Exception
     */
    public static String InputStreamTOString(InputStream in) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[BUFFER_SIZE];
        int count = -1;
        while ((count = in.read(data, 0, BUFFER_SIZE)) != -1) {
            outStream.write(data, 0, count);
        }
        data = null;
        return new String(outStream.toByteArray());
    }

    public static String contatStr(String str1, String str2) {
        if (!isEmpty(str1) && !isEmpty(str2)) {
            return str1.concat(",").concat(str2);
        } else if (!isEmpty(str1) && isEmpty(str2)) {
            return str1;
        } else {
            return str2;
        }
    }

    public static String joinStr(List<String> strs, String sep) {
        if (StringUtils.isEmpty(sep)) {
            sep = ",";
        }
        StringBuilder sb = new StringBuilder();
        if (null != strs && strs.size() > 0) {
            for (String str : strs) {
                sb.append(sep);
                sb.append(str);
            }
        }
        if (sb.length() > 1) {
            return sb.substring(1);
        }
        return sb.toString();
    }

    public static String convertHtmlToStr(String htmlStr) {
        if (isEmpty(htmlStr)) {
            return "";
        }
        Pattern p_script = Pattern.compile(REGEX_SCRIPT, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        htmlStr = m_script.replaceAll(""); // 过滤script标签

        Pattern p_style = Pattern.compile(REGEX_STYLE, Pattern.CASE_INSENSITIVE);
        Matcher m_style = p_style.matcher(htmlStr);
        htmlStr = m_style.replaceAll(""); // 过滤style标签

        Pattern p_html = Pattern.compile(REGEX_HTML, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        htmlStr = m_html.replaceAll(""); // 过滤html标签

        Pattern p_space = Pattern.compile(REGEX_SPACE, Pattern.CASE_INSENSITIVE);
        Matcher m_space = p_space.matcher(htmlStr);
        htmlStr = m_space.replaceAll(""); // 过滤空格回车标签

        htmlStr = htmlStr.replaceAll("&nbsp;", " ");
        return htmlStr; // 返回文本字符串
    }


    /**
     * '[1,2,3,4]'这样的toString字符串转成字符串集合
     *
     * @param str 字符串
     * @return 结果集合
     */
    public static List<String> tostringsToList(String str) {
        if (str == null) {
            return null;
        }
        if (str.equals("")) {
            return new ArrayList<>();
        }
        str = str.replace("[", "");
        str = str.replace("]", "");
        str = str.replaceAll(" ", "");
        String[] strArray = str.split(",");
        return Arrays.asList(strArray);
    }

    public static boolean isBlank(CharSequence cs) {
        int strLen;
        if (cs != null && (strLen = cs.length()) != 0) {
            for (int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(cs.charAt(i))) {
                    return false;
                }
            }
            return true;
        } else {
            return true;
        }
    }

    //判断字符串是否是纯数字
    public static boolean isNumber(String str) {

        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 生成 16 位随机字符串
     *
     * @return sku code
     */
    public static String get16Str() {
        String uuid = UUID.randomUUID().toString();
        char[] cs = new char[32];
        char c = 0;
        for (int i = uuid.length() / 2, j = 1; i-- > 0; ) {
            if ((c = uuid.charAt(i)) != '-') {
                cs[j++] = c;
            }
        }
        String uid = String.valueOf(cs);
        return uid;
    }

    /**
     * 以星号遮盖字符串中间
     *
     * @param content  原文
     * @param frontNum 起始位置
     * @param endNum   终止位置
     * @return 遮盖后的字符串
     */
    public static String getStringEncryption(String content, int frontNum, int endNum) {

        if (frontNum >= content.length() || frontNum < 0) {
            return content;
        }
        if (endNum >= content.length() || endNum < 0) {
            return content;
        }
        if (frontNum + endNum >= content.length()) {
            return content;
        }
        String starStr = "";
        for (int i = 0; i < (content.length() - frontNum - endNum); i++) {
            starStr = starStr + "*";
        }
        String s = content.substring(0, frontNum) + starStr
                + content.substring(content.length() - endNum, content.length());
        return s;
    }
}
