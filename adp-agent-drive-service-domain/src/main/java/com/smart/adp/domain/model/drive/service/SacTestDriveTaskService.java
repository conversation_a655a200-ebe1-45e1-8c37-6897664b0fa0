package com.smart.adp.domain.model.drive.service;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;

import java.util.List;

/**
 * @Description: 试乘试驾任务service
 * @Author: rik.ren
 * @Date: 2025/6/30 14:27
 **/
public interface SacTestDriveTaskService {
    /**
     * 创建试驾任务信息
     *
     * @param param
     * @return
     */
    Boolean createTestDriveTask(SacTestDriveTaskBO param);

    /**
     * 修改试驾任务信息
     *
     * @param param
     * @return
     */
    Boolean modifyTestDriveTask(SacTestDriveTaskBO param);

    /**
     * 根据条件查询一条试驾任务
     *
     * @param param
     * @return
     */
    SacTestDriveTaskBO getTestDriveTask(SacTestDriveTaskBO param, QueryColumn... columns);

    /**
     * 根据条件查询试驾任务集合
     *
     * @param param
     * @return
     */
    List<SacTestDriveTaskBO> queryTestDriveTaskList(SacTestDriveTaskBO param, QueryColumn... columns);

    /**
     * 分页查询试驾任务
     *
     * @param param
     * @param orderBy
     * @param columns
     * @return
     */
    DomainPage<SacTestDriveTaskBO> queryTestDriveTaskPage(SacTestDriveTaskBO param, QueryOrderBy orderBy, QueryColumn... columns);

    /**
     * 查询满足条件的试驾任务个数
     *
     * @param param
     * @return
     */
    Long queryTestDriveTaskCount(SacTestDriveTaskBO param);
}
