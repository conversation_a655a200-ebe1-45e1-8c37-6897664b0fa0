package com.smart.adp.domain.model.drive.service;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;

import java.util.List;

/**
 * @Description: 试驾车service
 * @Author: rik.ren
 * @Date: 2025/3/15 15:07
 **/
public interface ITestCarPrepareService {
    /**
     * 根据条件查询试驾车
     *
     * @param param
     * @return
     */
    List<TestCarPrepareBO> queryTestCarPrepareList(TestCarPrepareBO param, QueryColumn... columns);

    /**
     * 更新试驾车信息
     *
     * @param param
     * @return
     */
    Boolean modifyTestCarPrepare(TestCarPrepareBO param);

    /**
     * 更新试驾车信息给结束试驾使用
     *
     * @param param
     * @return
     */
    Boolean modifyTestCarPrepareForStop(TestCarPrepareBO param);

    /**
     * 根据条件查询整备信息
     *
     * @param param
     * @return
     */
    TestCarPrepareBO queryTestCarPrepare(TestCarPrepareBO param, QueryColumn... columns);
}
