package com.smart.adp.domain.model.drive.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.smart.adp.domain.enums.SexEnum;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 试乘试驾任务表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_test_drive_task", schema = "csc")
public class SacTestDriveTaskEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Id("ID")
    private String id;

    /**
     * 任务标题
     */
    @Column("TASK_TITLE")
    private String taskTitle;

    /**
     * 客户名称
     */
    @Column("CUST_NAME")
    private String custName;

    /**
     * 手机号
     */
    @Column("PHONE")
    private String phone;

    /**
     * 性别编码
     *
     * @see SexEnum
     */
    @Column("GENDER_CODE")
    private String genderCode;

    /**
     * 性别名称
     *
     * @see SexEnum
     */
    @Column("GENDER_NAME")
    private String genderName;

    /**
     * 试乘试驾类型
     */
    @Column("TEST_TYPE")
    private String testType;

    /**
     * 完成人员ID
     */
    @Column("TASK_PERSON_ID")
    private String taskPersonId;

    /**
     * 完成人员编码
     */
    @Column("TASK_PERSON_CODE")
    private String taskPersonCode;

    /**
     * 完成人员名称
     */
    @Column("TASK_PERSON_NAME")
    private String taskPersonName;

    /**
     * 完成人员专营店编码
     */
    @Column("TASK_PERSON_DLR_CODE")
    private String taskPersonDlrCode;

    /**
     * 完成人员专营店名称
     */
    @Column("TASK_PERSON_DLR_NAME")
    private String taskPersonDlrName;

    /**
     * 完成状态编码
     */
    @Column("TASK_STATE_CODE")
    private String taskStateCode;

    /**
     * 完成状态名称
     */
    @Column("TASK_STATE_NAME")
    private String taskStateName;

    /**
     * 备注
     */
    @Column("REMARK")
    private String remark;

    /**
     * 完成时间
     */
    @Column("BUSS_TIME")
    private LocalDateTime bussTime;

    /**
     * 试乘试驾车型
     */
    @Column("SMALL_CAR_TYPE_NAME")
    private String smallCarTypeName;

    /**
     * 预约试驾日期
     */
    @Column("APPOINTMENT_TEST_DATE")
    private String appointmentTestDate;

    /**
     * 预约试驾时间段
     */
    @Column("APPOINTMENT_TEST_TIME")
    private String appointmentTestTime;

    /**
     * 预约试驾开始时间
     */
    @Column("APPOINTMENT_START_TIME")
    private String appointmentStartTime;

    /**
     * 预约试驾结束时间
     */
    @Column("APPOINTMENT_END_TIME")
    private String appointmentEndTime;

    /**
     * 发起门店
     */
    @Column("SEND_DLR_NAME")
    private String sendDlrName;

    /**
     * 销售顾问名称
     */
    @Column("SALES_CONSULTANT_NAME")
    private String salesConsultantName;

    /**
     * 换店类型
     */
    @Column("MSG_TEST_TYPE")
    private String msgTestType;

    /**
     * 试驾单ID
     */
    @Column("OLD_TEST_DRIVE_SHEET_ID")
    private String oldTestDriveSheetId;

    /**
     * 意向级别编码
     */
    @Column("INTEN_LEVEL_CODE")
    private String intenLevelCode;

    /**
     * 意向级别名称
     */
    @Column("INTEN_LEVEL_NAME")
    private String intenLevelName;

    /**
     * 意向车型
     */
    @Column("INTEN_CAR_TYPE_NAME")
    private String intenCarTypeName;

    /**
     * 预计购车时间
     */
    @Column("PLAN_BUY_DATE_NAME")
    private String planBuyDateName;

    /**
     * 渠道来源
     */
    @Column("CHANNEL_NAME")
    private String channelName;

    /**
     * 新试驾单ID
     */
    @Column("NEW_TEST_DRIVE_SHEET_ID")
    private String newTestDriveSheetId;

    /**
     * 扩展信息
     */
    @Column("EXTEND_JSON")
    private String extendJson;

    /**
     * 邀请码
     */
    @Column("COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column("COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column("COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column("COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column("COLUMN5")
    private String column5;

    /**
     * 时间戳
     */
    @Column("_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column("OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column("GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column("OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column("GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column("IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column("SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column("SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;


    public SacTestDriveTaskEntity(String id) {
        this.id = id;
    }
}