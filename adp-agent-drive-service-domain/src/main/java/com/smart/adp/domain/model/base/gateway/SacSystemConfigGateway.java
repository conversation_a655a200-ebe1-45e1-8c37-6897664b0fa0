package com.smart.adp.domain.model.base.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.base.bo.SacSystemConfigBO;
import com.smart.adp.domain.model.base.valueObject.SacSystemConfigVO;

import java.util.List;

/**
 * @Description: 系统配置gateway接口
 * @Author: rik.ren
 * @Date: 2025/5/22 11:06
 **/
public interface SacSystemConfigGateway {
    /**
     * 根据条件查询系统配置信息
     * @param param
     * @return
     */
    List<SacSystemConfigBO> queryConfigInfoByCondition(SacSystemConfigBO param, QueryColumn... columns);
}
