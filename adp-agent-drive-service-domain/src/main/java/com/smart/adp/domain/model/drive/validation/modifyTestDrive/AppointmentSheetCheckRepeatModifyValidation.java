package com.smart.adp.domain.model.drive.validation.modifyTestDrive;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.service.SacAppointmentSheetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;

/**
 * @Description: 更新预约单查重
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Service
@Slf4j
public class AppointmentSheetCheckRepeatModifyValidation extends AbstractValidationHandler<SacTestDriveSheetBO> {

    @Autowired
    private SacAppointmentSheetService sacAppointmentSheetService;

    @Override
    protected void doValidate(SacTestDriveSheetBO context) throws BusicenException {
        checkRepeat(context);
    }

    /**
     * 查重
     *
     * @param context
     */
    private void checkRepeat(SacTestDriveSheetBO context) {
        try {
            // 普通试乘试驾拼接开始时间与结束时间
            if (!TestDriveTypeEnum.DEEP_TEST.getCode().equals(context.getTestType())) {
                String[] timeStrings = context.getAppointmentTestTime().split("-");
                String appointmentStartTime = context.getAppointmentTestDate().concat(" ").concat(timeStrings[0]).concat(":00");
                String appointmentEndTime = context.getAppointmentTestDate().concat(" ").concat(timeStrings[1]).concat(":00");
                context.setAppointmentStartTime(appointmentStartTime);
                context.setAppointmentEndTime(appointmentEndTime);
            } else {
                // 深度试驾处理
                // 提取日期和时间
                String appointmentTestDate = "";
                String appointmentTestTime = "";

                // 获取开始和结束时间
                String startTimeStr = context.getAppointmentStartTime();
                String endTimeStr = context.getAppointmentEndTime();

                if (startTimeStr != null && endTimeStr != null) {
                    // 解析开始和结束时间
                    LocalDateTime startTime = LocalDateTime.parse(startTimeStr, TimeConstant.DEFAULT_FORMATTER);
                    LocalDateTime endTime = LocalDateTime.parse(endTimeStr, TimeConstant.DEFAULT_FORMATTER);

                    appointmentTestDate = startTime.toLocalDate().toString();
                    appointmentTestTime = startTime.toLocalTime().toString() + "-" + endTime.toLocalTime().toString();
                }
                context.setAppointmentTestDate(appointmentTestDate);
                context.setAppointmentTestTime(appointmentTestTime);
//                appointmentMap.put("appointmentTestDate", appointmentTestDate);
//                appointmentMap.put("appointmentTestTime", appointmentTestTime);
            }
            // 判断是新增还是修改
            // 数据库查询是否存在此id
            SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
            appointmentSheetBO.setAppointmentId(context.getAppointmentId());
            List<SacAppointmentSheetBO> dbResult = sacAppointmentSheetService.queryAppointByCondition(appointmentSheetBO,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID, SAC_APPOINTMENT_SHEET_ENTITY.IS_TEST_DRIVE);
            if (CollectionUtil.isEmpty(dbResult)) {
                log.error("修改试驾单时，校验预约单信息不存在 {}", JSONObject.toJSONString(context));
                throw new BusicenException("预约单信息不存在");
            }
            // 当该试乘试驾单已经开始试乘试驾了就不能修改了
            if ("1".equals(dbResult.get(0).getIsTestDrive())) {
                throw new BusicenException("试乘试驾已开始，不能修改");
            }
            // 已经过期的试乘试驾预约单不能修改(跟进时不限制)
//            Calendar date = Calendar.getInstance();
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String dateString = simpleDateFormat.format(date.getTime());
//            if (simpleDateFormat.parse(dateString).after(simpleDateFormat.parse(list.get(0).getAppointmentStartTime()))
//                    && "0".equals(mapParam.get("isFollow"))) {
//                throw new BusicenException(message.get("APPOINTMENT-SHEET-14"));
//            }

            // 客户预约单查重:根据电话、预约试乘试驾日期、预约试乘试驾时间段、专营店编码查重
            // 普通试乘试驾查重
            // 查询用户是否已预约当前时间段
            appointmentSheetBO = new SacAppointmentSheetBO();
            appointmentSheetBO.setDlrCode(context.getDlrCode());
            appointmentSheetBO.setPlateNumber(context.getPlateNumber());
            appointmentSheetBO.setListTestStatus(Arrays.asList(TestDriveStatusEnum.NOT_STARTED.getCode(),
                    TestDriveStatusEnum.IN_PROGRESS.getCode()));
            appointmentSheetBO.setCustomerPhone(context.getCustomerPhone());
            appointmentSheetBO.setNotEqAppointmentId(context.getAppointmentId());
            appointmentSheetBO.setAppointmentStartTimeRepeat(context.getAppointmentStartTime());
            appointmentSheetBO.setAppointmentEndTimeRepeat(context.getAppointmentEndTime());
            List<SacAppointmentSheetBO> sacAppointmentSheetBOS = sacAppointmentSheetService.queryAppointByCondition(appointmentSheetBO,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID, SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME);
            log.info("修改试乘试驾预约单校验是否存在未开始的预约单 {}", JSONObject.toJSONString(sacAppointmentSheetBOS));

            // 校验数据库中已存在预约单信息是否和本次要修改的时间段重合，重合就返回错误信息
            if (CollectionUtil.isNotEmpty(sacAppointmentSheetBOS)) {
                // 解析当前预约时间段
                LocalDateTime currentStart = LocalDateTime.parse(context.getAppointmentStartTime(), TimeConstant.DEFAULT_FORMATTER);
                LocalDateTime currentEnd = LocalDateTime.parse(context.getAppointmentEndTime(), TimeConstant.DEFAULT_FORMATTER);

                boolean hasOverlap = sacAppointmentSheetBOS.stream()
                        .filter(Objects::nonNull)
                        .anyMatch(existing -> {
                            LocalDateTime existingStart = LocalDateTime.parse(existing.getAppointmentStartTime(),
                                    TimeConstant.DEFAULT_FORMATTER);
                            LocalDateTime existingEnd = LocalDateTime.parse(existing.getAppointmentEndTime(),
                                    TimeConstant.DEFAULT_FORMATTER);
                            // 时间重叠判断条件：现有开始 早于 当前选择结束 并且 现有结束 晚于 当前选择的开始
                            if (existingStart.isBefore(currentEnd) && existingEnd.isAfter(currentStart)) {
                                log.info("时间重叠判断条件 {} {}", existingStart, existingEnd);
                                log.info("时间重叠判断条件选择的时间 {} {}", currentEnd, currentStart);
                            }
                            return existingStart.isBefore(currentEnd) && existingEnd.isAfter(currentStart);
                        });
                if (hasOverlap) {
                    log.warn("在此时间段里您已预约试驾");
                    throw new BusicenException("在此时间段里您已预约试驾");
                }
            }
        } catch (Exception e) {
            log.error("更新试驾单checkRepeat异常", e);
            throw new BusicenException(e.getMessage());
        }
    }
}