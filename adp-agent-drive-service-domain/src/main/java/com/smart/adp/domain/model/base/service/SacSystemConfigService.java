package com.smart.adp.domain.model.base.service;

import com.smart.adp.domain.model.base.bo.SacSystemConfigBO;

import java.util.List;

/**
 * @Description: 系统配置service接口
 * @Author: rik.ren
 * @Date: 2025/5/22 11:06
 **/
public interface SacSystemConfigService {
    /**
     * 根据条件查询系统配置信息
     * @param param
     * @return
     */
    List<SacSystemConfigBO> queryConfigInfoByCondition(SacSystemConfigBO param);
}
