package com.smart.adp.domain.context;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.time.LocalDateTime;

/**
 * <p>
 * 时间
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/19
 */
public class TimeContext {

    private static final TransmittableThreadLocal<LocalDateTime> NOW_TTL = new TransmittableThreadLocal<LocalDateTime>() {
        @Override
        protected LocalDateTime initialValue() {
            return LocalDateTime.now();
        }
    };

    public static LocalDateTime now() {
        return NOW_TTL.get();
    }

    public static void remove() {
        NOW_TTL.remove();
    }
}
