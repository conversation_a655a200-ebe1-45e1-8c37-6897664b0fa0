package com.smart.adp.domain.model.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsBO;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsEventBO;
import com.smart.adp.domain.model.base.gateway.CdpLeadsServiceGateway;
import com.smart.adp.domain.model.base.service.CdpLeadsService;
import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsEventVO;
import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: adp推送数据给CDP的service实现
 * @Author: rik.ren
 * @Date: 2025/5/29 17:58
 **/
@Slf4j
@Service
public class CdpLeadsServiceImpl implements CdpLeadsService {
    @Autowired
    private CdpLeadsServiceGateway cdpLeadsServiceGateway;

    /**
     * 保存数据
     *
     * @param boParam
     * @return
     */
    @Override
    public Boolean saveData(IfsBaseCdpLeadsBO boParam) {
        return cdpLeadsServiceGateway.saveData(BeanUtil.copyProperties(boParam, IfsBaseCdpLeadsVO.class));
    }

    /**
     * 保存数据
     *
     * @param boParam
     * @return
     */
    @Override
    public Boolean saveDataEvent(IfsBaseCdpLeadsEventBO boParam) {
        return cdpLeadsServiceGateway.saveDataEvent(BeanUtil.copyProperties(boParam, IfsBaseCdpLeadsEventVO.class));
    }
}
