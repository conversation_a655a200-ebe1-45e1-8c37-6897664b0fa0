package com.smart.adp.domain.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

/**
 * @Description: 从Spring管理的类中获取Spring容器中的Bean
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/

@Service
public class ApplicationContextHolder implements ApplicationContextAware {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationContextHolder.class);
    private static ApplicationContext context;

    public static <T> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}