package com.smart.adp.domain.model.drive.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;
import com.smart.adp.domain.model.drive.gateway.TestCarPrepareGateway;
import com.smart.adp.domain.model.drive.service.ITestCarPrepareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.model.base.entity.table.AgentDlrInfoEntityTableDef.AGENT_DLR_INFO_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.TestCarPrepareEntityTableDef.TEST_CAR_PREPARE_ENTITY;


/**
 * 试驾车 服务层实现。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Service
public class TestCarPrepareServiceImpl implements ITestCarPrepareService {

    @Autowired
    private TestCarPrepareGateway testCarPrepareGateway;


    /**
     * 根据条件查询试驾车
     *
     * @param param
     * @return
     */
    @Override
    public List<TestCarPrepareBO> queryTestCarPrepareList(TestCarPrepareBO param, QueryColumn... queryColumns) {
        if (ObjectUtil.isEmpty(queryColumns)) {
            queryColumns = new QueryColumn[]{TEST_CAR_PREPARE_ENTITY.TESTCAR_PREPARE_ID, TEST_CAR_PREPARE_ENTITY.SALE_ORDER_CODE,
                    TEST_CAR_PREPARE_ENTITY.CAR_LICENCE_NO, TEST_CAR_PREPARE_ENTITY.APPLY_DLR_CODE, TEST_CAR_PREPARE_ENTITY.VIN,
                    TEST_CAR_PREPARE_ENTITY.TESTCAR_KILOMETERS, TEST_CAR_PREPARE_ENTITY.CREATED_DATE,
                    TEST_CAR_PREPARE_ENTITY.RESPONSE_ORDER_STATUS,
                    TEST_CAR_PREPARE_ENTITY.APPLY_CAR_TYPE_CODE, AGENT_DLR_INFO_ENTITY.DLR_SHORT_NAME};
        }
        return testCarPrepareGateway.queryTestCarPrepareList(param, queryColumns);
    }

    /**
     * 更新试驾车信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestCarPrepare(TestCarPrepareBO param) {
        return testCarPrepareGateway.modifyTestCarPrepare(param);
    }

    /**
     * 更新试驾车信息给结束试驾使用
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestCarPrepareForStop(TestCarPrepareBO param) {
        return testCarPrepareGateway.modifyTestCarPrepareForStop(param);
    }

    /**
     * 根据条件查询整备信息
     *
     * @param param
     * @param queryColumns
     * @return
     */
    @Override
    public TestCarPrepareBO queryTestCarPrepare(TestCarPrepareBO param, QueryColumn... queryColumns) {
        if (ObjectUtil.isEmpty(queryColumns)) {
            queryColumns = new QueryColumn[]{TEST_CAR_PREPARE_ENTITY.TESTCAR_PREPARE_ID, TEST_CAR_PREPARE_ENTITY.SALE_ORDER_CODE,
                    TEST_CAR_PREPARE_ENTITY.CAR_LICENCE_NO, TEST_CAR_PREPARE_ENTITY.APPLY_DLR_CODE, TEST_CAR_PREPARE_ENTITY.VIN,
                    TEST_CAR_PREPARE_ENTITY.TESTCAR_KILOMETERS, TEST_CAR_PREPARE_ENTITY.CREATED_DATE,
                    TEST_CAR_PREPARE_ENTITY.RESPONSE_ORDER_STATUS,
                    TEST_CAR_PREPARE_ENTITY.APPLY_CAR_TYPE_CODE, AGENT_DLR_INFO_ENTITY.DLR_SHORT_NAME};
        }
        return testCarPrepareGateway.queryTestCarPrepare(param, queryColumns);
    }
}