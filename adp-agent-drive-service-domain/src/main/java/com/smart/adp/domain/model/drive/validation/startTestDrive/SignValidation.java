package com.smart.adp.domain.model.drive.validation.startTestDrive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: 验证是否已签到
 * @Author: rik.ren
 * @Date: 2025/5/29 14:28
 **/
@Slf4j
@Service
public class SignValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {
    /**
     * 具体校验逻辑抽象方法（由子类实现）
     *
     * @param context 校验上下文对象
     */
    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        if (ObjectUtil.isEmpty(querySheetResult.getReceiverTime())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "请先点击签到");
        }
    }
}
