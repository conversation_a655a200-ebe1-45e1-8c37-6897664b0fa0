package com.smart.adp.domain.common.validation;

import com.smart.adp.domain.common.context.BusicenException;

/**
 * @param <T> 校验上下文类型
 * @Description: 校验处理器抽象基类（实现责任链基础逻辑），具体的校验由继承抽象类的子类去实现,todo优化成为允许并行校验，快速失败的
 * @Author: rik.ren
 * @Date: 2025/5/20 16:38
 **/
public abstract class AbstractValidationHandler<T> implements ValidationHandler<T> {
    // 责任链下一个处理器
    private ValidationHandler<T> next;

    @Override
    public ValidationHandler<T> setNext(ValidationHandler<T> handler) {
        this.next = handler;
        return handler;
    }

    @Override
    public void validate(T context) throws BusicenException {
        // 执行当前处理器校验逻辑
        doValidate(context);
        // 传递校验请求给下一个处理器
        if (next != null) {
            next.validate(context);
        }
    }

    /**
     * 具体校验逻辑抽象方法（由子类实现）
     *
     * @param context 校验上下文对象
     */
    protected abstract void doValidate(T context) throws BusicenException;
}