package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 试驾任务状态枚举
 * @Author: rik.ren
 * @Date: 2025/4/17 14:55
 */
@Getter
@AllArgsConstructor
public enum TestDriveTaskStatusEnum {

    /**
     * 未完成
     */
    UNFINISHED("0", "未完成"),

    /**
     * 已完成
     */
    FINISHED("1", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("2", "已取消"),

    ;

    private final String code;
    private final String desc;

    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static TestDriveTaskStatusEnum getByCode(String code) {
        return Arrays.stream(TestDriveTaskStatusEnum.values())
                     .filter(e -> e.getCode().equals(code))
                     .findAny()
                     .orElse(null);
    }
}
