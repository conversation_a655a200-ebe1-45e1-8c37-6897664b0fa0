package com.smart.adp.domain.utils.query;

import com.smart.adp.domain.utils.query.fillPolicy.FillPolicy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 多级存储查询协调者 - concurrent safety
 * </p>
 * <br/>
 * TODO
 *  <li/> hook support
 *  <li/> storage level > 2
 *  <li/> custom executor
 *
 * <AUTHOR>
 * @since 2025/7/30
 */
@Slf4j
@RequiredArgsConstructor
public class MultiStorageQueryCoordinator<K, V> {

    private final StorageQuery<K, V> fastStorageQuery;

    private final StorageQuery<K, V> slowStorageQuery;

    private final StorageFill<K, V> fastStorageFill;

    private final FillPolicy<K, V> fillPolicy;

    /**
     * multi storage qry
     *
     * @param keys keys
     * @return map
     */
    public Map<K, V> multiQueryAndFill(Collection<K> keys) {
        // fast qry
        Map<K, V> fastMap = fastStorageQuery.query(keys);
        // filter key
        Set<K> slowKeys = new HashSet<>(keys);
        slowKeys.removeAll(fastMap.keySet());
        if (slowKeys.isEmpty()) {
            return fastMap;
        }
        // slow qry
        Map<K, V> slowMap = slowStorageQuery.query(slowKeys);
        // handle null
        handleNull(slowKeys, slowMap);
        // fill
        doFill(slowMap);
        // merge & return
        fastMap.putAll(slowMap);
        return fastMap;
    }

    /**
     * multi storage qry <br/>
     * catch fast exception
     *
     * @param keys keys
     * @return map
     */
    public Map<K, V> multiQueryAndFillRobustly(Collection<K> keys) {
        Map<K, V> fastMap = new HashMap<>(keys.size());
        try {
            // fast qry
            fastMap.putAll(fastStorageQuery.query(keys));
        } catch (Exception e) {
            log.error("fastStorageQuery exception. {}", keys, e);
        }

        // filter key
        Set<K> slowKeys = new HashSet<>(keys);
        slowKeys.removeAll(fastMap.keySet());
        if (slowKeys.isEmpty()) {
            return fastMap;
        }
        // slow qry
        Map<K, V> slowMap = slowStorageQuery.query(slowKeys);

        // handle null
        handleNull(slowKeys, slowMap);

        try {
            // fill
            doFill(slowMap);
        } catch (Exception e) {
            log.error("MultiStorageQueryCoordinator fill exception. {}", keys, e);
        }

        // merge & return
        fastMap.putAll(slowMap);
        return fastMap;
    }

    /**
     * handle null - slow miss <br/>
     * only add null value
     *
     * @param slowKeys slow query keys
     * @param slowMap  slow query res
     */
    private void handleNull(Set<K> slowKeys, Map<K, V> slowMap) {
        if (fillPolicy.nullFill()) {
            Set<K> nullKeys = new HashSet<>(slowKeys);
            nullKeys.removeAll(slowMap.keySet());
            if (nullKeys.isEmpty()) {
                return;
            }

            nullKeys.forEach(k -> slowMap.put(k, fillPolicy.nullValueFunc().apply(k)));
        }
    }

    /**
     * fill by policy without null
     *
     * @param slowMap slow qry res
     */
    private void doFill(Map<K, V> slowMap) {
        if (!fillPolicy.shouldFill() || slowMap.isEmpty()) {
            return;
        }

        if (fillPolicy.async()) {
            // common pool
            CompletableFuture.runAsync(() -> fastStorageFill.fill(slowMap))
                             .exceptionally(e -> {
                                 log.error("MultiStorageQueryCoordinator async fill exception", e);
                                 return null;
                             });
        } else {
            fastStorageFill.fill(slowMap);
        }
    }
}
