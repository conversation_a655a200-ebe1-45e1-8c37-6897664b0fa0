package com.smart.adp.domain.runner;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.smart.adp.domain.enums.EnableEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Slf4j
@Component
public class FlexConfig {

    public void initFlex() {
        log.info("init Mybatis-Flex config");
        FlexGlobalConfig globalConfig = FlexGlobalConfig.getDefaultConfig();

        // 逻辑删除配置
        globalConfig.setNormalValueOfLogicDelete(EnableEnum.ENABLE.getCodeStr());
        globalConfig.setDeletedValueOfLogicDelete(EnableEnum.DISABLE.getCodeStr());

        // SQL 打印
        AuditManager.setAuditEnable(true);
        AuditManager.setMessageCollector(auditMessage ->
                log.info("{}, {}ms", auditMessage.getFullSql(), auditMessage.getElapsedTime())
        );
    }

    @PostConstruct
    public void init() {
        initFlex();
    }
}
