package com.smart.adp.domain.model.drive.validation.startTestDrive;

import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Description: 试乘试驾聚合根的返回结果基础接口
 * 试驾参数校验处理器（具体校验实现示例）
 * 功能：校验试驾类型参数是否合法
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Slf4j
@Service
public class StartDrivingParamValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {

    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        SacTestDriveSheetBO requestParam = context.getRequestParam();
        if (TestDriveTypeEnum.TEST_RIDE.getCode().equals(querySheetResult.getTestType()) && StringUtils.isEmpty(requestParam.getCustomerIdNumberAgreement())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "请上传身份证");
        }
        else if (TestDriveTypeEnum.TEST_DRIVE.getCode().equals(querySheetResult.getTestType()) && StringUtils.isEmpty(requestParam.getDrivingLicencePhoto())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "请上传驾驶证");
        }
    }
}