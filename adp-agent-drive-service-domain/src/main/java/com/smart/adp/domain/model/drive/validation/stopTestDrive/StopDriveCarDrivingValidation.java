package com.smart.adp.domain.model.drive.validation.stopTestDrive;

import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: 车辆是否在行驶中校验
 * @Author: rik.ren
 * @Date: 2025/5/29 14:30
 **/
@Slf4j
@Service
public class StopDriveCarDrivingValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {

    /**
     * 具体校验逻辑抽象方法（由子类实现）
     *
     * @param context 校验上下文对象
     */
    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        SacTestDriveSheetBO querySheetResult = context.getResult("querySheetResult");
        if (!querySheetResult.getTestStatus().equals(TestDriveStatusEnum.IN_PROGRESS.getCode())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "当前车辆没有在试驾中，无法结束试驾");
        }
    }
}
