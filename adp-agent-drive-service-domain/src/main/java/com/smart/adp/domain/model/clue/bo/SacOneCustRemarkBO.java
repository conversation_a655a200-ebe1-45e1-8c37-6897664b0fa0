package com.smart.adp.domain.model.clue.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
public class SacOneCustRemarkBO {

    /**
     * 客户 ID
     */
    @Schema(description = "客户 ID")
    private String custId;

    /**
     * 客户描述
     */
    @Schema(description = "客户描述")
    private String remark;

    /**
     * 用户阶段
     */
    @Schema(description = "用户阶段")
    private String userStage;

    /**
     * 上一个用户阶段
     */
    @Schema(description = "上一个用户阶段")
    private String previousUserStage;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    @Schema(description = "线索等级(H/A/B/C/D/E)")
    private String clueLevel;

    /**
     * 意向车型
     */
    @Schema(description = "意向车型")
    private String intentVehicleCode;

    /**
     * 竞品车型
     */
    @Schema(description = "竞品车型")
    private String competitiveVehicleCode;

    /**
     * 所在地
     */
    @Schema(description = "所在地")
    private String location;

    /**
     * 线索来源
     */
    @Schema(description = "线索来源")
    private String clueSource;

    /**
     * 用户阶段-了解的时间
     */
    @Schema(description = "用户阶段-了解的时间")
    private LocalDateTime knowDate;

    /**
     * 用户阶段-到店的时间
     */
    @Schema(description = "用户阶段-到店的时间")
    private LocalDateTime toStoreDate;

    /**
     * 用户阶段-试驾的时间
     */
    @Schema(description = "用户阶段-试驾的时间")
    private LocalDateTime testDriveDate;

    /**
     * 用户阶段-下定的时间
     */
    @Schema(description = "用户阶段-下定的时间")
    private LocalDateTime placeOrderDate;

    /**
     * 上次活跃时间
     */
    @Schema(description = "上次活跃时间")
    private LocalDateTime lastActiveTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateDate;


}
