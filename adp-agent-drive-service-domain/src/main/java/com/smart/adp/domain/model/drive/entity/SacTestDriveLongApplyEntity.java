package com.smart.adp.domain.model.drive.entity;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import lombok.*;

import java.time.LocalDateTime;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveLongApplyEntityTableDef.SAC_TEST_DRIVE_LONG_APPLY_ENTITY;

/**
 * 超长试驾申请表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Table(value = "t_sac_test_drive_long_apply", schema = "csc")
public class SacTestDriveLongApplyEntity {
    /**
     * 申请ID
     */
    @Column(value = "APPLY_ID")
    private String applyId;

    /**
     * 车牌号
     */
    @Column(value = "CAR_LICENCE_NO")
    private String carLicenceNo;

    /**
     * VIN码
     */
    @Column(value = "VIN")
    private String vin;

    /**
     * 车型编码
     */
    @Column(value = "CAR_TYPE_CODE")
    private String carTypeCode;

    /**
     * 车型名称
     */
    @Column(value = "CAR_TYPE_NAME")
    private String carTypeName;

    /**
     * 配置编码
     */
    @Column(value = "CARTYPE_CONFIG_CODE")
    private String cartypeConfigCode;

    /**
     * 配置名称
     */
    @Column(value = "CARTYPE_CONFIG_NAME")
    private String cartypeConfigName;

    /**
     * 颜色编码
     */
    @Column(value = "CAR_COLOR_CODE")
    private String carColorCode;

    /**
     * 颜色名称
     */
    @Column(value = "CAR_COLOR_NAME")
    private String carColorName;

    /**
     * 内饰编码
     */
    @Column(value = "CAR_INCOLOR_CODE")
    private String carIncolorCode;

    /**
     * 内饰名称
     */
    @Column(value = "CAR_INCOLOR_NAME")
    private String carIncolorName;

    /**
     * 可试驾天数
     */
    @Column(value = "CAN_TEST_DATE")
    private String canTestDate;

    /**
     * 申请门店
     */
    @Column(value = "APPLY_DLR_CODE")
    private String applyDlrCode;

    /**
     * 申请原因
     */
    @Column(value = "APPLY_REASON")
    private String applyReason;

    /**
     * 申请开始时间
     */
    @Column(value = "APPLY_TIME_BEGIN")
    private LocalDateTime applyTimeBegin;

    /**
     * 申请结束时间
     */
    @Column(value = "APPLY_TIME_END")
    private LocalDateTime applyTimeEnd;

    /**
     * 申请时长
     */
    @Column(value = "APPLY_TIME_LONG")
    private String applyTimeLong;

    /**
     * 审批人编码
     */
    @Column(value = "AUDIT_USER_ID")
    private String auditUserId;

    /**
     * 审批人名称
     */
    @Column(value = "AUDIT_EMP_NAME")
    private String auditEmpName;

    /**
     * 审批类型(1店长，2区域经理)
     */
    @Column(value = "AUDIT_TYPE")
    private String auditType;

    /**
     * 审批状态编码(审核中/通过/驳回/已取消)
     */
    @Column(value = "AUDIT_STATUS")
    private String auditStatus;

    /**
     * 审批状态名称(审核中/通过/驳回/已取消)
     */
    @Column(value = "AUDIT_STATUS_NAME")
    private String auditStatusName;

    /**
     * 审批意见
     */
    @Column(value = "AUDIT_REASON")
    private String auditReason;

    /**
     * 审批日期
     */
    @Column(value = "AUDIT_DATE")
    private LocalDateTime auditDate;

    /**
     * 扩展字段1
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 扩展字段2
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 扩展字段3
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 扩展字段4
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 扩展字段5
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 扩展字段6
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 扩展字段7
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 扩展字段8
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 扩展字段9
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 时间戳
     */
    @Column(value = "_MYCAT_OP_TIME")
    private Long mycatOpTime;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column(value = "OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column(value = "GROUP_CODE")
    private String groupCode;

    /**
     * 创建人ID
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建日期
     */
    @Column(value = "CREATED_DATE")
    private LocalDateTime createdDate;

    /**
     * 修改人ID
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新日期
     */
    @Column(value = "LAST_UPDATED_DATE")
    private LocalDateTime lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE", isLogicDelete = true)
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column(value = "SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column(value = "SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制ID
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    public SacTestDriveLongApplyEntity buildCondition(QueryWrapper wrapper) {
        wrapper.and(SAC_TEST_DRIVE_LONG_APPLY_ENTITY.CAR_LICENCE_NO.eq(getCarLicenceNo(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_LONG_APPLY_ENTITY.VIN.eq(getVin(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_LONG_APPLY_ENTITY.APPLY_TIME_BEGIN.lt(getApplyTimeEnd(), ObjectUtil::isNotNull))
                .and(SAC_TEST_DRIVE_LONG_APPLY_ENTITY.APPLY_TIME_END.gt(getApplyTimeBegin(), ObjectUtil::isNotNull));
        return this;
    }
}