package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 试驾类型
 * @Author: rik.ren
 * @Date: 2025/3/16 14:55
 **/
@Getter
@AllArgsConstructor
public enum TestDriveMethodEnum {
    /**
     * 试乘
     */
    DOOR_TO_DOOR("1", "上门试驾"),
    /**
     * 试驾
     */
    STORE("2", "门店试驾");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static TestDriveMethodEnum getByCode(String code) {
        return Arrays.stream(TestDriveMethodEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
