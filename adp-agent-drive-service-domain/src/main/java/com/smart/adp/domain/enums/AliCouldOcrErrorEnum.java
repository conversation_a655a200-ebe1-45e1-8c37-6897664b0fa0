package com.smart.adp.domain.enums;

import com.smart.common.basic.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 阿里云ocr识别错误码枚举
 */
@Getter
@AllArgsConstructor
public enum AliCouldOcrErrorEnum implements BasicEnum<String> {

    THROTTLING_USER("Throttling.User", "超过限流阈值"),

    UNSUPPORTED_IMAGE_FORMAT("unsupportedImageFormat", "图像内容错误或格式不支持"),

    UNMATCHED_IMAGE_TYPE("unmatchedImageType", "code: 400, The image type does not match the API operation"),

    EXCEEDED_IMAGE_CONTENT("exceededImageContent", "图像内容大小超过 10M"),

    ILLEGAL_IMAGE_CONTENT("illegalImageContent", " code: 400, The corresponding image content is missing"),

    ILLEGAL_IMAGE_URL("IllegalImageUrl", " code: 400, The image URL is unavailable or has timed out"),

    ;

    private final String code;

    private final String message;

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

}
