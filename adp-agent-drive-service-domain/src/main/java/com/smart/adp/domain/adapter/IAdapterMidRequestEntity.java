package com.smart.adp.domain.adapter;

import org.springframework.web.multipart.MultipartFile;

/**
 * @Description: 中台ocr接口适配器
 * @Author: rik.ren
 * @Date: 2025/03/11 18:10
 **/
public interface IAdapterMidRequestEntity {

    /**
     * 中台ocr接口适配器
     *
     * @param imageUrl
     * @return
     */
    Object midOCR(String imageUrl, MultipartFile imageBody);

    /**
     * 中台身份证信息接口
     * @param name
     * @param idCard
     * @return
     */
    Object midIdCardInfo(String name, String idCard);
}
