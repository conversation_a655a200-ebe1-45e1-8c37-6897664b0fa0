package com.smart.adp.domain.model.drive.bo;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestCarStatusCodeEnum;
import com.smart.adp.domain.enums.TestCarStatusEnum;
import com.smart.adp.domain.model.drive.entity.TestCarPrepareEntity;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.smart.adp.domain.model.drive.entity.table.TestCarPrepareEntityTableDef.TEST_CAR_PREPARE_ENTITY;


/**
 * 试驾车整备表 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class TestCarPrepareBO extends TestCarPrepareEntity {

    /**
     * 试驾车所属门店
     */
    private String dlrShortName;

    /**
     * 审批状态集合
     */
    private List<String> listResponseOrderStatus;

    /**
     * 要查询的车牌号集合
     */
    private List<String> listCarLicenceNo;
    /**
     * 要查询的车架号集合
     */
    private List<String> listCarVin;


    public TestCarPrepareBO testCarConditions(QueryWrapper wrapper) {
        wrapper.and(TEST_CAR_PREPARE_ENTITY.CAR_LICENCE_NO.eq(getCarLicenceNo(), StringUtil::hasText))
                .and(TEST_CAR_PREPARE_ENTITY.CAR_LICENCE_NO.in(getListCarLicenceNo(), CollectionUtil::isNotEmpty))
                .and(TEST_CAR_PREPARE_ENTITY.APPLY_DLR_CODE.eq(getApplyDlrCode(), StringUtil::hasText))
                .and(TEST_CAR_PREPARE_ENTITY.RESPONSE_ORDER_STATUS.eq(getResponseOrderStatus(), StringUtil::hasText))
                .and(TEST_CAR_PREPARE_ENTITY.RESPONSE_ORDER_STATUS.in(getListResponseOrderStatus(), CollectionUtil::isNotEmpty))
                .and(TEST_CAR_PREPARE_ENTITY.VIN.eq(getVin(), StringUtil::hasText))
                .and(TEST_CAR_PREPARE_ENTITY.VIN.in(getListCarVin(), CollectionUtil::isNotEmpty));
        return this;
    }

    public static TestCarPrepareBO buildTestCarPrepareBO(List<String> listCarVin, List<String> listResponseOrderStatus) {
        TestCarPrepareBO bo = new TestCarPrepareBO();
        bo.setListCarVin(listCarVin);
        bo.setListResponseOrderStatus(listResponseOrderStatus);
        return bo;
    }

    public static TestCarPrepareBO buildTestCarPrepareBO(String CarVin) {
        TestCarPrepareBO bo = new TestCarPrepareBO();
        bo.setVin(CarVin);
        return bo;
    }

    public TestCarPrepareBO buildModifyInfoBO(String carLicenceNo, Integer carStatusCode) {
        TestCarPrepareBO prepareBO = new TestCarPrepareBO();
        prepareBO.setCarStatusCode(carStatusCode);
        prepareBO.setCarLicenceNo(carLicenceNo);
        return prepareBO;
    }

    /**
     * 构建开始试驾时，对整备表的查询条件
     *
     * @param carLicenceNo
     * @return
     */
    public TestCarPrepareBO buildStartDrivingQueryBO(String carLicenceNo) {
        TestCarPrepareBO prepareBO = new TestCarPrepareBO();
        prepareBO.setResponseOrderStatus(TestCarStatusEnum.IN_SERVICE.getCode());
        prepareBO.setCarLicenceNo(carLicenceNo);
        return prepareBO;
    }

    /**
     * 构建开始试驾时，对整备表的操作字段
     *
     * @param carVin
     * @return
     */
    public TestCarPrepareBO buildStartDriveModifyBO(String carVin, String plateNumber) {
        TestCarPrepareBO prepareBO = new TestCarPrepareBO();
        prepareBO.setCarStatusCode(TestCarStatusCodeEnum.DRIVING.getCode());
        prepareBO.setCarStatusName(TestCarStatusCodeEnum.DRIVING.getDesc());
        prepareBO.setVin(carVin);
        prepareBO.setCarLicenceNo(plateNumber);
        prepareBO.setLastUpdatedDate(LocalDateTime.now());
        prepareBO.setModifier(UserInfoContext.get().getEmpName());
        return prepareBO;
    }

    /**
     * 构建结束试驾时，对整备表的操作字段
     *
     * @param carVin
     * @return
     */
    public TestCarPrepareBO buildStopDriveModifyBO(String carVin, String plateNumber, BigDecimal testEndRoadHaul) {
        TestCarPrepareBO prepareBO = new TestCarPrepareBO();
        prepareBO.setCarStatusCode(TestCarStatusCodeEnum.FREE.getCode());
        prepareBO.setCarStatusName(TestCarStatusCodeEnum.FREE.getDesc());
        prepareBO.setTestcarKilometers(testEndRoadHaul);
        prepareBO.setTestcarFrequency(1);
        prepareBO.setCarLicenceNo(plateNumber);
        prepareBO.setVin(carVin);
        prepareBO.setLastUpdatedDate(LocalDateTime.now());
        prepareBO.setModifier(UserInfoContext.get().getEmpName());
        return prepareBO;
    }
}
