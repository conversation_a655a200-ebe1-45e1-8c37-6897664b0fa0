package com.smart.adp.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 战败标签枚举
 * @Author: rik.ren
 * @Date: 2025/3/13 14:55
 **/
@Getter
@AllArgsConstructor
public enum DefeatFlagEnum {
    /**
     * 非战败
     */
    NORMAL(0, "正常"),
    /**
     * 战败
     */
    DEFEAT(1, "战败(全部)"),
    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;


    /**
     * 通过编码获取
     *
     * @param code 编码
     * @return result
     */
    public static DefeatFlagEnum getByCode(Integer code) {
        return Arrays.stream(DefeatFlagEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }
}
