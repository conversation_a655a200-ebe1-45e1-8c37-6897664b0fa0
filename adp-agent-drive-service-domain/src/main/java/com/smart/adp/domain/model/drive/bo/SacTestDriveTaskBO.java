package com.smart.adp.domain.model.drive.bo;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.enums.SexEnum;
import com.smart.adp.domain.enums.TestTaskStatusCodeEnum;
import com.smart.adp.domain.helper.StringHelper;
import com.smart.adp.domain.model.drive.entity.SacTestDriveTaskEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveTaskEntityTableDef.SAC_TEST_DRIVE_TASK_ENTITY;

/**
 * @Description: 试乘试驾任务表表BO。
 * @Author: rik.ren
 * @Date: 2025/6/30 13:50
 */
@Slf4j
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SacTestDriveTaskBO extends SacTestDriveTaskEntity {
    /**
     * 分页属性
     */
    private Integer pageNumber;

    private Integer pageSize;

    public SacTestDriveTaskBO(String testDriveId, String oldUpdateControlId) {
        super(testDriveId);
        this.oldUpdateControlId = oldUpdateControlId;
    }

    /**
     * 试驾任务id集合
     */
    private List<String> listNewTestDriveSheetId;

    /**
     * 原乐观锁
     */
    private String oldUpdateControlId;

    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public SacTestDriveTaskEntity buildTestDriveTaskCondition(QueryWrapper wrapper) {
        wrapper.and(SAC_TEST_DRIVE_TASK_ENTITY.NEW_TEST_DRIVE_SHEET_ID.eq(getNewTestDriveSheetId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.NEW_TEST_DRIVE_SHEET_ID.in(getListNewTestDriveSheetId(), CollectionUtil::isNotEmpty))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_ID.eq(getTaskPersonId(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_DLR_CODE.eq(getTaskPersonDlrCode(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_STATE_CODE.eq(getTaskStateCode(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.TEST_TYPE.eq(getTestType(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.PHONE.eq(getPhone(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.CUST_NAME.eq(getCustName(), StringUtil::hasText))
                .and(SAC_TEST_DRIVE_TASK_ENTITY.ID.eq(getId(), StringUtil::hasText));
        return this;
    }

    public SacTestDriveTaskBO buildTaskBO(String taskId, String oldUpdateControlId, SacTestDriveSheetBO sheetBO) {
        setId(taskId);
        setTaskStateCode(TestTaskStatusCodeEnum.FINISHED.getCode());
        setTaskStateName(TestTaskStatusCodeEnum.FINISHED.getDesc());
        setNewTestDriveSheetId(sheetBO.getTestDriveSheetId());
        setCustName(sheetBO.getCustomerName());
        setGenderCode(sheetBO.getCustomerSex());
        setGenderName(SexEnum.getByCode(sheetBO.getCustomerSex()).getDesc());
        setSmallCarTypeName(sheetBO.getSmallCarTypeName());
        setAppointmentTestDate(sheetBO.getAppointmentTestDate());
        setAppointmentTestTime(sheetBO.getAppointmentTestTime());
        setAppointmentStartTime(sheetBO.getAppointmentStartTime());
        setAppointmentEndTime(sheetBO.getAppointmentEndTime());
        setSendDlrName(sheetBO.getDlrName());
        setSalesConsultantName(sheetBO.getSalesConsultantName());
        setMsgTestType("1");
        setBussTime(sheetBO.getCreatedDate());
        setUpdateControlId(StringHelper.GetGUID());
        setOldUpdateControlId(oldUpdateControlId);
        setLastUpdatedDate(sheetBO.getLastUpdatedDate());
        setModifier(sheetBO.getModifier());
        return this;
    }
}