package com.smart.adp.domain.common.resp;

/**
 * <AUTHOR> created on 2020/2/28 15:07.
 * 为interfaces(用户界面层) 提供组件配置
 */
public class RespResult<T> {
    /**
     * 自定义业务码
     */
    private String code;
    /**
     * 自定义业务提示说明
     */
    private String message;
    /**
     * 自定义返回 数据结果集
     */
    private T result;

    private Boolean success;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public RespResult() {

    }

    public RespResult(String code) {
        this.code = code;
    }

    public RespResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public RespResult(String code, String message, T body) {
        this.code = code;
        this.message = message;
        this.result = body;
    }

    public RespResult(String code, String message, T result, Boolean success) {
        this.code = code;
        this.message = message;
        this.result = result;
        this.success = success;
    }

    public static RespResult<Void> ok() {
        return ok(null);
    }

    public static <T> RespResult<T> ok(T body) {
        return build(RespCode.OK, body, true);
    }

    public static <T> RespResult<T> ok(String message, T body) {
        return build(RespCode.OK.getCode(), message, body, true);
    }

    public static RespResult<ErrorBody> error(String code, String message, ErrorBody errorBody) {
        return build(code, message, errorBody, false);
    }

    public static RespResult<ErrorBody> error(ResultCode resultCode, ErrorBody errorBody) {
        return build(resultCode.getCode(), resultCode.getMessage(), errorBody, false);
    }

    public static RespResult<ErrorBody> error(ResultCode resultCode, String message, ErrorBody errorBody) {
        return build(resultCode.getCode(), message, errorBody, false);
    }

    public static <T> RespResult<T> fail(String message) {
        return fail(RespCode.FAIL, message, null, false);
    }

    public static <T> RespResult<T> fail(ResultCode resultCode) {
        return fail(resultCode, resultCode.getMessage(), null, false);
    }

    public static <T> RespResult<T> fail(ResultCode resultCode, String message, T data, Boolean success) {
        return build(resultCode.getCode(), message, data, success);
    }


    public static <T> RespResult<T> build(ResultCode resultCode, T body, Boolean success) {
        return build(resultCode.getCode(), resultCode.getMessage(), body, success);
    }

    /**
     * 以上所有构建均调用此底层方法
     *
     * @param stateCode 状态值
     * @param message   返回消息
     * @param body      返回数据体
     */
    public static <T> RespResult<T> build(String stateCode, String message, T body, Boolean success) {
        return new RespResult<>(stateCode, message, body, success);
    }

}
