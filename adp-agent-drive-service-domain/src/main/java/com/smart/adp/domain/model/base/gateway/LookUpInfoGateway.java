package com.smart.adp.domain.model.base.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.base.bo.LookUpInfoBO;

import java.util.List;

/**
 * @Description: LookUpInfo的gateway
 * @Author: rik.ren
 * @Date: 2025/7/1 13:37
 **/
public interface LookUpInfoGateway {
    /**
     * 根据条件查询字典信息
     *
     * @param param
     * @param columns
     * @return
     */
    List<LookUpInfoBO> queryLookUpInfo(LookUpInfoBO param, QueryColumn... columns);
}
