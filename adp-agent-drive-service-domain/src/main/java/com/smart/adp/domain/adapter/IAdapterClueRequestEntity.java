package com.smart.adp.domain.adapter;

import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: DTO适配器
 * @Author: rik.ren
 * @Date: 2025/03/11 18:10
 **/
public interface IAdapterClueRequestEntity {

    /**
     * 从clue查询线索的事件节点信息
     *
     * @param custIds
     * @return
     */
    Object queryClueEventFlowInfo(List<String> custIds);

    /**
     * 从clue查询线索的扩展信息
     *
     * @param custIds
     * @return
     */
    Object queryClueRemark(List<String> custIds);

    /**
     * 根据手机号查询线索
     *
     * @param custPhone
     * @return
     */
    Object queryClueInfoDlr(String custPhone);

    /**
     * 根据手机号查询线索
     *
     * @param custPhone
     * @return
     */
    Object queryClueInfoDlr(List<String> custPhone, Integer defeatFlag);

    /**
     * 创建线索，agent-clue服务需要的参数的适配器
     *
     * @param sacTestDriveSheetBO
     * @return
     */
    Object CreateAgentClue(SacTestDriveSheetBO sacTestDriveSheetBO, UserBusiEntity userBusiEntity);

    /**
     * 更新意向车型适配器
     *
     * @param custId
     * @param newIntentionCar
     * @return
     */
    Object modifyIntentionCar(String custId, String newIntentionCar, LocalDateTime lastTestdriverTime);
}
