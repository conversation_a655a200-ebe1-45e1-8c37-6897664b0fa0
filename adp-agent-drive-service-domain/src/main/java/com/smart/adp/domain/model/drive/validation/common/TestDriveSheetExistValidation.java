package com.smart.adp.domain.model.drive.validation.common;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.context.BusicenException;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.validation.AbstractValidationHandler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.service.ISacTestDriveSheetService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 校验试乘试驾单是否存在
 * @Author: rik.ren
 * @Date: 2025/5/30 11:23
 **/
@Slf4j
@Service
public class TestDriveSheetExistValidation extends AbstractValidationHandler<TestDriveAggregate.ValidationContext> {

    @Autowired
    private ISacTestDriveSheetService sheetService;

    /**
     * 具体校验逻辑抽象方法（由子类实现）
     *
     * @param context 校验上下文对象
     */
    @Override
    protected void doValidate(TestDriveAggregate.ValidationContext context) throws BusicenException {
        // 1. 查询试驾单
        SacTestDriveSheetBO querySheetParam = new SacTestDriveSheetBO(context.getRequestParam().getTestDriveSheetId(), Boolean.FALSE);
        SacTestDriveSheetBO querySheetResult = sheetService.queryTestDriveSheetInfoById(querySheetParam);
        if (ObjectUtil.isEmpty(querySheetResult) || StringUtils.isEmpty(querySheetResult.getTestDriveSheetId())) {
            throw new BusinessException(RespCode.FAIL.getCode(), "试乘试驾单不存在");
        }
        context.putResult("querySheetResult", querySheetResult);
    }
}
