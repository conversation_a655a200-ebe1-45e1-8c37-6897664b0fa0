package com.smart.adp.domain.model.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.base.gateway.BaseDataServiceGateway;
import com.smart.adp.domain.model.base.service.BaseDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description: 单号
 * @Author: rik.ren
 * @Date: 2025/5/24 14:06
 **/
@Slf4j
@Service
public class BaseDataServiceImpl implements BaseDataService {

    @Autowired
    private BaseDataServiceGateway baseDataServiceGateway;

    public String generateOrderCode(String billTypeId, UserBusiEntity userBusiEntity) {
        try {
            // 试乘试驾预约单单号生成
            log.info("试乘试驾预约单单号生成");
            if (ObjectUtil.isEmpty(userBusiEntity)) {
                userBusiEntity = UserInfoContext.get();
            }
            log.info("试乘试驾预约单单号生成参数 {} -{} -{}", billTypeId, userBusiEntity.getDlrID(), userBusiEntity.getToken());
            return baseDataServiceGateway.generateOrderCode(userBusiEntity.getDlrID(), billTypeId, userBusiEntity.getToken());
        } catch (Exception e) {
            throw new BusinessException(RespCode.FAIL.getCode(), "单号生成失败");
        }
    }

    /**
     * 根据条件查询员工信息
     *
     * @param param
     * @param columns
     * @return
     */
    @Override
    public List<UscMdmOrgEmployeeBO> queryUscMdmOrgEmp(UscMdmOrgEmployeeBO param, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        return baseDataServiceGateway.queryUscMdmOrgEmp(param, columns);
    }
}
