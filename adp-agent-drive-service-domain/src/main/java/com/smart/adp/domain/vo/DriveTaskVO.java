package com.smart.adp.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@Schema(description = "试驾任务VO")
public class DriveTaskVO {

    @Schema(description = "试驾任务Id")
    private String id;

    @Schema(description = "任务标题")
    private String taskTitle;

    @Schema(description = "邀请码")
    private String column1;

    @Schema(description = "任务人员编码")
    private String taskPersonCode;

    @Schema(description = "更新控制ID")
    private String updateControlId;

    @Schema(description = "业务时间")
    private LocalDateTime bussTime;

    @Schema(description = "是否驾驶或任务")
    private String isDriveOrTask = "0";

    @Schema(description = "组ID")
    private String groupId;

    @Schema(description = "修改人ID")
    private String modifier;

    @Schema(description = "测试类型")
    private String testType;

    @Schema(description = "任务状态名称")
    private String taskStateName;

    @Schema(description = "是否启用")
    private String isEnable;

    @Schema(description = "任务人员经销商名称")
    private String taskPersonDlrName;

    @Schema(description = "修改人姓名")
    private String modifyName;

    @Schema(description = "预约开始时间")
    private LocalDateTime appointmentStartTime;

    @Schema(description = "SDP用户ID")
    private String sdpUserId;

    @Schema(description = "OEM ID")
    private String oemId;

    @Schema(description = "创建人姓名")
    private String createdName;

    @Schema(description = "预约试驾日期")
    private String appointmentTestDate;

    @Schema(description = "发送经销商名称")
    private String sendDlrName;

    @Schema(description = "组编码")
    private String groupCode;

    @Schema(description = "创建人ID")
    private String creator;

    @Schema(description = "SDP组织ID")
    private String sdpOrgId;

    @Schema(description = "销售顾问姓名")
    private String salesConsultantName;

    @Schema(description = "OEM编码")
    private String oemCode;

    @Schema(description = "任务状态编码")
    private String taskStateCode;

    @Schema(description = "消息测试类型")
    private String msgTestType;

    @Schema(description = "客户姓名")
    private String custName;

    @Schema(description = "预约结束时间")
    private String appointmentEndTime;

    @Schema(description = "性别名称")
    private String genderName;

    @Schema(description = "任务人员姓名")
    private String taskPersonName;

    @Schema(description = "新试驾单ID")
    private String newTestDriveSheetId;

    @Schema(description = "性别编码")
    private String genderCode;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdatedDate;

    @Schema(description = "创建时间")
    private LocalDateTime createdDate;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "预约试驾时间段")
    private String appointmentTestTime;

    @Schema(description = "任务人员ID")
    private String taskPersonId;

    @Schema(description = "小型车型名称")
    private String smallCarTypeName;

    @Schema(description = "任务人员经销商编码")
    private String taskPersonDlrCode;

    @Schema(description = "线索客户 ID")
    private String clueCustId;

    @Schema(description = "线索状态")
    private String clueStatusCode;

    @Schema(description = "线索门店编码")
    private String clueDlrCode;

    @Schema(description = "线索人员编码")
    private String cluePersonId;

    @Schema(description = "线索人员名称")
    private String cluePersonName;
}
