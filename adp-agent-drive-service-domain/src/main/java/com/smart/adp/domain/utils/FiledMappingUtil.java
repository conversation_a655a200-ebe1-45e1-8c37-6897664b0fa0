package com.smart.adp.domain.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 线索公共方法
 *
 * <AUTHOR>
 */
public class FiledMappingUtil {

    /**
     * * 处理参数，将参数名、运算符、参数值分割并返回
     *
     * @param columInfo
     * @return
     * <AUTHOR>
     */
    public static List<Map<String, Object>> customHandle(String columInfo) {
        // 首先以逗号分割自定义参数
        String[] strings = String.valueOf(columInfo).split(";");
        // 遍历自定义参数数组,分割参数名,运算符以及值
        List<Map<String, Object>> columnList = new ArrayList<Map<String, Object>>();
        // 将参数放进一个map里,用于字段映射
        for (int i = 0; i < strings.length; i++) {
            // 获取参数名
            String columnName = strings[i].substring(0, strings[i].indexOf('['));
            // 获取运算符
            String operational = strings[i].substring(strings[i].indexOf('[') + 1, strings[i].indexOf(']'));
            // 获取参数值
            String str1 = strings[i].substring(0, strings[i].indexOf(']'));
            String value = strings[i].substring(str1.length() + 1, strings[i].length());
            //去除in/not in参数的单引号
            if ("in".equals(operational) || "not in".equals(operational)) {
                value = value.replace("'", "");
                value = value.substring(1, value.length() - 1);
            }
            Map<String, Object> columnMap = new HashMap<String, Object>(3);
            columnMap.put("columnName", columnName);
            columnMap.put("operational", operational);
            columnMap.put("value", value);
            // 将参数名,运算符,参数值放进参数集合中
            columnList.add(columnMap);
        }
        return columnList;
    }
}
