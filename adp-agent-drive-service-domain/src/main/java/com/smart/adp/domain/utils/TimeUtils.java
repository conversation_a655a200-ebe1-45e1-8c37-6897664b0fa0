package com.smart.adp.domain.utils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Description: 时间工具类
 * @Author: rik.ren
 * @Date: 2025/3/16 21:18
 **/
public class TimeUtils {
    public static String calculateDuration(LocalDateTime beginTime, LocalDateTime endTime) {
        if (beginTime == null || endTime == null) {
            return null;
        }

        Duration duration = Duration.between(beginTime, endTime);
        long seconds = duration.getSeconds();

        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            return minutes + "分钟" + remainingSeconds + "秒";
        } else {
            long hours = seconds / 3600;
            long remainingMinutes = (seconds % 3600) / 60;
            long remainingSeconds = seconds % 60;
            return hours + "小时" + remainingMinutes + "分钟" + remainingSeconds + "秒";
        }
    }

    public static LocalDateTime handleEnd(LocalDateTime time) {
        if (Objects.isNull(time)) {
            return null;
        }

        return time.toLocalDate()
                .atStartOfDay()
                .plusDays(1)
                .minusSeconds(1);
    }
}
