package com.smart.adp.domain.qry;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.enums.TestDriveTaskStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveTaskEntityTableDef.SAC_TEST_DRIVE_TASK_ENTITY;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class DriveTaskQry extends UserPageQry {

    /**
     * 试驾任务状态
     */
    private TestDriveTaskStatusEnum status;

    /**
     * 试驾任务 ID
     */
    private String id;

    public QueryWrapper conditionWrapper() {
        return QueryWrapper.create()
                           .and(SAC_TEST_DRIVE_TASK_ENTITY.ID.eq(getId(), StringUtil::hasText))
                           .and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_STATE_CODE.eq(getStatus().getCode()))
                           .and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_DLR_CODE.eq(getDlrCode(), StringUtil::hasText))
                           .and(SAC_TEST_DRIVE_TASK_ENTITY.TASK_PERSON_ID.eq(getUserId(), StringUtil::hasText));
    }
}
