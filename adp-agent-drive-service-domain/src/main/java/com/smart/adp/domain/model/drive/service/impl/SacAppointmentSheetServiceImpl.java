package com.smart.adp.domain.model.drive.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;
import com.smart.adp.domain.model.drive.gateway.SacAppointmentSheetGateway;
import com.smart.adp.domain.model.drive.service.SacAppointmentSheetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.smart.adp.domain.model.drive.entity.table.SacAppointmentSheetEntityTableDef.SAC_APPOINTMENT_SHEET_ENTITY;
import static com.smart.adp.domain.model.drive.entity.table.SacTestDriveSheetEntityTableDef.SAC_TEST_DRIVE_SHEET_ENTITY;

/**
 * @Description: 试乘试驾预约单service实现
 * @Author: rik.ren
 * @Date: 2025/5/22 15:20
 **/
@Slf4j
@Service
public class SacAppointmentSheetServiceImpl implements SacAppointmentSheetService {
    @Autowired
    private SacAppointmentSheetGateway sacAppointmentSheetGateway;

    /**
     * 根据条件查询预约单信息
     *
     * @param param
     * @return
     */
    @Override
    public List<SacAppointmentSheetBO> queryAppointByCondition(SacAppointmentSheetBO param, QueryColumn... columns) {
        if (ObjectUtil.isEmpty(columns)) {
            columns = new QueryColumn[]{SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_ID, SAC_APPOINTMENT_SHEET_ENTITY.DLR_CODE,
                    SAC_APPOINTMENT_SHEET_ENTITY.DLR_NAME, SAC_APPOINTMENT_SHEET_ENTITY.CUSTOMER_ID,
                    SAC_APPOINTMENT_SHEET_ENTITY.CUSTOMER_NAME,
                    SAC_APPOINTMENT_SHEET_ENTITY.CUSTOMER_PHONE, SAC_APPOINTMENT_SHEET_ENTITY.CUSTOMER_SEX,
                    SAC_APPOINTMENT_SHEET_ENTITY.SMALL_CAR_TYPE_CODE,
                    SAC_APPOINTMENT_SHEET_ENTITY.SMALL_CAR_TYPE_NAME, SAC_APPOINTMENT_SHEET_ENTITY.PLATE_NUMBER,
                    SAC_APPOINTMENT_SHEET_ENTITY.TEST_TYPE, SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_DATE,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_TEST_TIME, SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_START_TIME,
                    SAC_APPOINTMENT_SHEET_ENTITY.APPOINTMENT_END_TIME, SAC_APPOINTMENT_SHEET_ENTITY.CAR_VIN,
                    SAC_APPOINTMENT_SHEET_ENTITY.IS_TEST_DRIVE, SAC_TEST_DRIVE_SHEET_ENTITY.TEST_STATUS};
        }
        return sacAppointmentSheetGateway.queryAppointByCondition(param, columns);
    }

    /**
     * 根据条件查询预约单信息
     *
     * @param param
     * @return
     */
    @Override
    public Long queryAppointCountByCondition(SacAppointmentSheetBO param) {
        return sacAppointmentSheetGateway.queryAppointByCondition(param);
    }

    /**
     * 新建预约单
     *
     * @param param
     * @return
     */
    @Override
    public Boolean createAppointmentSheet(SacAppointmentSheetBO param) {
        return sacAppointmentSheetGateway.createAppointmentSheet(param);
    }

    /**
     * 更新预约单信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyAppointmentSheet(SacAppointmentSheetBO param) {
        return sacAppointmentSheetGateway.modifyAppointmentSheet(param);
    }

    /**
     * 删除预约单信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean deleteAppointmentSheet(SacAppointmentSheetBO param) {
        return sacAppointmentSheetGateway.deleteAppointmentSheet(param);
    }
}
