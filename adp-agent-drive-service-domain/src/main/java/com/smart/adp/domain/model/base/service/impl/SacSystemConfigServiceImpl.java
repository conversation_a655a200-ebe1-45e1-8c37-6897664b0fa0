package com.smart.adp.domain.model.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.common.annotation.SmartADPCache;
import com.smart.adp.domain.model.base.bo.SacSystemConfigBO;
import com.smart.adp.domain.model.base.gateway.SacSystemConfigGateway;
import com.smart.adp.domain.model.base.service.SacSystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.smart.adp.domain.model.base.valueObject.table.SacSystemConfigVOTableDef.SAC_SYSTEM_CONFIG_VO;
import static com.smart.adp.domain.model.base.valueObject.table.SacSystemConfigValueVOTableDef.SAC_SYSTEM_CONFIG_VALUE_VO;

/**
 * @Description: 系统配置service实现
 * @Author: rik.ren
 * @Date: 2025/5/22 11:07
 **/
@Slf4j
@Service
public class SacSystemConfigServiceImpl implements SacSystemConfigService {
    @Autowired
    private SacSystemConfigGateway sacSystemConfigGateway;

    /**
     * 根据条件查询系统配置信息
     *
     * @param param
     * @return
     */
    @SmartADPCache(value = "systemValueInfo", key = "#param.configCode", expire = 1, timeUnit = TimeUnit.HOURS)
    @Override
    public List<SacSystemConfigBO> queryConfigInfoByCondition(SacSystemConfigBO param) {
        if (ObjectUtil.isEmpty(param)) {
            return Collections.emptyList();
        }
        QueryColumn[] needColumn = {SAC_SYSTEM_CONFIG_VO.CONFIG_ID, SAC_SYSTEM_CONFIG_VO.CONFIG_CODE, SAC_SYSTEM_CONFIG_VO.CONFIG_RANGE,
                SAC_SYSTEM_CONFIG_VO.ORG_CODE, SAC_SYSTEM_CONFIG_VO.ORG_NAME, SAC_SYSTEM_CONFIG_VO.CONFIG_CODE,
                SAC_SYSTEM_CONFIG_VO.CONFIG_NAME,
                SAC_SYSTEM_CONFIG_VO.CONFIG_DESC, SAC_SYSTEM_CONFIG_VALUE_VO.VALUE_CODE, SAC_SYSTEM_CONFIG_VALUE_VO.VALUE_NAME};

        return sacSystemConfigGateway.queryConfigInfoByCondition(param, needColumn);
    }
}
