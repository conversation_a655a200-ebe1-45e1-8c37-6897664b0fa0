package com.smart.adp.domain.model.drive.service.impl;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.query.QueryOrderBy;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveTaskGateway;
import com.smart.adp.domain.model.drive.service.SacTestDriveTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 试乘试驾任务service实现
 * @Author: rik.ren
 * @Date: 2025/6/30 14:28
 **/
@Slf4j
@Service
public class SacTestDriveTaskServiceImpl implements SacTestDriveTaskService {
    @Autowired
    private SacTestDriveTaskGateway sacTestDriveTaskGateway;

    /**
     * 创建试驾任务信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean createTestDriveTask(SacTestDriveTaskBO param) {
        return null;
    }

    /**
     * 修改试驾任务信息
     *
     * @param param
     * @return
     */
    @Override
    public Boolean modifyTestDriveTask(SacTestDriveTaskBO param) {
        return sacTestDriveTaskGateway.modifyTestDriveTask(param);
    }

    /**
     * 根据条件查询一条试驾任务
     *
     * @param param
     * @param columns
     * @return
     */
    @Override
    public SacTestDriveTaskBO getTestDriveTask(SacTestDriveTaskBO param, QueryColumn... columns) {
        return sacTestDriveTaskGateway.getTestDriveTask(param, columns);
    }

    /**
     * 根据条件查询试驾任务集合
     *
     * @param param
     * @param columns
     * @return
     */
    @Override
    public List<SacTestDriveTaskBO> queryTestDriveTaskList(SacTestDriveTaskBO param, QueryColumn... columns) {
        return List.of();
    }

    /**
     * 分页查询试驾任务
     *
     * @param param
     * @param orderBy
     * @param columns
     * @return
     */
    @Override
    public DomainPage<SacTestDriveTaskBO> queryTestDriveTaskPage(SacTestDriveTaskBO param, QueryOrderBy orderBy, QueryColumn... columns) {
        return sacTestDriveTaskGateway.queryTestDriveTaskPage(param, orderBy, columns);
    }

    /**
     * 分页查询试驾任务
     *
     * @param param
     * @return
     */
    @Override
    public Long queryTestDriveTaskCount(SacTestDriveTaskBO param) {
        return sacTestDriveTaskGateway.queryTestDriveTaskCount(param);
    }
}
