package com.smart.adp.domain.model.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * date 2025/3/6 13:23
 * @description 代理商门店实体信息
 **/
@Table(value = "t_usc_mdm_org_dlr", schema = "mp")
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class AgentDlrInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 门店id
     */
    @Column("DLR_ID")
    private String dlrId;

    /**
     * 门店code
     */
    @Column("DLR_CODE")
    private String dlrCode;

    /**
     * 门店简称
     */
    @Column("DLR_SHORT_NAME")
    private String dlrShortName;

    /**
     * 门店全称
     */
    @Column("DLR_FULL_NAME")
    private String dlrFullName;

    /**
     * 门店类型
     */
    @Column("DLR_TYPE")
    private String dlrType;

    /**
     * 门店省id
     */
    @Column("PROVINCE_ID")
    private String provinceId;

    /**
     * 城市id
     */
    @Column("CITY_ID")
    private String cityId;

    /**
     * 县区id
     */
    @Column("COUNTY_ID")
    private String countyId;

    /**
     * 创建人
     */
    @Column("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column("CREATED_DATE")
    private LocalDateTime createdDate;

}
