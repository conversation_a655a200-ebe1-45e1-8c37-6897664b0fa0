package com.smart.adp.domain.model.base.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.Long;
import java.util.Date;
import java.sql.Timestamp;
import java.lang.String;

/**
 * 职员信息 实体类。
 *
 * <AUTHOR> automatic generation
 * @since 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(value = "t_usc_mdm_org_employee", schema = "mp")
public class UscMdmOrgEmployeeEntity {

    /**
     * 职员ID
     */
    @Id(keyType = KeyType.Generator, value = "uuid")
    private String empId;

    /**
     * 部门ID  T_MDM_ORG_DEPT DEPT_ID
     */
    @Column(value = "DEPT_ID")
    private String deptId;

    /**
     * 专营店编码
     */
    @Column(value = "DLR_CODE")
    private String dlrCode;

    /**
     * 职员编码
     */
    @Column(value = "EMP_CODE")
    private String empCode;

    /**
     * 职员姓名
     */
    @Column(value = "EMP_NAME")
    private String empName;

    /**
     * 出生日期
     */
    @Column(value = "BIRTH_DATE")
    private Date birthDate;

    /**
     * 工作电话
     */
    @Column(value = "WORK_TEL")
    private String workTel;

    /**
     * 手机号码
     */
    @Column(value = "MOBILE")
    private String mobile;

    /**
     * 性别编码
     */
    @Column(value = "GENDER_CODE")
    private String genderCode;

    /**
     * 学历编码
     */
    @Column(value = "DEGREE_CODE")
    private String degreeCode;

    /**
     * 个人通信地址
     */
    @Column(value = "PERSON_ADDR")
    private String personAddr;

    /**
     * 邮编
     */
    @Column(value = "ZIP")
    private String zip;

    /**
     * 邮箱
     */
    @Column(value = "EMAIL")
    private String email;

    /**
     * 传真号
     */
    @Column(value = "FAX")
    private String fax;

    /**
     * 国籍编码
     */
    @Column(value = "NATIONALITY_CODE")
    private String nationalityCode;

    /**
     * 婚姻状况编码
     */
    @Column(value = "MARRIAGED_CODE")
    private String marriagedCode;

    /**
     * 籍贯
     */
    @Column(value = "NATIVE_PLACE")
    private String nativePlace;

    /**
     * 毕业院校
     */
    @Column(value = "SCHOOL")
    private String school;

    /**
     * 专业
     */
    @Column(value = "DEGREEPRO")
    private String degreepro;

    /**
     * 技能特长
     */
    @Column(value = "SKILL_SPECIAL")
    private String skillSpecial;

    /**
     * 家庭电话
     */
    @Column(value = "FAMILY_PHONE")
    private String familyPhone;

    /**
     * 紧急联络人
     */
    @Column(value = "SECOND_MAN")
    private String secondMan;

    /**
     * 紧急联络人电话
     */
    @Column(value = "SECOND_MAN_TEL")
    private String secondManTel;

    /**
     * 领取驾照日期
     */
    @Column(value = "DRIVER_DATE")
    private Date driverDate;

    /**
     * 民族编码
     */
    @Column(value = "NATION_CODE")
    private String nationCode;

    /**
     * 汽车行业从业时间
     */
    @Column(value = "BUSINESS_DATE")
    private Date businessDate;

    /**
     * 入职日期
     */
    @Column(value = "EMPLOY_DATE")
    private Date employDate;

    /**
     * 是否有驾照
     */
    @Column(value = "IS_DRIVER")
    private String isDriver;

    /**
     * 招聘方式
     */
    @Column(value = "EMPLOY_TYPE")
    private String employType;

    /**
     * 政治面貌编码
     */
    @Column(value = "POLITICS_CODE")
    private String politicsCode;

    /**
     * 证件类型编码
     */
    @Column(value = "CRED_TYPE_CODE")
    private String credTypeCode;

    /**
     * 证件号
     */
    @Column(value = "CRED_NO")
    private String credNo;

    /**
     * 职称
     */
    @Column(value = "CLASS")
    private String title;

    /**
     * 职员照片
     */
    @Column(value = "EMP_PIC")
    private String empPic;

    /**
     * 自我评价
     */
    @Column(value = "SELF_ESTIMATE")
    private String selfEstimate;

    /**
     * 专营店ID
     */
    @Column(value = "DLR_ID")
    private String dlrId;

    /**
     * 第一岗位
     */
    @Column(value = "STATION_ID")
    private String stationId;

    /**
     * 二级网点ID 如属于一级店则为空
     */
    @Column(value = "SEC_DLR_ID")
    private String secDlrId;

    /**
     * 二级网点编码 如属于一级店则为空
     */
    @Column(value = "SEC_DLR_CODE")
    private String secDlrCode;

    /**
     * 直属上司ID
     */
    @Column(value = "HEAD_MANAGER")
    private String headManager;

    /**
     * 离职日期
     */
    @Column(value = "LEAVE_DATE")
    private Date leaveDate;

    /**
     * 是否冻结
     */
    @Column(value = "IS_FROZEN")
    private String isFrozen;

    /**
     * 一网编码  一网DLRCODE
     */
    @Column(value = "OLD_DLR_CODE")
    private String oldDlrCode;

    /**
     * 一网ID  一网DLR_ID
     */
    @Column(value = "OLD_DLR_ID")
    private String oldDlrId;

    /**
     * 时间戳
     */
    @Column(value = "_MYCAT_OP_TIME")
    private Long MycatOpTime;

    /**
     * 备注
     */
    @Column(value = "REMARK")
    private String remark;

    /**
     * 厂商标识ID
     */
    @Column(value = "OEM_ID")
    private String oemId;

    /**
     * 集团标识ID
     */
    @Column(value = "GROUP_ID")
    private String groupId;

    /**
     * 厂商标识
     */
    @Column(value = "OEM_CODE")
    private String oemCode;

    /**
     * 集团标识
     */
    @Column(value = "GROUP_CODE")
    private String groupCode;

    /**
     * 创建人
     */
    @Column(value = "CREATOR")
    private String creator;

    /**
     * 创建人姓名
     */
    @Column(value = "CREATED_NAME")
    private String createdName;

    /**
     * 创建时间
     */
    @Column(value = "CREATED_DATE")
    private Timestamp createdDate;

    /**
     * 最后更新人员
     */
    @Column(value = "MODIFIER")
    private String modifier;

    /**
     * 修改人姓名
     */
    @Column(value = "MODIFY_NAME")
    private String modifyName;

    /**
     * 最后更新时间
     */
    @Column(value = "LAST_UPDATED_DATE")
    private Timestamp lastUpdatedDate;

    /**
     * 是否可用
     */
    @Column(value = "IS_ENABLE")
    private String isEnable;

    /**
     * SDP用户ID
     */
    @Column(value = "SDP_USER_ID")
    private String sdpUserId;

    /**
     * SDP组织ID
     */
    @Column(value = "SDP_ORG_ID")
    private String sdpOrgId;

    /**
     * 并发控制字段
     */
    @Column(value = "UPDATE_CONTROL_ID")
    private String updateControlId;

    /**
     * 用户ID
     */
    @Column(value = "USER_ID")
    private String userId;

    /**
     * 用户名称
     */
    @Column(value = "USER_NAME")
    private String userName;

    /**
     * 爱学在线是否通过
     */
    @Column(value = "PASS_MAP_USER")
    private String passMapUser;

    /**
     * 培训是否通过(1是,0否)
     */
    @Column(value = "COLUMN1")
    private String column1;

    /**
     * 兼职岗位
     */
    @Column(value = "COLUMN2")
    private String column2;

    /**
     * 所属省份
     */
    @Column(value = "COLUMN3")
    private String column3;

    /**
     * 所属城市
     */
    @Column(value = "COLUMN4")
    private String column4;

    /**
     * 所属区县
     */
    @Column(value = "COLUMN5")
    private String column5;

    /**
     * 身份证正面
     */
    @Column(value = "COLUMN6")
    private String column6;

    /**
     * 身份证反面
     */
    @Column(value = "COLUMN7")
    private String column7;

    /**
     * 门店员工标识1
     */
    @Column(value = "COLUMN8")
    private String column8;

    /**
     * 是否留学(1是,0否)
     */
    @Column(value = "COLUMN9")
    private String column9;

    /**
     * 扩展字段10
     */
    @Column(value = "COLUMN10")
    private String column10;

    /**
     * 离职原因编码
     */
    @Column(value = "RESIGNATION_REASON_CODE")
    private String resignationReasonCode;

    /**
     * 离职原因名称
     */
    @Column(value = "RESIGNATION_REASON_NAME")
    private String resignationReasonName;

    /**
     * 老部门ID
     */
    @Column(value = "OLD_DEPT_ID")
    private String oldDeptId;

    /**
     * 状态（1在职，2离职）
     */
    @Column(value = "USER_STATUS")
    private String userStatus;

    /**
     * 对应厂家员工编码
     */
    @Column(value = "FACTORY_EMP_CODE")
    private String factoryEmpCode;

    /**
     * 职级
     */
    @Column(value = "DUTY")
    private String duty;

    /**
     * 职级名称
     */
    @Column(value = "DUTY_NAME")
    private String dutyName;

    /**
     * 团队
     */
    @Column(value = "GROUP_TEAM")
    private String groupTeam;

    /**
     * 团队名称
     */
    @Column(value = "GROUP_TEAM_NAME")
    private String groupTeamName;

    /**
     * 证件类型名称
     */
    @Column(value = "CRED_TYPE_NAME")
    private String credTypeName;

    /**
     * 生效日期
     */
    @Column(value = "ACTIVE_DATE")
    private Date activeDate;

    /**
     * 失效日期
     */
    @Column(value = "DISABLE_DATE")
    private Date disableDate;

    @Column(value = "SHOW_PHONE")
    private String showPhone;

    /**
     * ORG_ID
     */
    @Column(value = "ORG_ID")
    private String orgId;

    /**
     * ORG_NAME
     */
    @Column(value = "ORG_NAME")
    private String orgName;

    /**
     * 专营店账号id
     */
    @Column(value = "DLR_USER_ID")
    private String dlrUserId;

    /**
     * 专营店账号名
     */
    @Column(value = "DLR_USER_NAME")
    private String dlrUserName;

    /**
     * 灰度字段
     */
    @Column(value = "grayScale")
    private String grayScale;

    /**
     * 灰度筛选字段
     */
    @Column(value = "IS_GRAY_SCALE")
    private String isGrayScale;

    /**
     * 头像
     */
    @Column(value = "HEAD_PORTRAIT")
    private String headPortrait;

    /**
     * 是否修改岗位
     */
    @Column(value = "IS_UPDATE_STATION")
    private String isUpdateStation;

    /**
     * 组织类型
     */
    @Column(value = "ORG_TYPE")
    private String orgType;

    /**
     * 兼职岗位名称
     */
    @Column(value = "STATION_NAME")
    private String stationName;

    /**
     * 附件1
     */
    @Column(value = "IMAGE1")
    private String image1;

    /**
     * 附件2
     */
    @Column(value = "IMAGE2")
    private String image2;

    /**
     * 附件3
     */
    @Column(value = "IMAGE3")
    private String image3;

    /**
     * 附件4
     */
    @Column(value = "IMAGE4")
    private String image4;

    /**
     * 附件5
     */
    @Column(value = "IMAGE5")
    private String image5;

    /**
     * 企微创建人员标识（1：已创建）
     */
    @Column(value = "QY_CREATE_FLAG")
    private String qyCreateFlag;

    /**
     * 是否创建新部门(1:创建新部门)
     */
    @Column(value = "QY_DEPARTMENT_FLAG")
    private String qyDepartmentFlag;

    /**
     * 岗位认证
     */
    @Column(value = "PASS_TMS")
    private String passTms;

    /**
     * 冻结原因：11考勤异常、12未完成培训、13门店未上线
     */
    @Column(value = "FROZEN_CASE")
    private String frozenCase;

}
