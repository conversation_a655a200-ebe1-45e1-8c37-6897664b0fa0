package com.smart.adp.domain.model.base.gateway;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;

import java.util.List;

/**
 * @Description: base服务的gateway
 * @Author: rik.ren
 * @Date: 2025/5/24 14:08
 **/
public interface BaseDataServiceGateway {
    /**
     * 生成单号
     *
     * @param dlrId
     * @param billTypeId
     * @param token
     * @return
     */
    String generateOrderCode(String dlrId, String billTypeId, String token) throws Exception;

    /**
     * 根据条件查询员工信息
     *
     * @param param
     * @param columns
     * @return
     */
    List<UscMdmOrgEmployeeBO> queryUscMdmOrgEmp(UscMdmOrgEmployeeBO param, QueryColumn... columns);
}
