package com.smart.adp.domain.model.base.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserBusiEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userID;

    private String userName;
    //员工ID
    private String empID;

    //员工名称
    private String empName;

    //组织类型 0厂商，1门店，2集团 3供应商
    private String orgType;

    //组织ID
    private String orgID;

    //组织编码
    private String orgCode;

    //组织名称
    private String orgName;

    //专营店ID
    private String dlrID;

    //专营店名称
    private String dlrName;

    //专营店编码
    private String dlrCode;

    //父专营店ID
    private String parentDlrID;

    //父专营店名称
    private String parentDlrName;

    //父专营店编码
    private String parentDlrCode;

    //系统岗位Id 以逗号分隔
    private String posID;

    //系统岗位编码  以逗号分隔
    private String posCode;

    private String oemID = "1";

    private String oemCode = "1";

    private String groupCode = "1";

    private String groupID = "1";

    private String brandCode = "1";

    //经销商建店状态
    private String dlrStatus;

    //经销商类别
    private String dlrOrgType;

    //所属主机厂编码
    private String belongFactoryCode;

    //所属主机厂ID
    private String belongFactoryId;

    private String solutionId;

    //所属公司ID
    private String companyID;

    private String partId;

    private String mobile;

    private String stationName;

    private String stationId;

    private String empCode;

    private String dlrShortName;

    //灰度
    private String grayScale;

    /**
     * 代理商编码
     */
    private String agentCode;

    /**
     * 代理商id
     */
    private String agentId;

    /**
     * 当前登录的token
     */
    private String token;

    /**
     * 所属公司名称
     */
    private String companyName;
}
