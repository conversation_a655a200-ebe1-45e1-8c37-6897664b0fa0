package com.smart.adp.domain.model.drive.validation.createTestDrive;

import com.smart.adp.domain.common.validation.ValidationHandler;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 创建试乘试驾单校验链
 * 试驾校验链构建器
 * 创建预约单的校验链
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Service
@Slf4j
public class CreateTestDriveSheetValidationChain {

    @Autowired
    ParameterValidation paramValidation;
    @Autowired
    private AppointmentSheetCheckTimeValidation checkTimeValidation;
    @Autowired
    private AppointmentSheetCheckRepeatValidation checkRepeatValidation;
    @Autowired
    private AppointmentSheetCheckThresholdValueValidation checkThresholdValueValidation;

    /**
     * 构建试驾校验责任链
     *
     * @return 返回组装好的校验处理器链（当前顺序：参数校验 -> 时间校验 -> 重复校验 -> 创建预约单预约人数阈值校验）
     */
    public ValidationHandler<SacTestDriveSheetBO> buildValidationChain() {
        // 构建链式结构
        paramValidation.setNext(checkTimeValidation)
                .setNext(checkRepeatValidation)
                .setNext(checkThresholdValueValidation);
        return paramValidation;
    }
}