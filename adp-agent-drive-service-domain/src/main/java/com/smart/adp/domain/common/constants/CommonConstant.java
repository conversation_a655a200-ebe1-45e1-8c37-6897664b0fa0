package com.smart.adp.domain.common.constants;

/**
 * <AUTHOR>
 * date 2025/3/5 14:04
 * @description 线索关键常量
 **/
public class CommonConstant {

    // 常量定义（可统一管理）
    public static final String CLUE_TYPE_DLR = "dlrClue";

    public static final String DLR_CODE_PV = "PV";

    public static final String CONFIG_CLUE_SHARE_SWITCH = "CLUE_DLR_SHARE_SWITCH";

    public static final String REMOVE_CONFIG = "REMOVE_CONFIG";

    public static final String SHARE_SWITCH_ON = "1";

    public static final String SHARE_ORG_CODE = "share";

    public static final String SHARE_ORG_NAME = "共享专营店";

    public static final String ADP_TRANSFER = "ADP-TRANSFER";

    public static final String G00_FLAG = "false";

    public static final String G00_FLAG_DESC = "smartId已存在，不进行新建";

    public static final String CLUE_PROVINCE_CITY_COUNTY = "CLUE_PROVINCE_CITY_COUNTY";

    public static final String OVERTIME_SWITCH = "OVERTIME_SWITCH";

    public static final String AUTO_FP_SWITCH = "AUTO_FP_SWITCH";

    public static final String INFO_CHAN_M_NAME_ACTIVITY = "活动报名";

    public static final String INFO_CHAN_M_CODE_ACTIVITY = "ho_operationcampaign";

    public static final String INFO_CHAN_D_CODE_ACTIVITY = "agent_othermarket";

    public static final String CACHE_PREFIX = "agent:";

    public static final String CAR_TYPE_CACHE_PREFIX = "carType:";

    public static final String CLUE_COMMON_MSG = "，请勿重复添加";

    public static final String DLR_EMP_CACHE_PREFIX = "dlrEmp:";

    /**
     * 员工在职状态
     */
    public static final String USER_STATUS_ACTIVE = "1";

    public static final Integer DLR_EMP_CACHE_EXPIRE = 30 * 60;

    public static final Integer DLR_EMP_CACHE_EXPIRE_30 = 30 * 24 * 60 * 60;

    public static final Integer DLR_EMP_CACHE_EXPIRE_HALF = 30 * 60;

}
