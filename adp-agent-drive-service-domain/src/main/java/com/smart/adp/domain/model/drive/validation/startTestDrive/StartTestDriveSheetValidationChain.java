package com.smart.adp.domain.model.drive.validation.startTestDrive;

import com.smart.adp.domain.common.validation.ValidationHandler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.validation.common.TestDriveSheetExistValidation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 开始试驾试乘试驾校验链
 * 试驾校验链构建器
 * 创建预约单的校验链
 * @Author: rik.ren
 * @Date: 2025/5/21 18:04
 **/
@Slf4j
@Service
public class StartTestDriveSheetValidationChain {
    @Autowired
    TestDriveSheetExistValidation testDriveSheetExistValidation;
    @Autowired
    StartDrivingParamValidation startDrivingParamValidation;
    @Autowired
    TestCarPrepareExistValidation testCarPrepareExistValidation;
    @Autowired
    private SignValidation signValidation;
    @Autowired
    private CarDrivingValidation carDrivingValidation;

    /**
     * 构建试驾校验责任链
     *
     * @return 返回组装好的校验处理器链（当前顺序：试驾单是否存在 -> 参数校验 -> 试驾车是否存在 -> 是否已签到 -> 试驾车状态）
     */
    public ValidationHandler<TestDriveAggregate.ValidationContext> buildValidationChain() {
        // 构建链式结构
        testDriveSheetExistValidation
                .setNext(startDrivingParamValidation)
                .setNext(testCarPrepareExistValidation)
                .setNext(signValidation)
                .setNext(carDrivingValidation);
        return testDriveSheetExistValidation;
    }
}