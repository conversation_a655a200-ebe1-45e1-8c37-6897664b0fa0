package com.smart.adp.domain.model.base.service.impl;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.base.bo.LookUpInfoBO;
import com.smart.adp.domain.model.base.gateway.LookUpInfoGateway;
import com.smart.adp.domain.model.base.service.LookUpInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: LookUpInfoService的实现
 * @Author: rik.ren
 * @Date: 2025/7/1 13:47
 **/
@Slf4j
@Service
public class LookUpInfoServiceImpl implements LookUpInfoService {
    @Autowired
    private LookUpInfoGateway lookUpInfoGateway;

    /**
     * 根据条件查询字典信息
     *
     * @param param
     * @param columns
     * @return
     */
//    @SmartADPCache(value = "lookUpInfo", key = "#param.lookUpTypeCode", expire = 1, timeUnit = TimeUnit.HOURS)
    @Override
    public List<LookUpInfoBO> queryLookUpInfo(LookUpInfoBO param, QueryColumn... columns) {
        List<LookUpInfoBO> result = lookUpInfoGateway.queryLookUpInfo(param, columns);
        return result;
    }
}
