package com.smart.adp.domain.model.clue.gateway;

import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.bo.ClueEventFlowBO;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.clue.bo.SacOneCustRemarkBO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 门店线索gateway
 * @Author: rik.ren
 * @Date: 2025/5/27 10:14
 **/
public interface DlrClueGateway {
    /**
     * 创建线索
     *
     * @param param
     * @param userBusiEntity
     * @return
     */
    SacClueInfoDlrBO saveAgentDlrClue(Object param, UserBusiEntity userBusiEntity);

    /**
     * 根据手机号查询线索
     *
     * @param custPhone
     * @return
     */
    SacClueInfoDlrBO queryClueInfoDlr(String custPhone);

    /**
     * 根据手机号集合查询线索
     *
     * @param custPhone  手机号集合
     * @param defeatFlag 战败还是非战败标签 @see DefeatFlagEnum
     * @return
     */
    //List<SacClueInfoDlrBO> queryListClueInfoDlr(List<String> custPhone, Integer defeatFlag);

    /**
     * 查询用户旅程
     */
    List<ClueEventFlowBO> queryUserEventFlow(List<String> custPhoneList);

    /**
     * 查询线索的扩展信息
     *
     * @param custPhoneList
     * @return
     */
    List<SacOneCustRemarkBO> queryClueRemark(List<String> custPhoneList);

    /**
     * 更新意向车型
     *
     * @param custId
     * @param newIntentionCar
     * @param lastTestdriverTime
     * @param userBusiEntity
     */
    String modifyIntentionCar(String custId, String newIntentionCar, LocalDateTime lastTestdriverTime, UserBusiEntity userBusiEntity);
}
