package com.smart.adp.domain.adapter;

import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import org.springframework.http.HttpEntity;

/**
 * @Description: TDA适配器
 * @Author: rik.ren
 * @Date: 2025/03/11 18:10
 **/
public interface IAdapterTDARequestEntity {

    /**
     * 结束试驾推送信息给TDA
     *
     * @param boParam
     * @return
     */
    HttpEntity<Object> stopTestDriveSendTda(SacTestDriveSheetBO boParam, UscMdmOrgEmployeeBO empBoParam);
}
