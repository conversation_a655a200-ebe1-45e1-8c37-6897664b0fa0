package com.smart.adp.domain.qry;

import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.utils.UserUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
public class UserPageQry extends PageQry {

    /**
     * 当前用户门店编码
     */
    private String dlrCode;

    /**
     * 当前用户 userId
     */
    private String userId;

    /**
     * 填充当前用户信息 AOP?
     */
    public final void fillUserInfo() {
        UserBusiEntity user = UserInfoContext.get();
        setDlrCode(user.getDlrCode());
        if (UserUtil.productExpertValid()) {
            setUserId(user.getUserID());
        }
    }
}
