package com.smart.adp.domain.model.drive.service;

import com.mybatisflex.core.query.QueryColumn;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.common.DomainPage;

import java.util.List;
import java.util.Map;

/**
 * @Description: 试驾service
 * @Author: rik.ren
 * @Date: 2025/3/15 15:07
 **/
public interface ISacTestDriveSheetService {
    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    List<SacTestDriveSheetBO> queryTestDriveSheetList(SacTestDriveSheetBO param);

    /**
     * @param param
     * @return
     */
    DomainPage<SacTestDriveSheetBO> queryTestDriveSheetPage(SacTestDriveSheetBO param);

    /**
     * @param sheetBoParam
     * @return
     */
    Long queryTestDriveSheetCount(SacTestDriveSheetBO sheetBoParam);

    /**
     * 根据试驾单id查询一条试乘试驾单
     *
     * @param param
     * @return
     */
    SacTestDriveSheetBO queryTestDriveSheetInfoById(SacTestDriveSheetBO param, QueryColumn... columns);

    /**
     * 根据条件查询一条试乘试驾单
     *
     * @param param
     * @return
     */
    SacTestDriveSheetBO queryTestDriveSheetInfo(SacTestDriveSheetBO param, QueryColumn... columns);

    /**
     * 更新试驾单
     *
     * @param param
     * @return
     */
    Boolean modifyTestDriveSheet(SacTestDriveSheetBO param);

    /**
     * 创建试乘试驾单
     *
     * @param param
     * @return
     */
    Boolean createTestDriveSheet(SacTestDriveSheetBO param);

    /**
     * 发送试乘试驾短信
     *
     * @param param
     * @return
     */
    Boolean sendTestDriveMsg(Map<String, Object> param);

    /**
     * 删除试乘试驾单
     *
     * @param param
     * @return
     */
    Boolean deleteTestDriveSheet(SacTestDriveSheetBO param);

    /**
     * 试驾结束时记录接口表，获取BI车机数据
     *
     * @param param
     * @return
     */
    Boolean vehicleData(Map<String, Object> param);

    /**
     * 试驾结束发送ZTMQ
     *
     * @param param
     * @return
     */
    Boolean sendZTMQ(Map<String, Object> param);
}
