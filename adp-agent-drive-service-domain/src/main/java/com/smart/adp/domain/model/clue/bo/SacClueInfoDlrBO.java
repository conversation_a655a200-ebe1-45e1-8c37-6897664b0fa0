package com.smart.adp.domain.model.clue.bo;

import com.smart.adp.domain.enums.DefeatFlagEnum;
import com.smart.adp.domain.model.clue.entity.SacClueInfoDlrEntity;
import lombok.Data;

/**
 * @Description: 线索BO
 * @Author: rik.ren
 * @Date: 2025/4/23 16:51
 **/
@Data
public class SacClueInfoDlrBO extends SacClueInfoDlrEntity {
    /**
     * 线索标签
     * 战败标签，0非战败，1全部
     *
     * @see DefeatFlagEnum
     */
    private Integer defeatFlag;

    /**
     * 库名称
     */
    private String dbName;

    public String getDbName() {
        if (DefeatFlagEnum.DEFEAT.getCode().equals(defeatFlag)) {
            dbName = "csc";
        } else {
            dbName = "adp_leads";
        }
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }
}
