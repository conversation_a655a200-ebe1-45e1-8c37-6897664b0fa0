package com.smart.adp.domain.utils.query.fillPolicy;

import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/31
 */
public class FillPolicies {

    public static <K, V> FillPolicy<K, V> noFill() {
        return new FillPolicyImpl<>(false, false, false, k -> null);
    }

    public static <K, V> FillPolicy<K, V> syncFill() {
        return new FillPolicyImpl<>(true, false, false, k -> null);
    }

    public static <K, V> FillPolicy<K, V> asyncFill() {
        return new FillPolicyImpl<>(true, true, false, k -> null);
    }

    public static <K, V> FillPolicy<K, V> syncFillWithNull(Function<K, V> nullFunc) {
        return new FillPolicyImpl<>(true, false, true, nullFunc);
    }

    public static <K, V> FillPolicy<K, V> asyncFillWithNull(Function<K, V> nullFunc) {
        return new FillPolicyImpl<>(true, true, true, nullFunc);
    }
}
