package com.smart.adp.domain.model.base.bo;

import com.smart.adp.domain.model.base.valueObject.IfsBaseCdpLeadsVO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * adp自建线索创建发送cdp（adp->cdp）
 *
 * @TableName t_ifs_base_cdp_leads
 */
@Data
public class IfsBaseCdpLeadsBO extends IfsBaseCdpLeadsVO {

    /**
     * 构建开始试驾时，推送cdp消息的对象bo
     *
     * @param custPhone
     * @param smallCarTypeCode
     * @return
     */
    public IfsBaseCdpLeadsBO buildStartDrivingCdpLeadBO(String custPhone, String smallCarTypeCode) {
        setBk(custPhone);
        setMobile(custPhone);
        setCInterestedCarModel(smallCarTypeCode);
        setRemark("客户信息修改");
        setCLastupdateSystem("ADP");
        setInsertDate(LocalDateTime.now());
        setSendDate(LocalDateTime.now());
        setSendFlag("0");
        return this;
    }
}