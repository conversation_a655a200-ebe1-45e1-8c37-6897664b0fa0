package com.smart.adp.domain.model.base.bo;

/**
 * <AUTHOR>
 * date 2025/6/5 16:20
 * @description lookupinfo
 **/

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.smart.adp.domain.model.base.valueObject.LookUpInfoVO;
import lombok.*;

import static com.smart.adp.domain.model.base.valueObject.table.LookUpInfoVOTableDef.LOOK_UP_INFO_VO;

/**
 * 值列表
 * t_prc_mds_lookup_value
 */
@Data
public class LookUpInfoBO extends LookUpInfoVO {
    /**
     * 构建查询条件
     *
     * @param wrapper
     * @return
     */
    public LookUpInfoVO buildConditions(QueryWrapper wrapper) {
        wrapper.and(LOOK_UP_INFO_VO.LOOK_UP_TYPE_CODE.eq(getLookUpTypeCode(), StringUtil::hasText))
                .and(LOOK_UP_INFO_VO.LOOK_UP_TYPE_NAME.eq(getLookUpTypeName(), StringUtil::hasText))
                .and(LOOK_UP_INFO_VO.LOOK_UP_VALUE_CODE.eq(getLookUpValueCode(), StringUtil::hasText))
                .and(LOOK_UP_INFO_VO.LOOK_UP_VALUE_NAME.eq(getLookUpValueName(), StringUtil::hasText));
        return this;
    }
}
