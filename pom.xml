<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.smart</groupId>
        <artifactId>smart-elf-parent</artifactId>
        <version>1.1.3-RELEASE</version>
    </parent>

    <groupId>com.smart.adp</groupId>
    <artifactId>adp-agent-drive-service-parent</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>
    <name>adp-agent-drive-service-parent</name>
    <description>履约交付服务</description>

    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
<!--        <maven.compiler.target>1.8</maven.compiler.target>-->
<!--        <maven.compiler.source>1.8</maven.compiler.source>-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <fastjson2.version>2.0.23</fastjson2.version>
        <guava.version>31.1-jre</guava.version>
        <validation-api.version>2.0.0.Final</validation-api.version>
        <mybatis-flex-starter.version>1.10.9</mybatis-flex-starter.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <contra-version>1.0-SNAPSHOT</contra-version>
        <xxl-job.version>2.5.0</xxl-job.version>
        <redisson.version>1.1.3-RELEASE</redisson.version>
        <okhttp.version>4.11.0</okhttp.version> <!-- 与 OBS SDK 兼容的版本 -->

    </properties>

    <modules>
        <module>adp-agent-drive-service-interfaces</module>
        <module>adp-agent-drive-service-application</module>
        <module>adp-agent-drive-service-domain</module>
        <module>adp-agent-drive-service-infrastructure</module>
        <module>adp-agent-drive-service-startup</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--Project modules-->
            <dependency>
                <groupId>com.smart.adp</groupId>
                <artifactId>adp-agent-drive-service-interfaces</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.smart.adp</groupId>
                <artifactId>adp-agent-drive-service-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.smart.adp</groupId>
                <artifactId>adp-agent-drive-service-domain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.smart.adp</groupId>
                <artifactId>adp-agent-drive-service-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--Validation API-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <!--Validation API End -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.5</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.2</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot-starter</artifactId>
                <version>${mybatis-flex-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.smart.mss</groupId>
                <artifactId>contra-springboot-starter</artifactId>
                <version>${contra-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.15.2</version> <!-- 使用最新版本 -->
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.shyiko</groupId>
                <artifactId>mysql-binlog-connector-java</artifactId>
                <version>0.21.0</version>
            </dependency>
            <dependency>
                <groupId>com.smart</groupId>
                <artifactId>smart-elf-starter-redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- 添加 OkHttp 依赖 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- 华为云 OBS SDK -->
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java</artifactId>
                <version>3.21.12</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Apache POI for DOCX processing -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.3</version>
            </dependency>

            <!-- 添加 okio 依赖 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.5.0</version> <!-- 兼容版本 -->
            </dependency>

            <!-- Docx4J PDF 主要依赖 -->
            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-export-fo</artifactId>
                <version>11.3.2</version>
            </dependency>

            <!-- JAXB 支持（Java 11 需要手动引入） -->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
                <version>11.3.2</version>
            </dependency>
            <!-- JAXP 实现支持 -->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>4.0.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.2.6.RELEASE</version>
                </plugin>
                <plugin>
                    <groupId>com.github.shalousun</groupId>
                    <artifactId>smart-doc-maven-plugin</artifactId>
                    <version>2.6.2</version>
                    <configuration>
                        <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                        <configFile>src/main/resources/smart-doc.json</configFile>
                        <!--指定项目名称-->
                        <projectName>业务中台-统一项目模板v1.0</projectName>
                        <!--smart-doc实现自动分析依赖树加载第三方依赖的源码，如果一些框架依赖库加载不到导致报错，这时请使用excludes排除掉-->
                        <excludes>
                            <!--格式为：groupId:artifactId;参考如下-->
                            <!--也可以支持正则式如：com.alibaba:.* -->
                            <exclude>com.alibaba:fastjson</exclude>
                        </excludes>
                        <!--includes配置用于配置加载外部依赖源码,配置后插件会按照配置项加载外部源代码而不是自动加载所有，因此使用时需要注意-->
                        <!--smart-doc能自动分析依赖树加载所有依赖源码，原则上会影响文档构建效率，因此你可以使用includes来让插件加载你配置的组件-->
                        <includes>
                            <!-- 使用了mybatis-plus的Page分页需要include所使用的源码包 -->
                            <include>com.baomidou:mybatis-plus-extension</include>
                            <!-- 使用了mybatis-plus的IPage分页需要include mybatis-plus-core-->
                            <include>com.baomidou:mybatis-plus-core</include>
                            <!-- 如果配置了includes的情况下， 使用了jpa的分页需要include所使用的源码包 -->
                            <include>org.springframework.data:spring-data-commons</include>
                        </includes>
                    </configuration>
                    <executions>
                        <execution>
                            <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                            <phase>compile</phase>
                            <goals>
                                <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
                                <goal>html</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
