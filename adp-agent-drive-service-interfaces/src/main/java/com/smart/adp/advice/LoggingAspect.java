package com.smart.adp.advice;

import com.smart.adp.infrastructure.config.AsyncLoggingService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @Description: 日志切面，只拦截controller的接口入参
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class LoggingAspect {

    @Autowired
    private AsyncLoggingService asyncLoggingService;

    @Pointcut("execution(* com.smart.adp.interfaces.facade.*.*(..))")
    public void applicationServiceMethods() {
    }

    @Around("applicationServiceMethods()")
    public Object logMethodParams(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().toShortString();
        Object[] args = joinPoint.getArgs();
        asyncLoggingService.logMethodParams(methodName, args);

        Object res = joinPoint.proceed();

        asyncLoggingService.info("{} 总耗时 {}", methodName, System.currentTimeMillis() - start);
        return res;
    }
}