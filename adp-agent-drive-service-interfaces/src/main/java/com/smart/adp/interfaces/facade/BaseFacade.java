package com.smart.adp.interfaces.facade;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 用户
 * @Author: rik.ren
 * @Date: 2025/3/9 17:19
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "基础", description = "基础")
@RequestMapping("/api/agent/base")
public class BaseFacade {

}
