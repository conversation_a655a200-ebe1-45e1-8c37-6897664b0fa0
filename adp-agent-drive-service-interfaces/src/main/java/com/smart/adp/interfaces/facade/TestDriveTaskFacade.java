package com.smart.adp.interfaces.facade;


import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 试驾任务接口
 * @Author: rik.ren
 * @Date: 2025/7/16 13:28
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "试驾任务", description = "试驾任务")
@RequestMapping("/api/agent/testDriveTask")
public class TestDriveTaskFacade {



//    @Operation(summary = "创建试驾任务", tags = "创建试驾任务")
//    @PostMapping(value = "/saveTestDriverTask")
//    public RespBody<Boolean> saveTestDriverTask(@RequestBody @Validated SaveTestDriveTaskDTO param) {
//        Boolean result = testDriveTaskService.saveTestDriverTask(param);
//        return RespBody.ok(result);
//    }
//
//    @Operation(summary = "店长分配试驾任务给到产品专家", tags = "店长分配试驾任务给到产品专家")
//    @PostMapping(value = "/allocationTestDriverTask")
//    public RespBody<Boolean> allocationTestDriverTask(@RequestBody @Validated AllocationTestDriveTaskDTO param) {
//        Boolean result = testDriveTaskService.allocationTestDriverTask(param);
//        return RespBody.ok(result);
//    }
//
//    @Operation(summary = "分页查询试驾任务", tags = "分页查询试驾任务")
//    @PostMapping(value = "/queryTestDriveTaskPage")
//    public RespBody<PageVO<QueryTestDriveTaskRspVO>> queryTestDriveTaskPage(@RequestBody @Validated QueryTestDriverTaskDTO param) {
//        PageVO<QueryTestDriveTaskRspVO> result = testDriveTaskService.queryTestDriveTaskPage(param);
//        return RespBody.ok(result);
//    }
//
//    @Operation(summary = "查询试驾任务满足条件的总数", tags = "查询试驾任务满足条件的总数")
//    @PostMapping(value = "/queryTestDriveTaskCount")
//    public RespBody<Long> queryTestDriveTaskCount(@RequestBody QueryTestDriverTaskDTO param) {
//        Long result = testDriveTaskService.queryTestDriveTaskCount(param);
//        return RespBody.ok(result);
//    }
}
