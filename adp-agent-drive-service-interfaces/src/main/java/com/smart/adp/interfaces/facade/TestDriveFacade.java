package com.smart.adp.interfaces.facade;

import com.smart.adp.application.dto.base.NameMatchIdCardDTO;
import com.smart.adp.application.dto.drive.*;
import com.smart.adp.application.service.drive.TestDriveSheetService;
import com.smart.adp.application.service.drive.TestDriveTaskService;
import com.smart.adp.application.vo.drive.*;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.annotation.NoAuth;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.vo.DriveTaskVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;

/**
 * @Description: 试驾单
 * @Author: rik.ren
 * @Date: 2025/5/14 10:00
 **/
@Slf4j
@RestController
@Validated
@CrossOrigin(origins = "*")
@Tag(name = "试驾单", description = "试驾单")
@RequestMapping("/api/agent/testDrive")
public class TestDriveFacade {

    @Resource
    TestDriveSheetService testDriveSheetService;

    @Resource
    TestDriveTaskService testDriveTaskService;

    @Operation(summary = "分页查询试乘试驾单", description = "分页查询试乘试驾单")
    @PostMapping(value = "/queryTestDriveSheetPage")
    public RespBody<PageVO<QueryTestDriveSheetRspVO>> queryTestDriveSheetPage(@RequestBody @Validated QueryTestDriverSheetDTO param) {
        PageVO<QueryTestDriveSheetRspVO> result = testDriveSheetService.queryTestDriveSheetList(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "查询试乘试驾单满足条件的总数", description = "查询试乘试驾单满足条件的总数")
    @PostMapping(value = "/queryTestDriveSheetCount")
    public RespBody<Long> queryTestDriveSheetCount(@RequestBody QueryTestDriverSheetDTO param) {
        Long result = testDriveSheetService.queryTestDriveSheetCount(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "获取指定试驾单信息", description = "获取指定试驾单信息")
    @PostMapping(value = "/queryTestDriveSheetInfo")
    public RespBody<QueryTestDriveSheetInfoRspVO> queryTestDriveSheetInfo(@RequestBody @Validated QueryTestDriverSheetInfoDTO param) {
        QueryTestDriveSheetInfoRspVO result = testDriveSheetService.queryTestDriveSheetInfo(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "获取线索是否是本店线索", description = "获取线索是否是本店线索")
    @PostMapping(value = "/querySameDlrCodeFlag")
    public RespBody<QueryTestDriveSheetSameDlrCodeRspVO> querySameDlrCodeFlag(@RequestBody @Validated QueryTestDriverCustSameDlrDTO param) {
        QueryTestDriveSheetSameDlrCodeRspVO result = testDriveSheetService.querySameDlrCodeFlag(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "创建试乘试驾单", description = "创建试乘试驾单")
    @PostMapping(value = "/createTestDriverSheet")
    public RespBody<CreateTestDriveRspVO> createTestDriveSheet(@RequestBody @Validated CreateTestDriverSheetDTO param) {
        CreateTestDriveRspVO result = testDriveSheetService.createTestDriveSheet(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "更新试乘试驾单", description = "更新试乘试驾单")
    @PostMapping(value = "/modifyTestDriverSheet")
    public RespBody<Boolean> modifyTestDriverSheet(@RequestBody @Validated ModifyTestDriverSheetDTO param) {
        Boolean result = testDriveSheetService.modifyTestDriverSheet(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "试驾到店签到", description = "试驾到店签到")
    @PostMapping(value = "/testDriveSign")
    public RespBody<Boolean> testDriveSign(@RequestBody @Validated SignTestDriveDTO param) {
        Boolean result = testDriveSheetService.testDriveSign(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "开始试驾", description = "开始试驾")
    @PostMapping(value = "/testDriveStart")
    public RespBody<Boolean> testDriveStart(@RequestBody @Validated StartTestDriveDTO param) {
        Boolean result = testDriveSheetService.testDriveStart(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "结束试驾", description = "结束试驾")
    @PostMapping(value = "/testDriveStop")
    public RespBody<Boolean> testDriveStop(@RequestBody @Validated StopTestDriveDTO param) {
        Boolean result = testDriveSheetService.testDriveStop(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "身份证识别ocr-支持图片地址和文件", description = "身份证识别ocr")
    @PostMapping(value = "/IDCardOCRFormData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public RespBody<IDCardOCRVO> testDriveIDOCRFormData(OCRInfoDTO param) {
        IDCardOCRVO result = testDriveSheetService.IDCardOCR(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "驾驶证识别ocr-支持图片地址和文件", description = "驾驶证识别ocr")
    @PostMapping(value = "/driverLicenseOCRFormData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public RespBody<DriverLicenseOCRVO> driverLicenseOCRFormData(OCRInfoDTO param) {
        DriverLicenseOCRVO result = testDriveSheetService.driverLicenseOCR(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "身份证识别ocr-仅支持图片地址", description = "身份证识别ocr")
    @PostMapping("/IDCardOCR")
    public RespBody<IDCardOCRVO> IDInfoOCR(@RequestBody OCRInfoDTO param) {
        IDCardOCRVO result = testDriveSheetService.IDCardOCR(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "驾驶证识别ocr-仅支持图片地址", description = "驾驶证识别ocr")
    @PostMapping("/driverLicenseOCR")
    public RespBody<DriverLicenseOCRVO> driverLicenseOCR(@RequestBody OCRInfoDTO param) {
        DriverLicenseOCRVO result = testDriveSheetService.driverLicenseOCR(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "姓名身份证号匹配关系校验", description = "姓名身份证号匹配关系校验")
    @PostMapping("/nameMatchIdCard")
    public RespBody<Boolean> nameMatchIdCard(@RequestBody NameMatchIdCardDTO param) {
        Boolean result = testDriveSheetService.nameMatchIdCard(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "生成用户协议文件", description = "生成用户协议文件")
    @PostMapping("/generateAgreementFile")
    public RespBody<GenerateAgreementRspVO> generateAgreementFile(@RequestBody GenerateAgreementFileDTO param) {
        GenerateAgreementRspVO result = testDriveSheetService.generateAgreementFile(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "word转pdf", description = "word转pdf，该接口免鉴权，可以用作生成用户协议文件-补偿接口，当word生成失败，或者pdf生成失败调用该接口进行补偿")
    @PostMapping("/conventDocToPdf")
    @NoAuth
    public RespBody<String> conventDocToPdf(@RequestBody ConventDocToPdfDTO param) {
        String result = testDriveSheetService.conventDocToPdf(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "试驾单数据重推外部服务", description = "支持重推外部服务")
    @PostMapping("/testSheetDataRePush")
    public RespBody<Object> testSheetDataRePush(@RequestBody TestSheetDataRePushDTO param) {
        Object result = testDriveSheetService.testSheetDataRePush(param);
        return RespBody.ok(result);
    }

    @Operation(summary = "试驾任务列表", description = "试驾任务列表", tags = "试驾任务")
    @PostMapping(value = "/taskPage")
    public RespBody<PageVO<DriveTaskVO>> taskPage(@RequestBody @Validated DriveTaskDTO dto) {
        return RespBody.ok(testDriveSheetService.taskPage(dto));
    }

    @Operation(summary = "试驾任务刷新", description = "试驾任务刷新", tags = "试驾任务")
    @PostMapping(value = "/taskRefresh/{id}")
    public RespBody<DriveTaskVO> taskRefresh(@PathVariable("id")
                                                     @NotEmpty(message = "无效 ID")
                                                     @Parameter(description = "试驾任务 ID") String id,
                                                     @RequestBody @Validated DriveTaskDTO dto) {
        return RespBody.ok(testDriveSheetService.taskRefresh(id, dto));
    }

    @Operation(summary = "试驾任务代办", description = "试驾任务代办", tags = "试驾任务")
    @GetMapping(value = "/taskTodo")
    public RespBody<Boolean> taskTodo() {
        return RespBody.ok(testDriveSheetService.taskTodo());
    }

    @Operation(summary = "取消试驾任务", description = "取消试驾任务", tags = "试驾任务")
    @PostMapping(value = "/task/cancelTestDriverTask")
    public RespBody<Boolean> cancelTestDriverTask(@RequestBody @Validated CancelTestDriveTaskDTO param) {
        Boolean result = testDriveTaskService.cancelTestDriverTask(param);
        return RespBody.ok(result);
    }

}
