<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.smart.adp</groupId>
        <artifactId>adp-agent-drive-service-parent</artifactId>
        <version>1.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>adp-agent-drive-service-interfaces</artifactId>
    <packaging>jar</packaging>
    <name>adp-agent-drive-service-interfaces</name>
    <properties>
        <maven.install.skip>true</maven.install.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.smart.adp</groupId>
            <artifactId>adp-agent-drive-service-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.smart</groupId>
            <artifactId>smart-elf-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.smart</groupId>
            <artifactId>smart-elf-starter-feignclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
