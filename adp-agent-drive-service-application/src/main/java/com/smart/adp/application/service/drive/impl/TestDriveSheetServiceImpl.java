package com.smart.adp.application.service.drive.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.application.dto.base.NameMatchIdCardDTO;
import com.smart.adp.application.dto.drive.*;
import com.smart.adp.application.event.TestDriveCreatedEvent;
import com.smart.adp.application.event.TestDriveStartedEvent;
import com.smart.adp.application.event.TestDriveStopedEvent;
import com.smart.adp.application.rePushData.RePushTestDataStrategyFactory;
import com.smart.adp.application.service.drive.TestDriveSheetService;
import com.smart.adp.application.utils.ParallelExecutorUtils;
import com.smart.adp.application.vo.drive.*;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.annotation.SmartDistributedLock;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.resp.RespResult;
import com.smart.adp.domain.enums.TestDriveTaskStatusEnum;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.clue.ClueInfoAggregate;
import com.smart.adp.domain.model.clue.bo.ClueEventFlowBO;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.clue.bo.SacOneCustRemarkBO;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.gateway.SacTestDriveTaskGateway;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import com.smart.adp.domain.qry.DriveTaskQry;
import com.smart.adp.domain.utils.UserUtil;
import com.smart.adp.domain.utils.query.MultiStorageQueryCoordinator;
import com.smart.adp.domain.utils.query.fillPolicy.FillPolicies;
import com.smart.adp.domain.vo.DriveTaskVO;
import com.smart.adp.infrastructure.adapter.AdaptMid;
import com.smart.adp.infrastructure.feign.AgentClueFeign;
import com.smart.adp.infrastructure.feign.MidFeign;
import com.smart.adp.infrastructure.feign.request.ClueListReq;
import com.smart.adp.infrastructure.feign.response.ClueDlrRsp;
import com.smart.adp.infrastructure.feign.response.DriverLicenseOCRRsp;
import com.smart.adp.infrastructure.feign.response.IDCardOCRRsp;
import com.smart.adp.infrastructure.utils.AgreementFileUtil;
import com.smart.adp.infrastructure.utils.DocxToPdfConverter;
import com.smart.adp.infrastructure.utils.ObsUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 试驾单service实现，应用层的service要编排能力，只能调用各个域的聚合根暴漏出来的能力，不能直接调用领域层的能力
 * @Author: rik.ren
 * @Date: 2025/5/15 15:18
 **/
@Slf4j
@Service
public class TestDriveSheetServiceImpl implements TestDriveSheetService {
    @Autowired
    private TestDriveAggregate testDriveAggregate;
    @Autowired
    private BaseInfoAggregate baseInfoAggregate;
    @Autowired
    private ClueInfoAggregate clueInfoAggregate;
    @Autowired
    private AdaptMid adaptMid;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private MidFeign midFeign;
    @Autowired
    private AgreementFileUtil agreementFileUtil;
    @Autowired
    private ObsUtils obsUtils;
    @Value("${businessConfig.operateSwitch.matchNameAndID:true}")
    private Boolean matchNameAndIDSwitch;
    @Autowired
    private RePushTestDataStrategyFactory strategyFactory;
    @Autowired
    private SacTestDriveTaskGateway driveTaskGateway;
    @Autowired
    private AgentClueFeign agentClueFeign;
    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String DRIVE_TASK_LOG_PREFIX = "DRIVE_TASK";
    private static final String CLUE_PHONE_CACHE_KEY_FORMAT = "AGENT_DRIVE:CLUE_PHONE_CACHE:%s";
    private static final String CLUE_PHONE_CACHE_EX = "10";
    private final MultiStorageQueryCoordinator<String, ClueDlrRsp> clueQueryCoordinator =
        new MultiStorageQueryCoordinator<>(
            this::clueCacheQry, this::clueFeignQry, this::clueCacheFill,
            FillPolicies.asyncFillWithNull(phone -> ClueDlrRsp.builder().phone(phone).build())
        );

    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    @SneakyThrows
    @Override
    public PageVO<QueryTestDriveSheetRspVO> queryTestDriveSheetList(QueryTestDriverSheetDTO param) {

        // 1. 查询试驾单信息
        TestDriveAggregate.QueryResult queryTestDriveResult = testDriveAggregate.queryTestDriveSheetList(param.buildSacTestDriveSheetBO());
        if (ObjectUtil.isEmpty(queryTestDriveResult) || ObjectUtil.isEmpty(queryTestDriveResult.getSheets())
                || ObjectUtil.isEmpty(queryTestDriveResult.getSheets().getRecords())
                || queryTestDriveResult.getSheets().getTotalCount() == 0L) {
            return PageVO.<QueryTestDriveSheetRspVO>builder().records(null).pageSize(param.getPageSize()).pageNumber(param.getPageIndex()).totalRow(0L).build();
        }
        List<String> listCustId =
                queryTestDriveResult.getSheets().getRecords().stream().map(SacTestDriveSheetBO::getCustomerId).collect(Collectors.toList());

        // 1.1 将试驾单查询结果当做参数传给多线程查询结果后的组装函数
        Object[] paramObjects = new Object[]{queryTestDriveResult};

        // 2. 多线程查询线索事件和线索扩展信息
        PageVO<QueryTestDriveSheetRspVO> resultVO = ParallelExecutorUtils.executeFunction(this::conventTestDriveDataRsp, paramObjects,
                () -> Optional.ofNullable(clueInfoAggregate.queryUserEventFlow(listCustId)).orElse(Collections.emptyList()),
                () -> Optional.ofNullable(clueInfoAggregate.queryClueRemark(listCustId)).orElse(Collections.emptyList()));
        return resultVO;
    }

    /**
     * 查询试驾单多线程查询完毕后执行的方法
     *
     * @param results
     * @param param
     * @return
     */
    private PageVO<QueryTestDriveSheetRspVO> conventTestDriveDataRsp(List<Object> results, Object[] param) {
        // 1. 查询线索信息
        @SuppressWarnings("unchecked") List<ClueEventFlowBO> listClueEventFlowBO = (List<ClueEventFlowBO>) results.get(0);
        // 2. 查询线索扩展信息
        @SuppressWarnings("unchecked") List<SacOneCustRemarkBO> listSacOneCustRemark = (List<SacOneCustRemarkBO>) results.get(1);
        TestDriveAggregate.QueryResult queryResult = (TestDriveAggregate.QueryResult) param[0];
        // 3. 封装返回信息
        return QueryTestDriveSheetRspVO.convent(queryResult.getSheets(), queryResult.getPreparations(), listClueEventFlowBO,
                listSacOneCustRemark);
    }

    /**
     * 根据条件查询试驾单数量
     *
     * @param param
     * @return
     */
    @Override
    public Long queryTestDriveSheetCount(QueryTestDriverSheetDTO param) {
        return testDriveAggregate.queryTestDriveSheetCount(param.buildSacTestDriveSheetBO());
    }

    /**
     * 创建试乘试驾单
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'createTestDrive_lock_' + #param.customerPhone", message = "试驾单正在创建处理中，请勿重复操作", block = false, waitTime =
            10)
    public CreateTestDriveRspVO createTestDriveSheet(CreateTestDriverSheetDTO param) {
        // 1. 先获取基础领域配置
        String configTestDriveSwitchVal = baseInfoAggregate.ValueCode("APPOINTMENT_SWITCH", "1");
        String configDriveNumLimitVal = baseInfoAggregate.ValueCode("DRIVE_NUM_SWITCH", "1");

        // 2. 再通过试乘试驾聚合根，调用试驾领域的创建试驾单能力
        SacTestDriveSheetBO sacTestDriveSheetBO = param.buildSacTestDriveSheetBO(configTestDriveSwitchVal, configDriveNumLimitVal);
        TestDriveAggregate.CreateTestDriveSheetResult createResult;
        try {
            createResult = testDriveAggregate.createTestDriveSheet(sacTestDriveSheetBO);
            if (!createResult.getResult()) {
                return CreateTestDriveRspVO.convent(createResult);
            }
        } catch (Exception e) {
            log.error("创建试驾单失败 {}", JSONObject.toJSONString(param), e);
            throw new BusinessException(RespCode.FAIL.getCode(), e.getMessage());
        }

        // 3. 创建试乘试驾单后，后续操作
        createTestDriveFollowUp(param, sacTestDriveSheetBO);
        return CreateTestDriveRspVO.convent(createResult);
    }

    /**
     * 创建试乘试驾单后，后续操作
     *
     * @param testDriveSheetDto
     * @param sacTestDriveSheetBO
     */
    private void createTestDriveFollowUp(CreateTestDriverSheetDTO testDriveSheetDto, SacTestDriveSheetBO sacTestDriveSheetBO) {
        // 创建试乘试驾单后，后续操作事件发布
        eventPublisher.publishEvent(new TestDriveCreatedEvent(this,
                sacTestDriveSheetBO,
                testDriveSheetDto
        ));
    }

    /**
     * 更新试乘试驾单
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'modifyTestDrive_lock_' + #param.testDriveSheetId", message = "更新试驾单正在处理中，请勿重复操作", block = false)
    @Override
    public Boolean modifyTestDriverSheet(ModifyTestDriverSheetDTO param) {
        // 1. 先获取基础领域配置
        String configTestDriveSwitchVal = baseInfoAggregate.ValueCode("APPOINTMENT_SWITCH", "1");
        String configDriveNumLimitVal = baseInfoAggregate.ValueCode("DRIVE_NUM_SWITCH", "1");

        // 2. 调用试驾域的聚合根
        Boolean modifyResult = Boolean.FALSE;
        try {
            modifyResult = testDriveAggregate.modifyTestDriverSheet(param.buildModifyBO(configTestDriveSwitchVal, configDriveNumLimitVal));
        } catch (Exception e) {
            log.error("更新试驾单失败 ", e);
            throw new BusinessException(RespCode.FAIL.getCode(), e.getMessage());
        }
        return modifyResult;
    }

    /**
     * 根据条件查询某一条试乘试驾单
     *
     * @param param
     * @return
     */
    @Override
    public QueryTestDriveSheetInfoRspVO queryTestDriveSheetInfo(QueryTestDriverSheetInfoDTO param) {
        SacTestDriveSheetBO sheetBO = param.buildSacTestDriveSheetBO();
        // 查询试驾单信息
        TestDriveAggregate.QueryTestDriveInfoResult queryTestDriveInfoResult = testDriveAggregate.queryTestDriveSheetInfo(sheetBO);
        return new QueryTestDriveSheetInfoRspVO().buildQueryTestDriveSheetInfoRspVO(queryTestDriveInfoResult);
    }

    /**
     * 根据条件查询是否是同一家门店
     *
     * @param param
     * @return
     */
    @Override
    public QueryTestDriveSheetSameDlrCodeRspVO querySameDlrCodeFlag(QueryTestDriverCustSameDlrDTO param) {

        SacTestDriveSheetBO sheetBO = param.buildSacTestDriveSheetBO();
        QueryTestDriveSheetSameDlrCodeRspVO vo = new QueryTestDriveSheetSameDlrCodeRspVO();
        // 1. 查询对应线索信息
        SacClueInfoDlrBO queryClueInfoDlrResult = clueInfoAggregate.queryClueInfoDlr(sheetBO.getCustomerPhone());
        if (ObjectUtil.isEmpty(queryClueInfoDlrResult) || StringUtils.isBlank(queryClueInfoDlrResult.getCustId())) {
            log.info("根据条件查询是否是同一家门店线索不存在");
            return vo;
        }
        // 2. 查询试驾单信息
        sheetBO.setNeedAppointmentSheet(Boolean.FALSE);
        SacTestDriveSheetBO testDriveSheetBO = testDriveAggregate.queryTestDriveSheetDlr(sheetBO);
        vo.setTestDriveSheetId(param.getTestDriveSheetId());
        if (testDriveSheetBO.getDlrCode().equals(queryClueInfoDlrResult.getDlrCode())) {
            vo.setIsThisDlr(Boolean.TRUE);
            //线索创建时候，线索创建如果存在延时，端上立马调用获取线索，有可能不存在
            vo.setCustId(queryClueInfoDlrResult.getCustId());
        }
        return vo;
    }

    /**
     * 试驾签到
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'signTest_lock_' + #param.testDriveSheetId", message = "试驾签到正在处理中，请勿重复操作", block = false)
    @Override
    public Boolean testDriveSign(SignTestDriveDTO param) {
        return testDriveAggregate.testDriveSign(param.buildSignBO());
    }

    /**
     * 开始试驾
     * 1→2→3→7→8→9→(异步4→5→6)
     * 1、根据试驾单号获取试驾单
     * 2、验证是否已经点击了签到
     * 3、判断这辆车是否正在试驾中，如果有，就返回"当前车辆正在试驾中，试驾产品专家%s，客户为%s",
     * 7、更新试乘试驾单
     * 8、更新整备表的车辆信息为试驾中CarStatusCode为1
     * 9、更新预约单isTestDrive为1
     * 异步：
     * 4、查询线索，获取这个线索的意向车型，判断线索的意向车型中，是否有当前的试驾车型，如果没有，就更新线索的意向车型，如果线索没有意向车型，就更新当前试驾车型为意向车型
     * 5、更新t_sac_review的意向车型
     * 6、通知CDP
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'startTest_lock_' + #param.testDriveSheetId", message = "开始试驾正在处理中，请勿重复操作", block = false)
    @Override
    public Boolean testDriveStart(StartTestDriveDTO param) {

        // 1. 在试驾聚合根里面进行1/2/3/7/8/9的操作
        Boolean testDriveStartResult = testDriveAggregate.testDriveStart(param.buildStartTestDriveBO());
        if (!testDriveStartResult) {
            return Boolean.FALSE;
        }
        // 2. 异步更新t_sac_review的意向车型和推送CDP
        testDriveStartFollowUp(param);
        return Boolean.TRUE;
    }

    /**
     * 开始试驾后续操作
     *
     * @param param
     */
    private void testDriveStartFollowUp(StartTestDriveDTO param) {
        // 开始试驾后续操作，后续操作事件发布
        eventPublisher.publishEvent(new TestDriveStartedEvent(this, param));
    }

    /**
     * 结束试驾
     * 1. 校验试驾单是否存在、状态
     * 2. 校验里程信息，构建试驾总里程字段值
     * 3. 构建发送TDA的对象数据
     * 4. 构建发送ZTMQ的对象数据
     * 5. 调用xapi试驾结束时记录接口表，获取BI车机数据
     * 6. 更新试乘试驾单的信息，包括：testStatus、endTime、testEndRoadHaul、testRoadHaul、testDriveEndRemark、lastUpdatedDate、modifier、modifyName
     * 、updateControlId
     * 7. 更新试驾车信息，包括：testcarFrequency、carStatusCode、carStatusName
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'stopTest_lock_' + #param.testDriveSheetId", message = "结束试驾正在处理中，请勿重复操作", block = false)
    @Override
    public Boolean testDriveStop(StopTestDriveDTO param) {
        // 1. 在试驾聚合根里面进行1/2/6的操作
        Boolean testDriveStopResult = testDriveAggregate.testDriveStop(param.buildStopTestDriveBO());
        if (!testDriveStopResult) {
            return Boolean.FALSE;
        }

        // 2. 进行3/4/5操作
        testDriveStopFollowUp(param);
        return Boolean.TRUE;
    }

    /**
     * 结束试驾后续操作
     *
     * @param param
     */
    private void testDriveStopFollowUp(StopTestDriveDTO param) {
        // 结束试驾后续操作，后续操作事件发布
        eventPublisher.publishEvent(new TestDriveStopedEvent(this, param));
    }

    /**
     * 身份证OCR识别
     *
     * @param param
     * @return
     */
    @Override
    public IDCardOCRVO IDCardOCR(OCRInfoDTO param) {
        RespResult<List<IDCardOCRRsp>> idCardOCRResp = midFeign.idCardOCR(adaptMid.midOCR(param.getImageUrl(), param.getImageFile()));
        return IDCardOCRVO.idConvent(idCardOCRResp);
    }

    /**
     * 驾驶证OCR识别
     *
     * @param param
     * @return
     */
    @Override
    public DriverLicenseOCRVO driverLicenseOCR(OCRInfoDTO param) {
        RespResult<List<DriverLicenseOCRRsp>> idCardOCRResp = midFeign.drivingLicenseOCR(adaptMid.midOCR(param.getImageUrl(),
                param.getImageFile()));
        return DriverLicenseOCRVO.driveLicenseConvent(idCardOCRResp);
    }

    /**
     * 姓名身份证号匹配关系校验
     *
     * @param param
     * @return
     */
    @Override
    public Boolean nameMatchIdCard(NameMatchIdCardDTO param) {
        if (matchNameAndIDSwitch) {
            RespResult<Boolean> idCardInfoResult = midFeign.idCardInfo(adaptMid.midIdCardInfo(param.getName(), param.getIdCardNo()));
            if (ObjectUtil.isNotEmpty(idCardInfoResult.getResult()) && idCardInfoResult.getSuccess()) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 生成用户协议文件
     *
     * @param param
     * @return
     */
    @SmartDistributedLock(key = "'generateAgreementFile_lock_' + #param.testDriveSheetId", message = "试驾协议正在生成中，请勿重复操作", block = false)
    @Override
    public GenerateAgreementRspVO generateAgreementFile(GenerateAgreementFileDTO param) {
        String[] result = null;
        try {
            AgreementFileUtil.GenerateData generateData = param.buildGenerateData();
            result = agreementFileUtil.generateAgreementEntrance(generateData);
        } catch (Exception e) {
            log.error("生成用户协议文件异常", e);
            throw new BusinessException(RespCode.FAIL.getCode(), "请重试生成用户协议");
        }
        return GenerateAgreementRspVO.builder().wordFileUrl(result[0]).pdfFileUrl(result[1]).build();
    }

    /**
     * word转pdf
     * 参数这样传
     * curl -L -X POST 'http://127.0.0.1:8087/api/agent/testDrive/conventDocToPdf' \
     * -H 'Authorization: 0acbe45821e741e588058ae56a4d516a' \
     * -H 'Content-Type: application/json' --data '{
     * "wordFileUrl": "https://obs-adp-uat.obs.cn-east-3.myhuaweicloud
     * .com/testDriveAgreementFile-contracts/2025-07-02/testDriveAgreement_%E5%BC%A0%E4%B8%89_46972b93318f4a6f9dfacae179d70fec.docx",
     * "outputPath": "testDriveAgreementFile-contracts/2025-07-02",
     * "outputFileName": "testDriveAgreement_任怡融_12345678.pdf"
     * }'
     *
     * @param param
     * @return
     */
    @Override
    @SmartDistributedLock(key = "'conventPDF_lock_' + #param.serialNumber", message = "前方有PDF正在生成处理中，请稍后", block = false, waitTime = 10)
    public String conventDocToPdf(ConventDocToPdfDTO param) {
        String obsPDFUrl = null;
        File docxFile = null, pdfFile = null;
        try {
            // 1. 下载word文件
            docxFile = obsUtils.downloadFile(param.getWordFileUrl());
            if (ObjectUtil.isEmpty(docxFile)) {
                throw new BusinessException(RespCode.FAIL.getCode(), "下载word失败请重试");
            }
            // 2. 生成PDF文件
            pdfFile = DocxToPdfConverter.convertDocxToPdf(docxFile);
            if (ObjectUtil.isEmpty(pdfFile)) {
                throw new BusinessException(RespCode.FAIL.getCode(), "生成PDF失败请重试");
            }
            // 3. 上传OBS
            String pdfPath = param.getOutputPath() + "/" + param.getOutputFileName();
            obsPDFUrl = obsUtils.uploadToObs(pdfFile, pdfPath);
        } catch (Exception e) {
            log.error("生成PDF文件异常", e);
            throw new BusinessException(RespCode.FAIL.getCode(), "生成PDF失败请重试");
        } finally {
            if (docxFile != null) {
                docxFile.delete();
            }
            if (pdfFile != null) {
                pdfFile.delete();
            }
        }
        return obsPDFUrl;
    }

    /**
     * 试驾单数据重推外部服务
     *
     * @param param
     * @return
     */
    @Override
    public Object testSheetDataRePush(TestSheetDataRePushDTO param) {
        // 1. 查询试驾单信息
        SacTestDriveSheetBO querySheetResult = testDriveAggregate.queryTestSheetDataForRePush(param.buildRePushTestDriveBO());
        // 2. 通过策略模式选择重推类型
        RePushTestDataStrategy strategy = strategyFactory.getStrategy(param.getRePushType());
        // 3. 执行重推
        Object rePushResult = strategy.rePush(querySheetResult);
        return rePushResult;
    }

    @Override
    public PageVO<DriveTaskVO> taskPage(DriveTaskDTO dto) {
        log.info("{} page dto {}", DRIVE_TASK_LOG_PREFIX, JSONObject.toJSONString(dto));
        UserUtil.checkDlr();

        var qry = dto.buildQry();
        qry.fillUserInfo();

        var page = driveTaskGateway.page(qry);

        var records = page.getRecords();
        if (!records.isEmpty()) {
            driveTaskFillClueInfo(records);
        }

        return PageVO.of(page);
    }

    /**
     * 试驾任务填充线索信息 异常打印日志
     *
     * @param records tasks
     */
    private void driveTaskFillClueInfo(List<DriveTaskVO> records) {
        try {
            // clue feign & join
            List<String> phones = records.stream()
                                         .map(DriveTaskVO::getPhone)
                                         .collect(Collectors.toList());
            var clueMap = getClueMap(phones);

            for (DriveTaskVO vo : records) {
                ClueDlrRsp dlrRsp = Optional.of(vo)
                                            .map(DriveTaskVO::getPhone)
                                            .map(clueMap::get)
                                            .orElse(null);
                if (Objects.isNull(dlrRsp)) {
                    continue;
                }

                vo.setClueCustId(dlrRsp.getCustId());
                vo.setClueStatusCode(dlrRsp.getStatusCode());
                vo.setClueDlrCode(dlrRsp.getDlrCode());
                vo.setCluePersonId(dlrRsp.getReviewPersonId());
                vo.setCluePersonName(dlrRsp.getReviewPersonName());
            }
        } catch (Exception e) {
            log.error("{} 填充线索信息异常", DRIVE_TASK_LOG_PREFIX, e);
        }
    }

    /**
     * 获取线索数据
     *
     * @param phones 手机号列表
     * @return map phone -> clue
     */
    private Map<String, ClueDlrRsp> getClueMap(List<String> phones) {
        return clueQueryCoordinator.multiQueryAndFillRobustly(phones);
    }

    /**
     * 线索 feign 查询
     *
     * @param phones -
     * @return clueMap
     * @throws BusinessException 查询失败
     * @throws IllegalArgumentException 查询结果 null
     */
    private Map<String, ClueDlrRsp> clueFeignQry(Collection<String> phones) {
        var req = ClueListReq.builder()
                             .phones(new ArrayList<>(phones))
                             .build();

        var res = agentClueFeign.clueList(req);
        if (res.success()) {
            var clueList = res.getBody();
            Assert.notNull(clueList, "批量查询线索结果为 null");

            return clueList.stream()
                           .collect(Collectors.toMap(ClueDlrRsp::getPhone, Function.identity(),
                               (v1, v2) -> v2));

        } else {
            throw new BusinessException(RespCode.INTERNAL_SERVER_ERROR, "批量查询线索失败 " + JSONObject.toJSONString(res));
        }
    }

    /**
     * 线索缓存查询
     *
     * @param phones -
     * @return clueMap
     * @throws IllegalArgumentException when redis used in pipeline / transaction.
     */
    private Map<String, ClueDlrRsp> clueCacheQry(Collection<String> phones) {
        Set<String> keySet = phones.stream()
                                   .map(this::getCluePhoneCacheKey)
                                   .collect(Collectors.toSet());
        List<String> redisRes = redisTemplate.opsForValue()
                                             .multiGet(keySet);

        Assert.notNull(redisRes, "multi get res is null.");
        return redisRes.stream()
                       .filter(Objects::nonNull)
                       .map(s -> JSONObject.parseObject(s, ClueDlrRsp.class))
                       .collect(Collectors.toMap(ClueDlrRsp::getPhone, Function.identity(),
                           (v1, v2) -> v2));
    }

    /**
     * 线索缓存填充 - lua 实现原子及单次网络
     *
     * @param clueDlrRspMap map
     */
    private void clueCacheFill(Map<String, ClueDlrRsp> clueDlrRspMap) {
        if (Objects.isNull(clueDlrRspMap) || clueDlrRspMap.isEmpty()) {
            return;
        }

        try {
            int size = clueDlrRspMap.size();
            String luaScript =
                "for i=1,#KEYS do " +
                    "  redis.call('set', KEYS[i], ARGV[i], 'EX', ARGV[#KEYS+1]) " +
                    "end " +
                    "return 1";

            List<String> keys = new ArrayList<>(size);
            List<String> args = new ArrayList<>(size);
            clueDlrRspMap.forEach((phone, clue) -> {
                keys.add(getCluePhoneCacheKey(phone));
                args.add(JSONObject.toJSONString(clue));
            });
            args.add(CLUE_PHONE_CACHE_EX);

            redisTemplate.execute(
                RedisScript.of(luaScript, Integer.class),
                keys,
                args.toArray()
            );
        } catch (Exception e) {
            log.error("clueQueryCoordinator clueCacheFill exception.", e);
        }
    }

    /**
     * 获取线索手机号缓存 key
     *
     * @param phone 手机号
     * @return cache key
     */
    private String getCluePhoneCacheKey(String phone) {
        return String.format(CLUE_PHONE_CACHE_KEY_FORMAT, phone);
    }

    @Override
    public DriveTaskVO taskRefresh(String id, DriveTaskDTO dto) {
        log.info("{} refresh dto {}", DRIVE_TASK_LOG_PREFIX, JSONObject.toJSONString(dto));
        UserUtil.checkDlr();

        var qry = dto.buildQry();
        qry.fillUserInfo();
        qry.setId(id);

        var list = driveTaskGateway.list(qry);
        if (list.isEmpty()) {
            return null;
        } else {
            var vo = list.get(0);
            driveTaskFillClueInfo(List.of(vo));
            return vo;
        }
    }

    @Override
    public Boolean taskTodo() {
        UserUtil.checkDlr();

        var qry = new DriveTaskQry();
        qry.fillUserInfo();
        qry.setStatus(TestDriveTaskStatusEnum.UNFINISHED);

        return driveTaskGateway.existByQry(qry);
    }
}
