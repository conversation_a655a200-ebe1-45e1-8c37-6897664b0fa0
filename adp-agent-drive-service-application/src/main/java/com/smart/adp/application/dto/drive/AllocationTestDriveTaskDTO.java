package com.smart.adp.application.dto.drive;

import com.mybatisflex.annotation.Column;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @Description: 店长分配试驾任务给到产品专家DTO
 * @Author: rik.ren
 * @Date: 2025/5/27 19:40
 **/
@Data
@Builder
public class AllocationTestDriveTaskDTO {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 专家id
     */
    @Schema(description = "专家id")
    @NotBlank(message = "专家id不能为空")
    private String salesConsultantName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    @NotBlank(message = "客户姓名不能为空")
    private String custName;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题")
    @NotBlank(message = "任务标题不能为空")
    private String taskTitle;
    /**
     * 任务人的门店code
     */
    @Schema(description = "任务人的门店code")
    @NotBlank(message = "任务人门店code不能为空")
    private String taskPersonDlrCode;

    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    @NotBlank(message = "门店名称不能为空")
    private String taskPersonDlrName;

    /**
     * 车型名称
     */
    @Schema(description = "车型名称")
    private String smallCarTypeName;

    /**
     * 任务人id
     */
    @Schema(description = "任务人id")
    private String taskPersonId;

    /**
     * 并发控制锁
     */
    @Schema(description = "并发控制锁")
    @NotBlank(message = "并发控制锁不能为空")
    private String updateControlId;

    /**
     * 时间
     */
    @Schema(description = "时间")
    private LocalDateTime bussTime;
}
