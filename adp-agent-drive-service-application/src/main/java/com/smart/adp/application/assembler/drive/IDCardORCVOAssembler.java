package com.smart.adp.application.assembler.drive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.drive.IDCardOCRVO;
import com.smart.adp.infrastructure.feign.response.IDCardOCRRsp;

/**
 * @Description: 身份证OCR识别结果对象装配
 * @Author: rik.ren
 * @Date: 2025/5/18 14:30
 **/
public class IDCardORCVOAssembler implements Assembler<IDCardOCRRsp, IDCardOCRVO> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public IDCardOCRVO assemble(IDCardOCRRsp source, Class<IDCardOCRVO> target) {
        if (ObjectUtil.isEmpty(source)) {
            return null;
        }
        IDCardOCRVO iDCardOCRVO = new IDCardOCRVO();
        iDCardOCRVO.setName(source.getName());
        iDCardOCRVO.setValidPeriod(source.getValidPeriod());
        iDCardOCRVO.setBirthDate(source.getBirthDate());
        iDCardOCRVO.setIdNumber(source.getIdNumber());
        iDCardOCRVO.setSex(source.getSex());
        iDCardOCRVO.setEthnicity(source.getEthnicity());
        iDCardOCRVO.setAddress(source.getAddress());
        return iDCardOCRVO;
    }
}
