package com.smart.adp.application.vo.drive;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.drive.IDCardORCVOAssembler;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespBody;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.resp.RespResult;
import com.smart.adp.domain.enums.AliCouldOcrErrorEnum;
import com.smart.adp.infrastructure.feign.response.IDCardOCRRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Description: 身份证识别结果
 * @Author: rik.ren
 * @Date: 2025/6/19 17:50
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class IDCardOCRVO {
    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;
    /**
     * 有效期限
     */
    @Schema(description = "有效期限")
    private String validPeriod;
    /**
     * 出生日期（格式：YYYY-MM-DD）
     */
    @Schema(description = "出生日期")
    private String birthDate;
    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    private String idNumber;
    /**
     * 性别
     */
    @Schema(description = "性别")
    private String sex;
    /**
     * 民族
     */
    @Schema(description = "民族")
    private String ethnicity;
    /**
     * 住址
     */
    @Schema(description = "住址")
    private String address;

    public static IDCardOCRVO idConvent(RespResult<List<IDCardOCRRsp>> source) {
        log.info("调用中台OCR识别身份证结果：{}", JSONObject.toJSONString(source));
        if (ObjectUtil.isEmpty(source) || CollectionUtil.isEmpty(source.getResult())) {
            return null;
        }
        if (!RespCode.OK.getCode().equals(source.getCode())) {
            log.error("调用中台OCR识别身份证错误，错误信息：{}", JSONObject.toJSONString(source));
            throw new BusinessException(RespCode.FAIL.getCode(), "请重新上传识别图片");
        }

        if (StrUtil.isNotBlank(source.getResult().get(0).getCode())) {
            if (AliCouldOcrErrorEnum.UNMATCHED_IMAGE_TYPE.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "请上传正确身份证！");
            } else if (AliCouldOcrErrorEnum.ILLEGAL_IMAGE_URL.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "请上传正确的身份证图片!");
            } else if (AliCouldOcrErrorEnum.EXCEEDED_IMAGE_CONTENT.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "证件图片最大支持10M!");
            } else if (AliCouldOcrErrorEnum.ILLEGAL_IMAGE_CONTENT.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "该图片无法识别，请手动填写!");
            } else if (AliCouldOcrErrorEnum.UNSUPPORTED_IMAGE_FORMAT.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "该图片无法识别，请手动填写!");
            }
        }

        return AssemblerFactory.getInstance().convert
                (IDCardORCVOAssembler.class, source.getResult().get(0), IDCardOCRVO.class);
    }

}
