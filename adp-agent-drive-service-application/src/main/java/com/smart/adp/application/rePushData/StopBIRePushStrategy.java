package com.smart.adp.application.rePushData;

import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 结束试驾后重推BI获取车机数据
 * @Author: rik.ren
 * @Date: 2025/7/9 18:12
 **/
@Slf4j
@Component
public class StopBIRePushStrategy implements RePushTestDataStrategy {

    @Autowired
    private TestDriveAggregate testDriveAggregate;

    /**
     * 重推
     *
     * @param param
     * @return
     */
    @Override
    public Boolean rePush(Object param) {
        log.info("开始重推试驾结束的BI车机数据");
        SacTestDriveSheetBO sheetBO = (SacTestDriveSheetBO) param;
        // 1. 试驾结束时记录接口表，获取BI车机数据
        Boolean vehicleDataResult = testDriveAggregate.insertIfVehicleData(sheetBO);
        return vehicleDataResult;
    }
}
