package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 查询试乘试驾单入参
 * @Author: rik.ren
 * @Date: 2025/5/15 16:03
 **/
@Data
public class QueryTestDriverCustSameDlrDTO {

    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id")
    @NotBlank(message = "试驾单id不能为空")
    private String testDriveSheetId;

    /**
     * 线索手机号
     */
    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String custPhone;

    /**
     * 构建判断是否是本店线索的试驾单bo
     *
     * @return
     */
    public SacTestDriveSheetBO buildSacTestDriveSheetBO() {
        SacTestDriveSheetBO bo = new SacTestDriveSheetBO();
        bo.setTestDriveSheetId(this.getTestDriveSheetId());
        bo.setCustomerPhone(this.getCustPhone());
        return bo;
    }
}
