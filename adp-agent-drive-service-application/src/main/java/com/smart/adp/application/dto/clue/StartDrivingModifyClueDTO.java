package com.smart.adp.application.dto.clue;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 开始试驾更新线索的信息DTO
 * @Author: rik.ren
 * @Date: 2025/5/29 17:11
 **/
@Builder
@Data
public class StartDrivingModifyClueDTO {

    /**
     * 线索id
     */
    private String custId;

    /**
     * 线索手机号
     */
    private String custPhone;

    /**
     * 意向车型code
     */
    private String intenCarTypeCode;

    /**
     * 意向车型名称
     */
    private String intenCarTypeName;

    /**
     * 最近一次试驾时间
     */
    private LocalDateTime lastTestdriverTime;
}
