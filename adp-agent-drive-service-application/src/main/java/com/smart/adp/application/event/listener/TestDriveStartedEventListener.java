package com.smart.adp.application.event.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.application.event.TestDriveStartedEvent;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsBO;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.ClueInfoAggregate;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 开始试驾后续操作的领域事件监听器
 * @Author: rik.ren
 * @Date: 2025/6/19 13:50
 **/
@Component
@Slf4j
public class TestDriveStartedEventListener implements ApplicationListener<TestDriveStartedEvent> {
    @Autowired
    private ClueInfoAggregate clueInfoAggregate;
    @Autowired
    private BaseInfoAggregate baseInfoAggregate;
    @Autowired
    @Qualifier("smartExecutor")
    private Executor asyncTaskExecutor;
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void onApplicationEvent(TestDriveStartedEvent event) {

        log.info("开始试驾后续操作的领域事件监听器");

        // 在主线程中捕获用户上下文传递给异步线程
        UserBusiEntity userBusiEntity = UserInfoContext.get();

        CompletableFuture.runAsync(() -> {
            try {
                // 如果入参中没有带custId，那可能是线索还没创建好，所以这里需要主动查询线索
                if (StringUtils.isBlank(event.getStartTestDriveObj().getCustId())) {

                    SacClueInfoDlrBO queryClueInfoDlrResult =
                            clueInfoAggregate.queryClueInfoDlr(event.getStartTestDriveObj().getCustomerPhone());
                    // 如果还是没查到，那就重新发送事件
                    if (ObjectUtil.isEmpty(queryClueInfoDlrResult) || StringUtils.isBlank(queryClueInfoDlrResult.getCustId())) {
                        log.info("开始试驾后续操作的领域事件监听器，线索还没创建好，重新发送事件");
                        TimeUnit.MILLISECONDS.sleep(100);
                        eventPublisher.publishEvent(new TestDriveStartedEvent(this, event.getStartTestDriveObj()));
                        return;
                    }
                    log.info("开始试驾后续操作的领域事件监听器，线索已经创建好，custId = {}", queryClueInfoDlrResult.getCustId());
                    event.getStartTestDriveObj().setCustId(queryClueInfoDlrResult.getCustId());
                }
                // 更新线索的意向车型
                String modifyIntentionCarResult = clueInfoAggregate.modifyIntentionCar(
                        event.getStartTestDriveObj().getCustId(),
                        event.getStartTestDriveObj().getSmallCarTypeCode(),
                        userBusiEntity);

                // 通知CDP
                IfsBaseCdpLeadsBO cdpLeadsBO = new IfsBaseCdpLeadsBO();
                Boolean cdpResult =
                        baseInfoAggregate.saveCdpLeads(cdpLeadsBO.buildStartDrivingCdpLeadBO(event.getStartTestDriveObj().getCustomerPhone(),
                                modifyIntentionCarResult));
                log.info("开始试驾后续通知CDP {}", cdpResult);
            } catch (Exception e) {
                log.error("开始试驾事件处理失败 {}", JSONObject.toJSONString(event.getStartTestDriveObj()), e);
                throw new BusinessException(RespCode.FAIL.getCode(), "开始试驾事件处理失败");
            }
        }, asyncTaskExecutor);
    }
}
