package com.smart.adp.application.service.drive.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.smart.adp.application.dto.drive.AllocationTestDriveTaskDTO;
import com.smart.adp.application.dto.drive.CancelTestDriveTaskDTO;
import com.smart.adp.application.dto.drive.QueryTestDriverTaskDTO;
import com.smart.adp.application.dto.drive.SaveTestDriveTaskDTO;
import com.smart.adp.application.service.drive.TestDriveTaskService;
import com.smart.adp.application.vo.drive.QueryTestDriveTaskRspVO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsBO;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsEventBO;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.ClueInfoAggregate;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import com.smart.adp.domain.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 试驾任务service实现
 * @Author: rik.ren
 * @Date: 2025/7/16 16:09
 **/
@Slf4j
@Service
public class TestDriveTaskServiceImpl implements TestDriveTaskService {
    @Autowired
    private TestDriveAggregate testDriveAggregate;
    @Autowired
    private ClueInfoAggregate clueInfoAggregate;
    @Autowired
    private BaseInfoAggregate baseInfoAggregate;

    /**
     * 分页查询试驾任务列表
     *
     * @param param
     * @return
     */
//    @Override
//    public PageVO<QueryTestDriveTaskRspVO> queryTestDriveTaskPage(QueryTestDriverTaskDTO param) {
//
//        // 1. 查询试驾任务表信息
//        DomainPage<SacTestDriveTaskBO> queryTestTaskResult = testDriveAggregate.queryTestDriveTask(param.buildSacTestDriveTaskBO());
//        if (ObjectUtil.isEmpty(queryTestTaskResult)
//                || ObjectUtil.isEmpty(queryTestTaskResult.getRecords())
//                || queryTestTaskResult.getTotalCount() == 0L) {
//            return PageVO.<QueryTestDriveTaskRspVO>builder().records(null).pageSize(param.getPageSize()).pageNumber(param.getPageIndex()).totalRow(0L).build();
//        }
//
//        // 2. 查询线索信息
//        List<String> listPhone =
//                queryTestTaskResult.getRecords().stream().map(SacTestDriveTaskBO::getPhone).collect(Collectors.toList());
//        List<SacClueInfoDlrBO> listClueInfoDlrBO = clueInfoAggregate.queryListClueInfoDlr(listPhone);
//
//        // 3. 转换为返回结果
//        PageVO<QueryTestDriveTaskRspVO> resultVO = QueryTestDriveTaskRspVO.convent(queryTestTaskResult, listClueInfoDlrBO);
//        return resultVO;
//    }

    /**
//     * 分页查询试驾任务个数
//     *
//     * @param param
//     * @return
//     */
//    @Override
//    public Long queryTestDriveTaskCount(QueryTestDriverTaskDTO param) {
//        Long countResult = testDriveAggregate.queryTestDriveTaskCount(param.buildSacTestDriveTaskBO());
//        return countResult;
//    }


    /**
     * 创建试驾任务
     *
     * @param param
     * @return
     */
    @Override
    public Boolean saveTestDriverTask(SaveTestDriveTaskDTO param) {
        return null;
    }

    /**
     * 店长分配试驾任务给到产品专家
     *
     * @param param
     * @return
     */
    @Override
    public Boolean allocationTestDriverTask(AllocationTestDriveTaskDTO param) {
        log.info("店长分配试驾任务开始,param={}", JSONUtil.toJsonStr(param));
        if (!UserUtil.isStoreManager(UserInfoContext.get().getStationId())) {
            log.warn("非店长不允许进行试驾任务分配,param={}", JSONUtil.toJsonStr(param));
            throw new BusinessException(RespCode.FAIL.getCode(), "非店长不允许分配试驾任务！");
        }

        //预约车型校验【该手机号已预约该车型,请确认】

        //被分配人在职状态脚丫

        //试驾任务更新【分配人】

        //发送试驾任务，企微提醒

        return null;
    }

    /**
     * 更新试驾任务
     *
     * @param taskId
     * @param sheetBO
     * @return
     */
    @Override
    public Boolean modifyTestDriveTask(String taskId, String oldUpdateControlId, SacTestDriveSheetBO sheetBO) {
        Boolean result = testDriveAggregate.modifyTestDriveTask(taskId, oldUpdateControlId, sheetBO);
        return result;
    }

    /**
     * 取消试驾任务
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelTestDriverTask(CancelTestDriveTaskDTO param) {
        //1、更新试驾任务状态为取消
        UserBusiEntity userBusiEntity = UserInfoContext.get();
        Boolean result = testDriveAggregate.modifyTestDriveTask(param.buildCancelTaskBO(param.getTestDriveTaskId(), param.getTaskUpdateControlId(), userBusiEntity));

        //2、试驾取消事件推送CDP
        IfsBaseCdpLeadsEventBO cdpLeadsBO = new IfsBaseCdpLeadsEventBO();
        Boolean cdpResult = baseInfoAggregate.saveCdpLeadsEvent(cdpLeadsBO.buildCancelTestTaskCdpLeadBO(param.getPhone()));
        log.info("取消试驾任务试驾后续通知CDP {}", cdpResult);
        return result;
    }
}
