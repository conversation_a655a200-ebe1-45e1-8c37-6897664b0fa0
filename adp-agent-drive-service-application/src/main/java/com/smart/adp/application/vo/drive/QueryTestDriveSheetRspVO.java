package com.smart.adp.application.vo.drive;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.enums.TestDriveMethodEnum;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.clue.bo.ClueEventFlowBO;
import com.smart.adp.domain.model.clue.bo.SacOneCustRemarkBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: 分页查询试乘试驾单VO
 * @Author: rik.ren
 * @Date: 2025/5/14 17:06
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class QueryTestDriveSheetRspVO {

    /**
     * 试乘试驾单id
     */
    @Schema(description = "试乘试驾单id")
    private String testDriveSheetId;

    /**
     * 预约单id
     */
    @Schema(description = "预约单id")
    private String appointmentId;

    /**
     * 试乘试驾状态
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "试乘试驾状态")
    private String testStatus;

    /**
     * 试乘试驾状态
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "试乘试驾状态")
    private String testStatusName;


    /**
     * 所属专营店名称
     */
    @Schema(description = "所属专营店名称")
    private String dlrName;

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名")
    private String customerName;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话")
    private String customerPhone;

    /**
     * 销售顾问姓名
     */
    @Schema(description = "销售顾问姓名")
    private String salesConsultantName;

    /**
     * 销售顾问ID
     */
    @Schema(description = "销售顾问ID")
    private String salesConsultantId;

    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    private String eventName;

    /**
     * 线索等级(H/A/B/C/D/E)
     */
    @Schema(description = "线索等级(H/A/B/C/D/E)")
    private String clueLevel;

    /**
     * 试乘试驾车型编码
     */
    @Schema(description = "试乘试驾车型编码")
    private String smallCarTypeCode;

    /**
     * 试乘试驾车型名称
     */
    @Schema(description = "试乘试驾车型名称")
    private String smallCarTypeName;

    /**
     * 试驾车牌
     */
    @Schema(description = "试驾车牌")
    private String plateNumber;

    /**
     * 试乘试驾类型(0：试乘，1：试驾，2：深度试驾)
     *
     * @see TestDriveTypeEnum
     */
    @Schema(description = "试乘试驾类型(0：试乘，1：试驾，2：深度试驾)")
    private String testTypeName;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private LocalDateTime createdDate;

    /**
     * 预约试乘试驾日期(普通试乘试驾)
     */
    @Schema(description = "预约试乘试驾日期")
    private String appointmentTestDateTime;

    /**
     * 试驾车所属门店
     */
    @Schema(description = "试驾车所属门店")
    private String testCarBelongStore;

    /**
     * 试驾时长
     */
    @Schema(description = "试驾时长")
    private String duration;

    /**
     * 试驾方式
     *
     * @see TestDriveMethodEnum
     */
    @Schema(description = "试驾方式")
    private String testMethod;

    /**
     * 试驾方式名称
     *
     * @see TestDriveMethodEnum
     */
    @Schema(description = "试驾方式名称")
    private String testMethodName;

    /**
     * 试乘试驾行驶里程
     */
    @Schema(description = "试乘试驾行驶里程")
    private java.math.BigDecimal testRoadHaul;

    /**
     * 试乘试驾开始里程
     */
    @Schema(description = "试乘试驾开始里程")
    private java.math.BigDecimal testStartRoadHaul;

    /**
     * 试乘试驾结束里程
     */
    @Schema(description = "试乘试驾结束里程")
    private java.math.BigDecimal testEndRoadHaul;
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String endTime;

    /**
     * 录音ID
     */
    @Schema(description = "录音ID")
    private String recordId;

    /**
     * 并发控制锁
     */
    @Schema(description = "并发控制锁")
    private String updateControlId;


    public static PageVO<QueryTestDriveSheetRspVO> convent(DomainPage<SacTestDriveSheetBO> testDriveDomainPage,
                                                           List<TestCarPrepareBO> listTestCarPrepare,
                                                           List<ClueEventFlowBO> listClueEventFlow,
                                                           List<SacOneCustRemarkBO> listCustRemark) {
        if (ObjectUtil.isEmpty(testDriveDomainPage) || CollectionUtil.isEmpty(testDriveDomainPage.getRecords())) {
            return PageVO.<QueryTestDriveSheetRspVO>builder().build();
        }
        // 使用条件表达式初始化Map，确保变量成为实际上的最终变量
//        Map<String, TestCarPrepareBO> testCarPrepareBOMap = CollectionUtil.isNotEmpty(listTestCarPrepare)
//                ? listTestCarPrepare.stream()
//                .collect(Collectors.toMap(TestCarPrepareBO::getCarLicenceNo, Function.identity()))
//                : Collections.emptyMap();
        Map<String, TestCarPrepareBO> testCarPrepareBOMap = CollectionUtil.isNotEmpty(listTestCarPrepare)
                ? listTestCarPrepare.stream()
                .collect(Collectors.toMap(
                        TestCarPrepareBO::getCarLicenceNo,
                        Function.identity(),
                        (existing, replacement) -> existing)) // 保留第一个出现的值
                : Collections.emptyMap();

        Map<String, ClueEventFlowBO> flowRspMap = CollectionUtil.isNotEmpty(listClueEventFlow)
                ? listClueEventFlow.stream()
                .collect(Collectors.toMap(ClueEventFlowBO::getCustId, Function.identity()))
                : Collections.emptyMap();

        Map<String, SacOneCustRemarkBO> custRemarkMap = CollectionUtil.isNotEmpty(listCustRemark)
                ? listCustRemark.stream()
                .collect(Collectors.toMap(SacOneCustRemarkBO::getCustId, Function.identity()))
                : Collections.emptyMap();

        // 使用Stream处理转换逻辑
        List<QueryTestDriveSheetRspVO> result = testDriveDomainPage.getRecords().stream()
                .map(sheetBO -> {
                    QueryTestDriveSheetRspVO sheetRspVO = new QueryTestDriveSheetRspVO();
                    sheetRspVO.setTestDriveSheetId(sheetBO.getTestDriveSheetId());
                    sheetRspVO.setAppointmentId(sheetBO.getAppointmentId());
                    sheetRspVO.setTestStatus(sheetBO.getTestStatus());
                    sheetRspVO.setTestStatusName(ObjectUtil.isEmpty(sheetBO.getTestStatus()) ? null :
                            TestDriveStatusEnum.getByCode(sheetBO.getTestStatus()).getDesc());
                    sheetRspVO.setDlrName(sheetBO.getDlrName());
                    sheetRspVO.setCustomerName(sheetBO.getCustomerName());
                    sheetRspVO.setCustomerId(sheetBO.getCustomerId());
                    sheetRspVO.setCustomerPhone(sheetBO.getCustomerPhone());
                    sheetRspVO.setSalesConsultantName(sheetBO.getSalesConsultantName());
                    sheetRspVO.setSalesConsultantId(sheetBO.getSalesConsultantId());
                    sheetRspVO.setSmallCarTypeCode(sheetBO.getSmallCarTypeCode());
                    sheetRspVO.setSmallCarTypeName(sheetBO.getSmallCarTypeName());
                    sheetRspVO.setPlateNumber(sheetBO.getPlateNumber());
                    sheetRspVO.setTestTypeName(ObjectUtil.isEmpty(sheetBO.getTestType())
                            ? null
                            : TestDriveTypeEnum.getByCode(sheetBO.getTestType()).getDesc());
                    sheetRspVO.setTestMethod(sheetBO.getTestDriveMethod());
                    sheetRspVO.setTestMethodName(ObjectUtil.isEmpty(sheetBO.getTestDriveMethod())
                            ? null
                            : TestDriveMethodEnum.getByCode(sheetBO.getTestDriveMethod()).getDesc());

                    sheetRspVO.setCreatedDate(sheetBO.getCreatedDate());
                    sheetRspVO.setDuration(getFormattedDuration(sheetBO));
                    sheetRspVO.setTestRoadHaul(sheetBO.getTestRoadHaul());
                    sheetRspVO.setTestStartRoadHaul(sheetBO.getTestStartRoadHaul());
                    sheetRspVO.setTestEndRoadHaul(sheetBO.getTestEndRoadHaul());
                    sheetRspVO.setStartTime(sheetBO.getStartTime());
                    sheetRspVO.setEndTime(sheetBO.getEndTime());
                    sheetRspVO.setRecordId(sheetBO.getRecordId());
                    if (TestDriveTypeEnum.DEEP_TEST.getCode().equals(sheetBO.getTestType())) {
                        String start = Optional.ofNullable(sheetBO.getAppointmentStartTime()).orElse("");
                        String end = Optional.ofNullable(sheetBO.getAppointmentEndTime()).orElse("");
                        sheetRspVO.setAppointmentTestDateTime(start + " " + end);
                    } else {
                        String date = Optional.ofNullable(sheetBO.getAppointmentTestDate()).orElse("");
                        String time = Optional.ofNullable(sheetBO.getAppointmentTestTime()).orElse("");
                        sheetRspVO.setAppointmentTestDateTime(date + " " + time);
                    }
                    // 处理试驾车所属门店
                    Optional.ofNullable(testCarPrepareBOMap.get(sheetBO.getPlateNumber()))
                            .ifPresent(bo -> sheetRspVO.setTestCarBelongStore(bo.getDlrShortName()));

                    // 处理事件名称
                    Optional.ofNullable(flowRspMap.get(sheetBO.getCustomerId()))
                            .ifPresent(flow -> sheetRspVO.setEventName(flow.getEventName()));

                    // 处理事件名称
                    Optional.ofNullable(custRemarkMap.get(sheetBO.getCustomerId()))
                            .ifPresent(remark -> sheetRspVO.setClueLevel(remark.getClueLevel()));
                    sheetRspVO.setUpdateControlId(sheetBO.getUpdateControlId());
                    return sheetRspVO;
                })
                .collect(Collectors.toList());

        // 构建分页结果
        PageVO<QueryTestDriveSheetRspVO> vo = new PageVO<>();
        vo.setRecords(result);
        vo.setTotalRow(testDriveDomainPage.getTotalCount());
        return vo;
    }

    private static String getFormattedDuration(SacTestDriveSheetBO param) {
        // 跳过开始时间为空的记录
        if (param.getStartTime() == null || param.getStartTime().isEmpty()) {
            return null;
        }
        try {
            // 解析开始时间
            LocalDateTime start = LocalDateTime.parse(param.getStartTime(), TimeConstant.DEFAULT_FORMATTER);

            // 确定结束时间（如果为空则使用当前时间）
            LocalDateTime end = (param.getEndTime() == null || param.getEndTime().isEmpty())
                    ? LocalDateTime.now()
                    : LocalDateTime.parse(param.getEndTime(), TimeConstant.DEFAULT_FORMATTER);

            // 确保结束时间在开始时间之后
            if (end.isBefore(start)) {
                param.setDuration("结束时间早于开始时间");
                return null;
            }

            // 计算时间差
            Duration duration = Duration.between(start, end);

            return formatDuration(duration);

        } catch (Exception e) {
            param.setDuration("时间格式错误: " + e.getMessage());
        }
        return null;
    }

    private static String formatDuration(Duration duration) {
        // 计算总分钟数
        long totalMinutes = duration.toMinutes();

        // 处理分钟进位到小时的情况
        long hours = totalMinutes / 60;
        long minutes = totalMinutes % 60;

        if (hours >= 24) {
            // 超过一天：显示d天h小时m分钟
            long days = hours / 24;
            long remainingHours = hours % 24;

            StringBuilder sb = new StringBuilder();
            sb.append(days).append("天");
            if (remainingHours > 0) {
                sb.append(remainingHours).append("小时");
            }
            if (minutes > 0) {
                sb.append(minutes).append("分钟");
            }
            return sb.toString();

        } else {
            // 不足一天：显示h小时m分钟
            return hours + "小时" + minutes + "分钟";
        }
    }

    private static String getFormattedDuration1(String duration) {
        if (StringUtils.isBlank(duration)) return "";

        // 定义正则表达式匹配"X小时Y分钟"格式
        Pattern pattern = Pattern.compile("(\\d+)\\s*小时\\s*(\\d+)\\s*分钟");
        Matcher matcher = pattern.matcher(duration);
        if (matcher.find()) {
            try {
                // 提取小时和分钟数值
                int hours = Integer.parseInt(matcher.group(1));
                int minutes = Integer.parseInt(matcher.group(2));
                // 处理分钟进位
                if (minutes >= 60) {
                    hours += minutes / 60;
                    minutes = minutes % 60;
                }
                // 如果总时长不足24小时，保持原格式
                if (hours < 24) {
                    return String.format("%d小时%d分钟", hours, minutes);
                }
                // 计算天数和剩余小时
                int days = hours / 24;
                int remainingHours = hours % 24;

                // 构建格式化字符串
                StringBuilder result = new StringBuilder();
                if (days > 0) {
                    result.append(days).append("天");
                }
                if (remainingHours > 0) {
                    result.append(remainingHours).append("小时");
                }
                if (minutes > 0 || (remainingHours == 0 && days == 0)) {
                    result.append(minutes).append("分钟");
                }
                return result.toString();

            } catch (NumberFormatException e) {
                return "时长格式错误: 包含非数字字符";
            }
        }
        return "时长格式错误: 应匹配'X小时Y分钟'模式";
    }

}
