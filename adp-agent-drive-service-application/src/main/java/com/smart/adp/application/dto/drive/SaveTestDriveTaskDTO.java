package com.smart.adp.application.dto.drive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * @Description: 创建试驾任务DTO
 * @Author: rik.ren
 * @Date: 2025/5/27 19:40
 **/
@Data
@Builder
public class SaveTestDriveTaskDTO {

    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    private String sendDlrName;

    /**
     * 预约日期
     */
    @Schema(description = "预约日期")
    private String appointmentTestDate;

    /**
     * 预约开始时间
     */
    @Schema(description = "预约开始时间")
    private String appointmentStartTime;

    /**
     * 预约结束时间
     */
    @Schema(description = "预约结束时间")
    private String appointmentEndTime;

    /**
     * 预约时间段
     */
    @Schema(description = "预约时间段")
    private String appointmentTestTime;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题")
    private String taskTitle;

    /**
     * 任务人的门店code
     */
    @Schema(description = "任务人的门店code")
    private String taskPersonDlrCode;

    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    private String taskPersonDlrName;

    /**
     * 车型名称
     */
    @Schema(description = "车型名称")
    private String smallCarTypeName;

    /**
     * 任务人id
     */
    @Schema(description = "任务人id")
    private String taskPersonId;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 性别
     */
    @Schema(description = "性别")
    private String genderCode;

    /**
     * * 性别名称
     */
    @Schema(description = "性别名称")
    private String genderName;

    /**
     * 试驾方式，上门试驾，到店试驾
     */
    @Schema(description = "试驾方式，上门试驾，到店试驾")
    private String testDriveMethod;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String custName;

    /**
     *
     */
    @Schema(description = "")
    private String msgTestType;

    /**
     * 试驾类型
     */
    @Schema(description = "试驾类型")
    private String testType;

    /**
     * 专家id
     */
    @Schema(description = "专家id")
    private String salesConsultantName;
}
