package com.smart.adp.application.event;

import com.smart.adp.application.dto.drive.CreateTestDriverSheetDTO;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 创建试驾单领域事件
 * @Author: rik.ren
 * @Date: 2025/6/19 13:48
 **/
@Getter
@Setter
@ToString
public class TestDriveCreatedEvent extends ApplicationEvent {
    private SacTestDriveSheetBO sacTestDriveSheetObj;
    private CreateTestDriverSheetDTO testDriverSheetDTO;
    private SacClueInfoDlrBO sacClueInfoDlrObj;

    public TestDriveCreatedEvent(Object source, SacTestDriveSheetBO sacTestDriveSheetObj, CreateTestDriverSheetDTO testDriverSheetDTO) {
        super(source);
        this.sacTestDriveSheetObj = sacTestDriveSheetObj;
        this.testDriverSheetDTO = testDriverSheetDTO;
    }

    /**
     * 构建客户履历保存入参
     *
     * @return
     */
    public Map<String, Object> buildResumeSave() {
        Map<String, Object> map = new HashMap<>();
        // 基础信息
        map.put("custId", this.getSacClueInfoDlrObj().getCustId());
        map.put("senceCode", "10");
        map.put("senceName", "试驾预约");

        // 车辆信息（需要从sheet对象获取）
        map.put("testDriveOrderNo", this.getSacTestDriveSheetObj().getTestDriveOrderNo());
        map.put("smallCarTypeCode", this.getSacTestDriveSheetObj().getSmallCarTypeCode());
        map.put("smallCarTypeName", this.getSacTestDriveSheetObj().getSmallCarTypeName());
        map.put("plateNumber", this.getSacTestDriveSheetObj().getPlateNumber());

        // 经销商信息
        map.put("dlrName", this.getSacTestDriveSheetObj().getDlrName());

        // 预约时间信息
        map.put("appointmentTime", this.getSacTestDriveSheetObj().getCreatedDate().format(TimeConstant.DEFAULT_FORMATTER));

        // 客户联系信息
        map.put("phone", this.getSacClueInfoDlrObj().getPhone());

        // 线索等级intenLevelCode
        map.put("clueLevelCode", this.getSacTestDriveSheetObj().getIntenLevelCode());

        // 构建嵌套的resumeDesc JSON
        String resumeDesc = String.format(
                "{\"smallCarTypeCode\":\"%s\",\"smallCarTypeName\":\"%s\",\"plateNumber\":\"%s\","
                        + "\"appointmentTestDate\":\"%s\",\"appointmentTestTime\":\"%s\","
                        + "\"appointmentStartTime\":\"%s\",\"appointmentEndTime\":\"%s\"}",
                this.getSacTestDriveSheetObj().getSmallCarTypeCode(),
                this.getSacTestDriveSheetObj().getSmallCarTypeName(),
                this.getSacTestDriveSheetObj().getPlateNumber(),
                this.getTestDriverSheetDTO().getAppointmentTestDate(),
                this.getTestDriverSheetDTO().getAppointmentTestTime(),
                this.getTestDriverSheetDTO().getAppointmentStartTime(),
                this.getTestDriverSheetDTO().getAppointmentEndTime()
        );
        map.put("resumeDesc", resumeDesc);

        return map;
    }

}
