package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.infrastructure.utils.AgreementFileUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * @Description: 生成试驾协议文件DTO
 * @Author: rik.ren
 * @Date: 2025/6/24 19:14
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerateAgreementFileDTO {
    /**
     * 签名地址
     */
    @Schema(description = "签名图片地址", required = true)
    private String signatureUrl;
    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名", required = true)
    private String name;
    /**
     * 代理商城市公司名称
     */
    @Schema(description = "代理商城市公司名称", required = false)
    private String companyName;
    /**
     * 试驾类型
     */
    @Schema(description = "试驾类型", required = true)
    private String testDriveType;
    /**
     * 证件号
     */
    @Schema(description = "证件号", required = true)
    private String idCardNo;
    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id", required = true)
    private String testDriveSheetId;

    public String getCompanyName() {
        if (StringUtils.isEmpty(companyName)) {
            this.companyName = UserInfoContext.get().getCompanyName();
        }
        return companyName;
    }

    public void setCompanyName(String companyName) {
        if (StringUtils.isEmpty(this.companyName)) {
            this.companyName = UserInfoContext.get().getCompanyName();
        }
    }

    public AgreementFileUtil.GenerateData buildGenerateData() {
        AgreementFileUtil.GenerateData generateData = new AgreementFileUtil.GenerateData();
        generateData.setPartyA(getCompanyName());
        generateData.setPartyB(getName());
        generateData.setTestDriveType(getTestDriveType());
        generateData.setIdCardNo(getIdCardNo());
        generateData.setSignDate(LocalDateTime.now().format(TimeConstant.TIME_D_FORMATTER));
        generateData.setSignatureUrl(getSignatureUrl());
        generateData.setTestDriveSheetId(getTestDriveSheetId());
        return generateData;
    }

    public String getName() {
        if (name.contains(".pdf")) {
            // 因为试驾单的姓名可以任意写，所以如果姓名中包含.pdf，就要提前将用户名去掉.，为了和pdf文件后缀区分开
            name = name.replace(".pdf", "pdf");
        }
        return name;
    }
}
