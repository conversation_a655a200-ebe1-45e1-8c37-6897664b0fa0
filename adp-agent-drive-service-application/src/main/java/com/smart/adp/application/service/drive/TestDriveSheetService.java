package com.smart.adp.application.service.drive;

import com.smart.adp.application.dto.base.NameMatchIdCardDTO;
import com.smart.adp.application.dto.drive.*;
import com.smart.adp.application.vo.drive.*;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.vo.DriveTaskVO;

/**
 * @Description: 试驾单applicationService
 * @Author: rik.ren
 * @Date: 2025/5/15 15:18
 **/
public interface TestDriveSheetService {
    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    PageVO<QueryTestDriveSheetRspVO> queryTestDriveSheetList(QueryTestDriverSheetDTO param);

    /**
     * 根据条件查询试驾单
     *
     * @param param
     * @return
     */
    Long queryTestDriveSheetCount(QueryTestDriverSheetDTO param);

    /**
     * 创建试乘试驾单
     *
     * @param param
     * @return
     */
    CreateTestDriveRspVO createTestDriveSheet(CreateTestDriverSheetDTO param);

    /**
     * 更新试乘试驾单
     *
     * @param param
     * @return
     */
    Boolean modifyTestDriverSheet(ModifyTestDriverSheetDTO param);

    /**
     * 根据条件查询某一条试乘试驾单
     *
     * @param param
     * @return
     */
    QueryTestDriveSheetInfoRspVO queryTestDriveSheetInfo(QueryTestDriverSheetInfoDTO param);

    /**
     * 根据条件查询是否是同一家门店
     *
     * @param param
     * @return
     */
    QueryTestDriveSheetSameDlrCodeRspVO querySameDlrCodeFlag(QueryTestDriverCustSameDlrDTO param);

    /**
     * 开始试驾
     *
     * @param param
     * @return
     */
    Boolean testDriveStart(StartTestDriveDTO param);

    /**
     * 结束试驾
     *
     * @param param
     * @return
     */
    Boolean testDriveStop(StopTestDriveDTO param);

    /**
     * 试驾签到
     *
     * @param param
     * @return
     */
    Boolean testDriveSign(SignTestDriveDTO param);

    /**
     * 身份证OCR识别
     *
     * @param param
     * @return
     */
    IDCardOCRVO IDCardOCR(OCRInfoDTO param);

    /**
     * 驾驶证OCR识别
     *
     * @param param
     * @return
     */
    DriverLicenseOCRVO driverLicenseOCR(OCRInfoDTO param);

    /**
     * 姓名身份证号匹配关系校验
     *
     * @param param
     * @return
     */
    Boolean nameMatchIdCard(NameMatchIdCardDTO param);

    /**
     * 生成用户协议文件
     *
     * @param param
     * @return
     */
    GenerateAgreementRspVO generateAgreementFile(GenerateAgreementFileDTO param);

    /**
     * word转pdf
     *
     * @param param
     * @return
     */
    String conventDocToPdf(ConventDocToPdfDTO param);

    /**
     * 试驾单数据重推外部服务
     *
     * @param param
     * @return
     */
    Object testSheetDataRePush(TestSheetDataRePushDTO param);

    /**
     * 试驾任务分页
     *
     * @param dto 查询入参
     * @return page
     */
    PageVO<DriveTaskVO> taskPage(DriveTaskDTO dto);

    /**
     * 试驾任务刷新
     *
     * @param id  试驾任务 ID
     * @param dto 查询入参
     * @return vo
     */
    DriveTaskVO taskRefresh(String id, DriveTaskDTO dto);

    /**
     * 试驾任务代办
     *
     * @return boolean
     */
    Boolean taskTodo();
}
