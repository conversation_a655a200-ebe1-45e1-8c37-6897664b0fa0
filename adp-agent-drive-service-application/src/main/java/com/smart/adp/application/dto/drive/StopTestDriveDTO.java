package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 结束试驾入参DTO
 * @Author: rik.ren
 * @Date: 2025/5/28 20:10
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StopTestDriveDTO {

    /**
     * 试驾单ID
     */
    @Schema(description = "试驾单ID", required = true)
    @NotBlank(message = "试驾单ID不能为空")
    private String testDriveSheetId;

    /**
     * 开始里程
     */
    @Schema(description = "开始里程")
    private Integer testStartRoadHaul;

    /**
     * 结束里程
     */
    @Schema(description = "结束里程")
    private BigDecimal testEndRoadHaul;

    /**
     * 记录内容
     */
    @Schema(description = "记录内容")
    private String testDriveEndRemark;

    /**
     * 并发控制锁
     */
    @Schema(description = "并发控制锁")
    @NotBlank(message = "并发控制锁不能为空")
    private String updateControlId;

    /**
     * 构建开始试驾的试驾单BO
     *
     * @return
     */
    public SacTestDriveSheetBO buildStopTestDriveBO() {
        LocalDateTime now = LocalDateTime.now();
        SacTestDriveSheetBO sheetBO = new SacTestDriveSheetBO();
        sheetBO.setTestStatus(TestDriveStatusEnum.COMPLETED.getCode());
        sheetBO.setEndTime(TimeConstant.DEFAULT_FORMATTER.format(now));
        sheetBO.setTestDriveEndRemark(testDriveEndRemark);
        sheetBO.setTestEndRoadHaul(testEndRoadHaul);
        sheetBO.setTestDriveSheetId(this.testDriveSheetId);
        sheetBO.setModifyName(UserInfoContext.get().getEmpName());
        sheetBO.setModifier(UserInfoContext.get().getUserID());
        sheetBO.setLastUpdatedDate(now);
        sheetBO.setUpdateControlId(this.updateControlId);
        return sheetBO;
    }
}
