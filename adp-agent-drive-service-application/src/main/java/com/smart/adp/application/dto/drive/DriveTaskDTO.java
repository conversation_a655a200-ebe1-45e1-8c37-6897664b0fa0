package com.smart.adp.application.dto.drive;

import com.smart.adp.application.common.PageDTO;
import com.smart.adp.domain.enums.TestDriveTaskStatusEnum;
import com.smart.adp.domain.qry.DriveTaskQry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(description = "试驾任务分页入参")
public class DriveTaskDTO extends PageDTO {

    /**
     * 试驾任务状态
     */
    @NotNull(message = "状态不能为空")
    private TestDriveTaskStatusEnum status;

    public DriveTaskQry buildQry() {
        DriveTaskQry qry = new DriveTaskQry();
        qry.setPageIndex(getPageIndex());
        qry.setPageSize(getPageSize());
        qry.setStatus(getStatus());
        return qry;
    }
}
