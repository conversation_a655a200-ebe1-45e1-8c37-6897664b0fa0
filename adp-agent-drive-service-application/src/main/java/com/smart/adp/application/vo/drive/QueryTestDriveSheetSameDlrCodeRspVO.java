package com.smart.adp.application.vo.drive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 分页查询试乘试驾单VO
 * @Author: rik.ren
 * @Date: 2025/5/14 17:06
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class QueryTestDriveSheetSameDlrCodeRspVO {

    /**
     * 试乘试驾单id
     */
    @Schema(description = "试乘试驾单id")
    private String testDriveSheetId;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private String custId;

    /**
     * 线索门店和试驾单门店一致
     */
    @Schema(description = "线索门店和试驾单门店一致")
    private Boolean isThisDlr = Boolean.FALSE;
}
