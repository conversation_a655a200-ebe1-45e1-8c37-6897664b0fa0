package com.smart.adp.application.rePushData;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 根据 RePushTestDriveTypeEnum 获取对应的策略
 * @Author: rik.ren
 * @Date: 2025/7/9 11:08
 **/
@Component
public class RePushTestDataStrategyFactory {

    private final Map<Integer, RePushTestDataStrategy> strategyMap = new HashMap<>();

    @Autowired
    public RePushTestDataStrategyFactory(Map<Integer, RePushTestDataStrategy> rePushTestDataStrategyMap) {
        rePushTestDataStrategyMap.forEach((key, value) -> this.strategyMap.put(key, value));
    }

    /**
     * 从map中获取对应的执行者，map是在项目启动时加载进去的，方法在ClueModifyStrategyConfig
     *
     * @param clueModifyType
     * @return
     */
    public RePushTestDataStrategy getStrategy(Integer clueModifyType) {
        RePushTestDataStrategy rePushTestDataStrategy = strategyMap.get(clueModifyType);
        if (ObjectUtil.isEmpty(rePushTestDataStrategy)) {
            throw new BusinessException(RespCode.FAIL.getCode(), "重推类型不存在");
        }
        return rePushTestDataStrategy;
    }
}