package com.smart.adp.application.utils;

import com.smart.adp.domain.common.constants.TimeConstant;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/7
 */
public class LocalDateTimeUtil {
    /**
     * 将字符串 20210512101011 转化为 Date
     *
     * @param time
     * @return
     */
    public static Date getDateFromString(String time) {
        return Date.from(LocalDateTime.parse(time, TimeConstant.STR_TIME_D_FORMATTER).atZone(ZoneId.systemDefault()).toInstant());
    }


    public static Date getDateFromTimeString(String time, DateTimeFormatter dateTimeFormatter) {
        return Date.from(LocalDateTime.parse(time, dateTimeFormatter).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * get current time as default format
     *
     * @return time
     */
    public static String getCurrentDateTimeAsString() {
        return getCurrentDateTimeAsString(TimeConstant.DEFAULT_FORMATTER);
    }

    /**
     * get current date as default format
     *
     * @return time
     */
    public static String getCurrentDateAsString() {
        return getCurrentDateTimeAsString(TimeConstant.TIME_D_FORMATTER);
    }

    public static String getYearMonthAsString() {
        return getCurrentDateTimeAsString(TimeConstant.YEAR_MONTH_FORMATTER);
    }

    /**
     * get date as 20210513
     *
     * @return
     */
    public static String getCurrentDateAsPlainString() {
        return getCurrentDateTimeAsString(TimeConstant.TIME_PLAIN_D_FORMATTER);
    }

    public static String getMsgDt() {
        return getCurrentDateTimeAsString(TimeConstant.MSG_D_FORMATTER);
    }

    public static String getMillisecondTimeAsPlainString() {
        return getCurrentDateTimeAsString(TimeConstant.MILLISECOND_TIME_D_FORMATTER);
    }

    public static String getCurrentMonthAsPlainString() {
        return getCurrentDateTimeAsString(TimeConstant.TIME_PLAIN_M_FORMATTER);
    }

    public static String getCurrentTimeAsPlainString() {
        return getCurrentDateTimeAsString(TimeConstant.STR_TIME_D_FORMATTER);
    }

    public static String getCurrentTime6AsPlainString() {
        return getCurrentDateTimeAsString(TimeConstant.STR_TIME_6_FORMATTER);
    }

    public static String getCurrentTime10AsPlainString() {
        return getCurrentDateTimeAsString(TimeConstant.STR_TIME_10_FORMATTER);
    }

    /**
     * get current time as format
     *
     * @param format format
     * @return time
     */
    public static String getCurrentDateTimeAsString(String format) {
        return getCurrentDateTimeAsString(LocalDateTime.now(), format);
    }

    /**
     * get current time as format
     *
     * @param localDateTime local date time
     * @param format        format
     * @return time
     */
    public static String getCurrentDateTimeAsString(LocalDateTime localDateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return getCurrentDateTimeAsString(localDateTime, formatter);
    }

    /**
     * get current time as format
     *
     * @param formatter formatter
     * @return time
     */
    public static String getCurrentDateTimeAsString(DateTimeFormatter formatter) {
        return getCurrentDateTimeAsString(LocalDateTime.now(), formatter);
    }

    /**
     * get current time as format
     *
     * @param localDateTime local date time
     * @param formatter     format
     * @return time
     */
    public static String getCurrentDateTimeAsString(LocalDateTime localDateTime, DateTimeFormatter formatter) {
        return localDateTime.format(formatter);
    }

    /**
     * get current time as format
     *
     * @param timestamp
     * @return time
     */
    public static String getDateTimeAsString(Long timestamp) {
        LocalDateTime time = getDateTimeOfTimestamp(timestamp);
        return getDateTimeAsString(time, TimeConstant.DEFAULT_FORMATTER);
    }

    public static String getDateTimeAsString(Long timestamp, DateTimeFormatter formatter) {
        LocalDateTime time = getDateTimeOfTimestamp(timestamp);
        return getDateTimeAsString(time, formatter);
    }

    public static String getFormatString(Long timestamp, DateTimeFormatter formatter) {
        LocalDateTime time = getDateTimeOfTimestamp(timestamp);
        return getDateTimeAsString(time, formatter);
    }

    /**
     * get current time as format
     *
     * @param localDateTime local date time
     * @return time
     */
    public static String getDateTimeAsString(LocalDateTime localDateTime) {
        return getDateTimeAsString(localDateTime, TimeConstant.DEFAULT_FORMATTER);
    }

    /**
     * get current time as format
     *
     * @param localDateTime local date time
     * @param format        format
     * @return time
     */
    public static String getDateTimeAsString(LocalDateTime localDateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return getDateTimeAsString(localDateTime, formatter);
    }

    /**
     * get current time as format
     *
     * @param localDateTime local date time
     * @param formatter     format
     * @return time
     */
    public static String getDateTimeAsString(LocalDateTime localDateTime, DateTimeFormatter formatter) {
        return localDateTime.format(formatter);
    }

    /**
     * get current local date time
     *
     * @return
     */
    public static LocalDateTime getCurrnetDateTime() {
        return LocalDateTime.now();
    }

    /**
     * get local date time of timestamp
     *
     * @param timestamp
     * @return time
     */
    public static LocalDateTime getDateTimeOfTimestamp(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * get timestamp of local date time
     *
     * @param localDateTime local date time
     * @return time
     */
    public static long getTimestampOfDateTime(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * format string
     *
     * @param time input string time
     * @return LocalDateTime
     */
    public static LocalDateTime parseStringToDateTime(String time) {
        return parseStringToDateTime(time, TimeConstant.DEFAULT_FORMATTER);
    }

    public static String formatStr(String time, DateTimeFormatter fromFormatter, DateTimeFormatter toFormatter) {
        return getCurrentDateTimeAsString(parseStringToDateTime(time, fromFormatter), toFormatter);
    }

    /**
     * format string
     *
     * @param time   input string time
     * @param format input format
     * @return LocalDateTime
     */
    public static LocalDateTime parseStringToDateTime(String time, String format) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return parseStringToDateTime(time, df);
    }

    /**
     * format string
     *
     * @param time      input string time
     * @param formatter input formatter
     * @return LocalDateTime
     */
    public static LocalDateTime parseStringToDateTime(String time, DateTimeFormatter formatter) {
        return LocalDateTime.parse(time, formatter);
    }

    /**
     * compare date
     *
     * @param left
     * @param right
     * @return
     */
    public static int compareMinute(long left, long right) {
        return (int) (left / TimeConstant.MS_IN_MINUTE - right / TimeConstant.MS_IN_MINUTE);
    }

    /**
     * 移除秒信息
     *
     * @param time
     * @return
     */
    public static long removeSeconds(long time) {
        return time / TimeConstant.MS_IN_MINUTE * TimeConstant.MS_IN_MINUTE;
    }

    public static boolean isDate(String value, String pattern) {
        try {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            df.parse(value);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    public static LocalDateTime handleEnd(LocalDateTime time) {
        if (Objects.isNull(time)) {
            return null;
        }

        return time.toLocalDate()
                   .atStartOfDay()
                   .plusDays(1)
                   .minusSeconds(1);
    }
}
