package com.smart.adp.application.vo.drive;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.drive.DriverLicenseOCRVOAssembler;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.common.resp.RespResult;
import com.smart.adp.domain.enums.AliCouldOcrErrorEnum;
import com.smart.adp.infrastructure.feign.response.DriverLicenseOCRRsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Description: 驾驶证OCR识别结果对象
 * @Author: rik.ren
 * @Date: 2025/6/19 18:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class DriverLicenseOCRVO {
    /**
     * 姓名（背面识别）
     */
    @Schema(description = "驾驶证背面识别姓名")
    private String nameBack;

    /**
     * 驾驶证号码
     */
    @Schema(description = "驾驶证号")
    private String licenseNumber;

    /**
     * 姓名（正面识别）
     */
    @Schema(description = "驾驶证正面识别姓名")
    private String name;

    /**
     * 档案编号
     */
    @Schema(description = "驾驶证档案编号")
    private String record;

    /**
     * 国籍
     */
    @Schema(description = "持证人国籍")
    private String nationality;

    /**
     * 出生日期（格式：YYYY-MM-DD）
     */
    @Schema(description = "出生日期")
    private String birthDate;

    /**
     * 签发机关
     */
    @Schema(description = "驾驶证签发机关")
    private String issueAuthority;

    /**
     * 准驾车型代号
     */
    @Schema(description = "准驾车型代号")
    private String approvedType;

    /**
     * 性别（男/女）
     */
    @Schema(description = "性别")
    private String sex;

    /**
     * 驾驶证副页号码
     */
    @Schema(description = "驾驶证背面编号")
    private String licenseNumberBack;

    /**
     * 初次领证日期
     */
    @Schema(description = "初次领证日期")
    private String initialIssueDate;

    /**
     * 住址
     */
    @Schema(description = "持证人住址")
    private String address;

    /**
     * 档案编号
     */
    @Schema(description = "档案编号")
    private String recordNumber;


    public static DriverLicenseOCRVO driveLicenseConvent(RespResult<List<DriverLicenseOCRRsp>> source) {
        log.info("调用中台OCR识别驾驶证结果：{}", JSONObject.toJSONString(source));
        if (ObjectUtil.isEmpty(source) || CollectionUtil.isEmpty(source.getResult())) {
            return null;
        }
        if (!RespCode.OK.getCode().equals(source.getCode())) {
            log.error("调用中台OCR识别驾驶证错误，错误信息：{}", JSONObject.toJSONString(source));
            throw new BusinessException(RespCode.FAIL.getCode(), "请重新上传识别图片");
        }

        //识别失败错误码，成功不会返回此字段
        if (StrUtil.isNotBlank(source.getResult().get(0).getCode())) {
            if (AliCouldOcrErrorEnum.UNMATCHED_IMAGE_TYPE.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "请上传正确驾驶证！");
            } else if (AliCouldOcrErrorEnum.ILLEGAL_IMAGE_URL.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "请上传正确的驾驶证图片!");
            } else if (AliCouldOcrErrorEnum.EXCEEDED_IMAGE_CONTENT.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "证件图片最大支持10M!");
            } else if (AliCouldOcrErrorEnum.ILLEGAL_IMAGE_CONTENT.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "该图片无法识别，请手动填写!");
            } else if (AliCouldOcrErrorEnum.UNSUPPORTED_IMAGE_FORMAT.getCode().equals(source.getResult().get(0).getCode())) {
                throw new BusinessException(RespCode.FAIL.getCode(), "该图片无法识别，请手动填写!");
            }
        }

        return AssemblerFactory.getInstance().convert
                (DriverLicenseOCRVOAssembler.class, source.getResult().get(0), DriverLicenseOCRVO.class);
    }
}

