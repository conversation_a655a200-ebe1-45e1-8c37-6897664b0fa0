package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestDriveMethodEnum;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.helper.StringHelper;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 保存试乘试驾单
 * @Author: rik.ren
 * @Date: 2025/5/20 11:08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateTestDriverSheetDTO {

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名", required = true)
    @NotBlank(message = "客户姓名不能为空")
    private String customerName;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话", required = true)
    @NotBlank(message = "客户电话不能为空")
    private String customerPhone;

    /**
     * 客户性别编码
     */
    @Schema(description = "客户性别编码", required = true)
    @NotBlank(message = "客户性别编码不能为空")
    private String customerSexCode;

    /**
     * 客户性别名称
     */
    @Schema(description = "客户性别名称", required = true)
    @NotBlank(message = "客户性别名称不能为空")
    private String customerSexName;

    /**
     * 试驾类型
     */
    @Schema(description = "试驾类型", required = true)
    @NotBlank(message = "试驾类型不能为空")
    private String testType;

    /**
     * 试驾类型名称
     */
    @Schema(description = "试驾类型名称", required = true)
    @NotBlank(message = "试驾类型名称不能为空")
    private String testTypeName;

    /**
     * 车架号
     */
    @Schema(description = "车架号", required = true)
    @NotBlank(message = "车架号不能为空")
    private String carVin;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号", required = true)
    @NotBlank(message = "车牌号不能为空")
    private String plateNumber;

    /**
     * 小型车类型编码
     */
    @Schema(description = "小型车类型编码", required = true)
    @NotBlank(message = "小型车类型编码不能为空")
    private String smallCarTypeCode;

    /**
     * 小型车类型名称
     */
    @Schema(description = "小型车类型名称", required = true)
    @NotBlank(message = "小型车类型名称不能为空")
    private String smallCarTypeName;

    /**
     * 普通试乘试驾预约日期
     */
    @Schema(description = "普通试乘试驾预约日期")
    private String appointmentTestDate;

    /**
     * 普通试乘试驾预约时间
     */
    @Schema(description = "普通试乘试驾预约时间")
    private String appointmentTestTime;

    /**
     * 深度试驾开始时间
     */
    @Schema(description = "深度试驾开始时间")
    private String appointmentStartTime;

    /**
     * 深度试驾结束时间
     */
    @Schema(description = "深度试驾结束时间")
    private String appointmentEndTime;

    /**
     * 意向等级编码
     */
    @Schema(description = "意向等级编码", required = true)
    @NotBlank(message = "意向等级编码不能为空")
    private String intenLevelCode;

    /**
     * 意向等级名称
     */
    @Schema(description = "意向等级名称", required = true)
    @NotBlank(message = "意向等级名称不能为空")
    private String intenLevelName;

    /**
     * 业务热度编码
     */
    @Schema(description = "业务热度编码HWC", required = true)
    @NotBlank(message = "业务热度编码HWC不能为空")
    private String businessHeatCode;

    /**
     * 业务热度名称
     */
    @Schema(description = "业务热度名称HWC", required = true)
    @NotBlank(message = "业务热度名称HWC不能为空")
    private String businessHeatName;

    /**
     * 经销商线索订单号
     */
    @Schema(description = "经销商线索订单号", required = false)
    private String dlrClueOrderNo;

    /**
     * 预约ID
     */
    @Schema(description = "预约ID", required = false)
    private String appointmentId;

    /**
     * 计划购买日期
     */
    @Schema(description = "计划购买日期", required = false)
    private String planBuyDate;

    /**
     * 计划购买日期名称
     */
    @Schema(description = "计划购买日期名称", required = false)
    private String planBuyDateName;

    /**
     * 定金
     */
    @Schema(description = "定金")
    private BigDecimal deposit;

    /**
     * 销售顾问ID
     */
    @Schema(description = "销售顾问ID")
    private String salesConsultantId;

    /**
     * 销售顾问姓名
     */
    @Schema(description = "销售顾问姓名")
    private String salesConsultantName;

    /**
     * 使用场景
     */
    @Schema(description = "发送消息的使用场景")
    private String scenario = "3";

    /**
     * 消息类型
     */
    @Schema(description = "消息类型")
    private String messageType = "3";

    /**
     * 是否可修改
     */
    @Schema(description = "是否可修改")
    private String isCanChange = "1";

    /**
     * 旧试驾单ID
     */
    @Schema(description = "旧试驾单ID")
    private String oldTestDriveSheetId;

    /**
     * 路线名称
     */
    @Schema(description = "路线名称", required = true)
    private String routeTypeName;

    /**
     * 试驾路线ID
     */
    @Schema(description = "试驾路线ID", required = true)
    private String testcarRouteId;

    /**
     * 预计开始日期
     */
    @Schema(description = "预计到店日期")
    private String expectStartDate;

    /**
     * 试驾方式
     *
     * @see TestDriveMethodEnum
     */
    @Schema(description = "试驾方式", required = true)
    @NotBlank(message = "试驾方式不能为空")
    private String testDriveMethod;

    /**
     * 门店code，从token中解析获取
     */
    @Schema(description = "门店code", hidden = true)
    private String dlrCode;

    /**
     * 门店名称，从token中解析获取
     */
    @Schema(description = "门店名称", hidden = true)
    private String dlrName;

    /**
     * 门店Id，从token中解析获取
     */
    @Schema(description = "门店Id", hidden = true)
    private String dlrId;
    /**
     * 跟进人id，从token中解析获取
     */
    @Schema(description = "跟进人id", hidden = true)
    private String reviewPersonId;
    /**
     * 跟进人name，从token中解析获取
     */
    @Schema(description = "跟进人name", hidden = true)
    private String reviewPersonName;
    /**
     * 更新标记，true是更新，false是新建
     */
    @Schema(description = "更新标记，true是更新，false是新建")
    private Boolean updateFlag = false;
    /**
     * 试乘试驾单状态
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "试乘试驾单状态", hidden = true)
    private String testStatus = "0";
    /**
     * 试乘试驾单主键ID
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "试乘试驾单主键ID", hidden = true)
    private String testDriveSheetId = StringHelper.GetGUID();
    /**
     * 试驾任务ID，如果是从试驾任务列表创建试驾单，那此字段必传
     */
    @Schema(description = "试驾任务ID")
    private String testDriveTaskId;
    /**
     * 试驾任务乐观锁ID，如果是从试驾任务列表创建试驾单，那此字段必传
     */
    @Schema(description = "试驾任务乐观锁ID")
    private String taskUpdateControlId;


    /**
     * 控制id
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "控制id", hidden = true)
    private String updateControlId = StringHelper.GetGUID();

    public void setSalesConsultantName(String salesConsultantName) {
        if (StringUtils.isEmpty(this.salesConsultantName)) {
            this.salesConsultantName = UserInfoContext.get().getEmpName();
        }
    }

    public String getSalesConsultantName() {
        if (StringUtils.isEmpty(this.salesConsultantName)) {
            this.salesConsultantName = UserInfoContext.get().getEmpName();
        }
        return salesConsultantName;
    }

    public void setSalesConsultantId(String salesConsultantId) {
        if (StringUtils.isEmpty(this.salesConsultantId)) {
            this.salesConsultantId = UserInfoContext.get().getUserID();
        }
    }

    public String getSalesConsultantId() {
        if (StringUtils.isEmpty(this.salesConsultantId)) {
            this.salesConsultantId = UserInfoContext.get().getUserID();
        }
        return salesConsultantId;
    }

    public String getDlrCode() {
        if (StringUtils.isEmpty(this.dlrCode)) {
            this.dlrCode = UserInfoContext.get().getDlrCode();
        }
        return dlrCode;
    }

    public String getDlrName() {
        if (StringUtils.isEmpty(this.dlrName)) {
            this.dlrName = UserInfoContext.get().getDlrName();
        }
        return dlrName;
    }

    public String getDlrId() {
        if (StringUtils.isEmpty(this.dlrId)) {
            this.dlrId = UserInfoContext.get().getDlrID();
        }
        return dlrId;
    }

    public String getReviewPersonId() {
        if (StringUtils.isEmpty(this.reviewPersonId)) {
            this.reviewPersonId = UserInfoContext.get().getUserID();
        }
        return reviewPersonId;
    }

    public String getReviewPersonName() {
        if (StringUtils.isEmpty(this.reviewPersonName)) {
            this.reviewPersonName = UserInfoContext.get().getUserName();
        }
        return reviewPersonName;
    }

    public SacTestDriveSheetBO buildSacTestDriveSheetBO(String configTestDriveSwitchVal, String configDriveNumLimitVal) {
        SacTestDriveSheetBO bo = new SacTestDriveSheetBO();
        bo.setTestDriveSheetId(testDriveSheetId);
        bo.setCustomerName(customerName);
        bo.setCustomerPhone(customerPhone);
        bo.setCustomerSex(customerSexCode);
        bo.setCustomerSexName(customerSexName);
        bo.setTestStatus(testStatus);
        bo.setTestType(testType);
        bo.setCarVin(carVin);
        bo.setPlateNumber(plateNumber);
        bo.setSmallCarTypeCode(smallCarTypeCode);
        bo.setSmallCarTypeName(smallCarTypeName);
        bo.setAppointmentTestDate(appointmentTestDate);
        bo.setAppointmentTestTime(appointmentTestTime);
        bo.setAppointmentStartTime(appointmentStartTime);
        bo.setAppointmentEndTime(appointmentEndTime);
        bo.setAppointmentChannel("0");
        bo.setIntenLevelCode(intenLevelCode);
        bo.setIntenLevelName(intenLevelName);
        bo.setDlrClueOrderNo(dlrClueOrderNo);
        bo.setAppointmentId(appointmentId);
        bo.setDeposit(deposit);
        bo.setSalesConsultantId(salesConsultantId);
        bo.setSalesConsultantName(salesConsultantName);
        bo.setIsCanChange(isCanChange);
        bo.setOldTestDriveSheetId(oldTestDriveSheetId);
        bo.setTestDriveMethod(testDriveMethod);
        bo.setDlrCode(getDlrCode());
        bo.setDlrName(getDlrName());
        bo.setOemId(UserInfoContext.get().getOemID());
        bo.setGroupId(UserInfoContext.get().getGroupID());
        bo.setTestDriveOrderNo("");
        bo.setCreator(UserInfoContext.get().getUserID());
        bo.setCreatedName(UserInfoContext.get().getEmpName());
        bo.setCreatedDate(LocalDateTime.now());
        bo.setLastUpdatedDate(bo.getCreatedDate());
        bo.setUpdateControlId(updateControlId);
        bo.setBusinessHeatCode(businessHeatCode);
        bo.setBusinessHeatName(businessHeatName);
        bo.setColumn8("agent_nature");
        bo.setColumn9("agent_nature");
        bo.setColumn3(testcarRouteId);
        bo.setColumn4(routeTypeName);
        bo.setConfigValueSwitch(configTestDriveSwitchVal);
        bo.setConfigValueDriveNum(configDriveNumLimitVal);
        bo.setIsSendMessage("1");
        bo.setTestRoadHaul(BigDecimal.ZERO);
        bo.setTestStartRoadHaul(BigDecimal.ZERO);
        return bo;
    }
}
