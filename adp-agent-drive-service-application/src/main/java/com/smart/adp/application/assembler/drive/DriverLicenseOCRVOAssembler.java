package com.smart.adp.application.assembler.drive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.drive.DriverLicenseOCRVO;
import com.smart.adp.infrastructure.feign.response.DriverLicenseOCRRsp;

/**
 * @Description: 驾驶证OCR识别结果对象装配
 * @Author: rik.ren
 * @Date: 2025/5/18 14:30
 **/
public class DriverLicenseOCRVOAssembler implements Assembler<DriverLicenseOCRRsp, DriverLicenseOCRVO> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public DriverLicenseOCRVO assemble(DriverLicenseOCRRsp source, Class<DriverLicenseOCRVO> target) {
        if (ObjectUtil.isEmpty(source)) {
            return null;
        }
        DriverLicenseOCRVO driverLicenseOCRVO = new DriverLicenseOCRVO();
        driverLicenseOCRVO.setNameBack(source.getNameBack());
        driverLicenseOCRVO.setLicenseNumber(source.getLicenseNumber());
        driverLicenseOCRVO.setName(source.getName());
        driverLicenseOCRVO.setRecord(source.getRecord());
        driverLicenseOCRVO.setNationality(source.getNationality());
        driverLicenseOCRVO.setBirthDate(source.getBirthDate());
        driverLicenseOCRVO.setIssueAuthority(source.getIssueAuthority());
        driverLicenseOCRVO.setApprovedType(source.getApprovedType());
        driverLicenseOCRVO.setSex(source.getSex());
        driverLicenseOCRVO.setLicenseNumberBack(source.getLicenseNumberBack());
        driverLicenseOCRVO.setInitialIssueDate(source.getInitialIssueDate());
        driverLicenseOCRVO.setAddress(source.getAddress());
        driverLicenseOCRVO.setRecordNumber(source.getRecordNumber());
        return driverLicenseOCRVO;
    }
}
