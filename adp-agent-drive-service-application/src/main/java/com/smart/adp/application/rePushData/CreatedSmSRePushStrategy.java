package com.smart.adp.application.rePushData;

import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 创建试驾单后重推SMS
 * @Author: rik.ren
 * @Date: 2025/7/9 18:11
 **/
@Slf4j
@Component
public class CreatedSmSRePushStrategy implements RePushTestDataStrategy {
    @Autowired
    private TestDriveAggregate testDriveAggregate;

    /**
     * 重推
     *
     * @param param
     * @return
     */
    @Override
    public Boolean rePush(Object param) {
        log.info("开始重推创建试驾单的SMS通知");
        SacTestDriveSheetBO sheetBO = (SacTestDriveSheetBO) param;
        sheetBO.setIsSendMessage("1");
        testDriveAggregate.sendMessage(sheetBO);
        return Boolean.TRUE;
    }
}
