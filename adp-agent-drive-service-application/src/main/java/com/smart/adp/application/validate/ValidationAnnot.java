package com.smart.adp.application.validate;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @Description: 自定义数据校验的注解
 * @Author: rik.ren
 * @Date: 2025/3/5 18:24
 **/
@Documented
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValueExistValidation.class)
public @interface ValidationAnnot {

    /**
     * 校验标记的名称，随便定义只要不重复，名称没有业务含义
     * @return
     */
    String checkMarkName() default "";

    /**
     * 提示的错误信息
     *
     * @return
     */
    String message() default "数据不存在";

    /**
     * 分组
     *
     * @return
     */
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
