package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.utils.UserUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 查询试乘试驾单入参
 * @Author: rik.ren
 * @Date: 2025/5/15 16:03
 **/
@Data
public class QueryTestDriverSheetInfoDTO {

    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id")
    private String testDriveSheetId;

    /**
     * 预约单id
     *
     * @see TestDriveTypeEnum
     */
    @Schema(description = "预约单id")
    private String appointmentId;

    /**
     * 线索id
     */
    @Schema(description = "线索id")
    private String customerId;

    /**
     * 线索手机号
     */
    @Schema(description = "线索手机号")
    private String custPhone;

    /**
     * 门店code，从token中解析获取
     */
    @Schema(description = "门店Code", hidden = true)
    private String dlrCode;

    /**
     * 产品专家id，从token中解析获取
     */
    @Schema(description = "产品专家id", hidden = true)
    private String salesConsultantId;


    public void setDlrCode(String dlrCode) {
        if (StringUtils.isEmpty(this.dlrCode)) {
            this.dlrCode = UserInfoContext.get().getDlrCode();
        }
    }

//    public String getDlrCode() {
//        if (StringUtils.isEmpty(this.dlrCode)) {
//            this.dlrCode = UserInfoContext.get().getDlrCode();
//        }
//        return dlrCode;
//    }

    public void setSalesConsultantId(String salesConsultantId) {
        if (StringUtils.isEmpty(this.salesConsultantId)) {
            this.salesConsultantId = UserInfoContext.get().getUserID();
        }
    }

    public String getSalesConsultantId() {
        if (StringUtils.isEmpty(this.salesConsultantId)) {
            this.salesConsultantId = UserInfoContext.get().getUserID();
        }
        return salesConsultantId;
    }

    /**
     * 构建查询试驾单详情的试驾单bo
     * @return
     */
    public SacTestDriveSheetBO buildSacTestDriveSheetBO() {
        SacTestDriveSheetBO bo = new SacTestDriveSheetBO();
        bo.setDlrCode(this.getDlrCode());
        // 判断是否产品专家
        if (UserUtil.isProductExpert(UserInfoContext.get().getStationId())) {
            bo.setSalesConsultantId(this.getSalesConsultantId());
        }
        bo.setTestDriveSheetId(this.getTestDriveSheetId());
        bo.setAppointmentId(this.getAppointmentId());
        bo.setCustomerId(this.getCustomerId());
        bo.setCustomerPhone(this.getCustPhone());
        return bo;
    }
}
