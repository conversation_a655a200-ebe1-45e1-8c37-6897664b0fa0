package com.smart.adp.application.service.drive;

import com.smart.adp.application.dto.drive.AllocationTestDriveTaskDTO;
import com.smart.adp.application.dto.drive.CancelTestDriveTaskDTO;
import com.smart.adp.application.dto.drive.QueryTestDriverTaskDTO;
import com.smart.adp.application.dto.drive.SaveTestDriveTaskDTO;
import com.smart.adp.application.vo.drive.QueryTestDriveTaskRspVO;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description: 试驾任务server
 * @Author: rik.ren
 * @Date: 2025/7/16 14:06
 **/
public interface TestDriveTaskService {
    /**
     * 分页查询试驾任务列表
     *
     * @param param
     * @return
     */
   // PageVO<QueryTestDriveTaskRspVO> queryTestDriveTaskPage(QueryTestDriverTaskDTO param);

    /**
     * 分页查询试驾任务个数
     *
     * @param param
     * @return
     */
   // Long queryTestDriveTaskCount(QueryTestDriverTaskDTO param);

    /**
     * 创建试驾任务
     *
     * @param param
     * @return
     */
    Boolean saveTestDriverTask(SaveTestDriveTaskDTO param);

    /**
     * 店长分配试驾任务给到产品专家
     *
     * @param param
     * @return
     */
    Boolean allocationTestDriverTask(AllocationTestDriveTaskDTO param);

    /**
     * 更新试驾任务
     *
     * @param taskId
     * @param oldUpdateControlId
     * @param sheetBO
     * @return
     */
    Boolean modifyTestDriveTask(String taskId, String oldUpdateControlId, SacTestDriveSheetBO sheetBO);

    /**
     * 取消试驾任务
     *
     * @param param
     * @return
     */
    Boolean cancelTestDriverTask(CancelTestDriveTaskDTO param);
}
