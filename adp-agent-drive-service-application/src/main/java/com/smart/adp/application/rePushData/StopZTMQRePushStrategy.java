package com.smart.adp.application.rePushData;

import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 结束试驾重推ZTMQ，交换机是smart.ex.adp.driveEnd
 * @Author: rik.ren
 * @Date: 2025/7/9 18:12
 **/
@Slf4j
@Component
public class StopZTMQRePushStrategy implements RePushTestDataStrategy {

    @Autowired
    private TestDriveAggregate testDriveAggregate;

    /**
     * 重推
     *
     * @param param
     * @return
     */
    @Override
    public Boolean rePush(Object param) {
        log.info("开始重推试驾结束的ZTMQ");
        SacTestDriveSheetBO sheetBO = (SacTestDriveSheetBO) param;
        SacTestDriveSheetBO getTestDriveSheetResult =
                testDriveAggregate.getTestDriveSheet(new SacTestDriveSheetBO(sheetBO.getTestDriveSheetId(),
                Boolean.TRUE));
        Boolean ztmqResult = testDriveAggregate.sendZTMQ(getTestDriveSheetResult);
        return ztmqResult;
    }
}
