package com.smart.adp.application.vo.drive;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.smart.adp.domain.enums.TestDriveMethodEnum;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.infrastructure.utils.AESUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 分页查询试乘试驾单VO
 * @Author: rik.ren
 * @Date: 2025/5/14 17:06
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Slf4j
public class QueryTestDriveSheetInfoRspVO {

    /**
     * 试乘试驾单id
     */
    @Schema(description = "试乘试驾单id")
    private String testDriveSheetId;

    /**
     * 预约单id
     */
    @Schema(description = "预约单id")
    private String appointmentId;

    /**
     * 线索单号
     */
    @Schema(description = "线索单号")
    private String dlrClueOrderNo;

    /**
     * 试乘试驾单号
     */
    @Schema(description = "试乘试驾单号")
    private String testDriveOrderNo;

    /**
     * vin
     */
    @Schema(description = "vin")
    private String carVin;

    /**
     * 试驾门店
     */
    @Schema(description = "试驾门店code")
    private String testDlrCode;

    /**
     * 试驾门店名称
     */
    @Schema(description = "试驾门店名称")
    private String testDlrName;

    /**
     * 原试驾单id，多个逗号分割
     */
    @Schema(description = "原试驾单id，多个逗号分割")
    private String oldTestDriveSheetId;

    /**
     * 试乘试驾开始里程
     */
    @Schema(description = "试乘试驾开始里程")
    private BigDecimal testStartRoadHaul;

    /**
     * 试乘试驾结束里程
     */
    @Schema(description = "试乘试驾结束里程")
    private BigDecimal testEndRoadHaul;

    /**
     * 试驾车上次结束试驾单时的总里程数
     */
    @Schema(description = "试驾车上次结束试驾单时的总里程数")
    private BigDecimal lastEndRoadHaul;

    /**
     * 试驾路线
     */
    @Schema(description = "试驾路线名称")
    private String routeTypeName;

    /**
     * 试驾路线
     */
    @Schema(description = "试驾路线id")
    private String testcarRouteId;

    /**
     * 试乘试驾状态
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "试乘试驾状态")
    private String testStatus;

    /**
     * 所属专营店名称
     */
    @Schema(description = "所属专营店名称")
    private String dlrName;

    /**
     * 所属专营店code
     */
    @Schema(description = "所属专营店code")
    private String dlrCode;

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名")
    private String customerName;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话")
    private String customerPhone;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话")
    private String customerPhoneTm;

    /**
     * 性别
     */
    @Schema(description = "性别")
    private String customerSex;

    /**
     * 试乘试驾车型编码
     */
    @Schema(description = "试乘试驾车型编码")
    private String smallCarTypeCode;

    /**
     * 试乘试驾车型名称
     */
    @Schema(description = "试乘试驾车型名称")
    private String smallCarTypeName;

    /**
     * 试驾车牌
     */
    @Schema(description = "试驾车牌")
    private String plateNumber;

    /**
     * 试乘试驾类型(0：试乘，：试驾，2：深度试驾)
     *
     * @see TestDriveTypeEnum
     */
    @Schema(description = "试乘试驾类型(0：试乘，：试驾，2：深度试驾)")
    private String testType;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private LocalDateTime createdDate;

    /**
     * 预约试乘试驾日期(普通试乘试驾)
     */
    @Schema(description = "预约试乘试驾日期(普通试乘试驾)")
    private String appointmentTestDate;

    /**
     * 预约试乘试驾时间段(普通试乘试驾)
     */
    @Schema(description = "预约试乘试驾时间段(普通试乘试驾)")
    private String appointmentTestTime;

    /**
     * 预约超长试驾开始时间
     */
    @Schema(description = "预约超长试驾开始时间")
    private String appointmentStartTime;

    /**
     * 预约超长试驾结束时间
     */
    @Schema(description = "预约超长试驾结束时间")
    private String appointmentEndTime;

    /**
     * 试驾车所属门店
     */
    @Schema(description = "试驾车所属门店")
    private String testCarBelongStore;

    /**
     * 所属门店
     */
    @Schema(description = "所属门店")
    private String testCarBelongStoreCode;

    /**
     * 线索门店和试驾单门店一致
     */
    @Schema(description = "线索门店和试驾单门店一致")
    private Boolean isThisDlr = Boolean.FALSE;

    /**
     * 驾驶证附件
     */
    @Schema(description = "驾驶证附件")
    private String drivingLicencePhoto;

    /**
     * 顾客身份证附件
     */
    @Schema(description = "顾客身份证附件")
    private String customerIdNumberAgreement;

    /**
     * 试驾协议
     */
    @Schema(description = "试驾协议")
    private String testDriveAgreement;

    /**
     * 试驾协议PDF
     */
    @Schema(description = "试驾协议PDF")
    private String testDriveAgreementPDF;

    /**
     * 其他附件
     */
    @Schema(description = "其他附件")
    private String otherAgreement;

    /**
     * 是否可以更换门店
     */
    @Schema(description = "是否可以更换门店")
    private String isCanChange;

    /**
     * 顾客签名附件
     */
    @Schema(description = "顾客签名附件")
    private String customerSignatureAgreement;

    /**
     * 并发控制ID
     */
    @Schema(description = "并发控制ID")
    private String updateControlSheetId;

    /**
     * 试乘试驾单跟进内容
     */
    @Schema(description = "试乘试驾单跟进内容")
    private String testDriveDesc;

    /**
     * 试驾小结
     */
    @Schema(description = "试驾小结")
    private String testDriveEndRemark;

    /**
     * 试驾方式1上门，2到店
     *
     * @see TestDriveMethodEnum
     */
    @Schema(description = "试驾方式1上门，2到店")
    private String testMethod;

    /**
     * OCR识别后真实姓名
     */
    @Schema(description = "OCR识别后真实姓名")
    private String realName;

    /**
     * OCR识别后证件号
     */
    @Schema(description = "OCR识别后证件号")
    @JsonProperty("IDNumber")
    private String IDNumber;

    @SneakyThrows
    public QueryTestDriveSheetInfoRspVO buildQueryTestDriveSheetInfoRspVO(TestDriveAggregate.QueryTestDriveInfoResult queryTestDriveInfoResult) {
        if (ObjectUtil.isEmpty(queryTestDriveInfoResult) || ObjectUtil.isEmpty(queryTestDriveInfoResult.getSheets())) {
            return null;
        }
        QueryTestDriveSheetInfoRspVO queryTestDriveSheetInfoRspVO = QueryTestDriveSheetInfoRspVO.builder()
                .testDriveSheetId(queryTestDriveInfoResult.getSheets().getTestDriveSheetId())
                .appointmentId(queryTestDriveInfoResult.getSheets().getAppointmentId())
                .dlrClueOrderNo(queryTestDriveInfoResult.getSheets().getDlrClueOrderNo())
                .testDriveOrderNo(queryTestDriveInfoResult.getSheets().getTestDriveOrderNo())
                .carVin(queryTestDriveInfoResult.getSheets().getCarVin())
                .testDlrCode(queryTestDriveInfoResult.getSheets().getDlrCode())
                .testDlrName(queryTestDriveInfoResult.getSheets().getDlrName())
                .oldTestDriveSheetId(queryTestDriveInfoResult.getSheets().getOldTestDriveSheetId())
                .testStartRoadHaul(queryTestDriveInfoResult.getSheets().getTestStartRoadHaul())
                .testEndRoadHaul(queryTestDriveInfoResult.getSheets().getTestEndRoadHaul())
                .routeTypeName(queryTestDriveInfoResult.getSheets().getColumn4())
                .testcarRouteId(queryTestDriveInfoResult.getSheets().getColumn3())
                .testStatus(queryTestDriveInfoResult.getSheets().getTestStatus())
                .dlrName(queryTestDriveInfoResult.getSheets().getDlrName())
                .dlrCode(queryTestDriveInfoResult.getSheets().getDlrCode())
                .customerName(queryTestDriveInfoResult.getSheets().getCustomerName())
                .customerId(queryTestDriveInfoResult.getSheets().getCustomerId())
                .customerPhone(queryTestDriveInfoResult.getSheets().getCustomerPhone())
                .customerPhoneTm(maskHighPerformance(queryTestDriveInfoResult.getSheets().getCustomerPhone()))
                .customerSex(queryTestDriveInfoResult.getSheets().getCustomerSex())
                .smallCarTypeCode(queryTestDriveInfoResult.getSheets().getSmallCarTypeCode())
                .smallCarTypeName(queryTestDriveInfoResult.getSheets().getSmallCarTypeName())
                .plateNumber(queryTestDriveInfoResult.getSheets().getPlateNumber())
                .testType(queryTestDriveInfoResult.getSheets().getTestType())
                .createdDate(queryTestDriveInfoResult.getSheets().getCreatedDate())
                .appointmentTestDate(queryTestDriveInfoResult.getSheets().getAppointmentTestDate())
                .appointmentTestTime(queryTestDriveInfoResult.getSheets().getAppointmentTestTime())
                .appointmentStartTime(queryTestDriveInfoResult.getSheets().getAppointmentStartTime())
                .appointmentEndTime(queryTestDriveInfoResult.getSheets().getAppointmentEndTime())
                .drivingLicencePhoto(queryTestDriveInfoResult.getSheets().getDrivingLicencePhoto())
                .customerIdNumberAgreement(queryTestDriveInfoResult.getSheets().getCustomerIdNumberAgreement())
                .otherAgreement(queryTestDriveInfoResult.getSheets().getOtherAgreement())
                .testDriveAgreement(queryTestDriveInfoResult.getSheets().getTestDriveAgreement())
                .testDriveAgreementPDF(queryTestDriveInfoResult.getSheets().getTestDriveAgreementPDF())
                .isCanChange(queryTestDriveInfoResult.getSheets().getIsCanChange())
                .customerSignatureAgreement(queryTestDriveInfoResult.getSheets().getCustomerSignatureAgreement())
                .updateControlSheetId(queryTestDriveInfoResult.getSheets().getUpdateControlId())
                .testDriveDesc(ObjectUtil.isEmpty(queryTestDriveInfoResult.getReviewRecordBO()) ? null :
                        queryTestDriveInfoResult.getReviewRecordBO().getTestDriveDesc())
                .testDriveEndRemark(queryTestDriveInfoResult.getSheets().getTestDriveEndRemark())
                .testMethod(queryTestDriveInfoResult.getSheets().getTestDriveMethod())
                .testCarBelongStore(ObjectUtil.isEmpty(queryTestDriveInfoResult.getTestCarPrepare()) ? null :
                        queryTestDriveInfoResult.getTestCarPrepare().getDlrShortName())
                .testCarBelongStoreCode(ObjectUtil.isEmpty(queryTestDriveInfoResult.getTestCarPrepare()) ? null :
                        queryTestDriveInfoResult.getTestCarPrepare().getApplyDlrCode())
                .realName(queryTestDriveInfoResult.getSheets().getRealName())
                .lastEndRoadHaul(queryTestDriveInfoResult.getTestCarPrepare().getTestcarKilometers())
                .build();
        try {
            queryTestDriveSheetInfoRspVO.setIDNumber(StringUtils.isNotEmpty(queryTestDriveInfoResult.getSheets().getCustomerIDNumber()) ?
                    AESUtil.decrypt(queryTestDriveInfoResult.getSheets().getCustomerIDNumber()) : null);
        } catch (Exception e) {
            log.error("身份证解密失败, customerIDNumber:{}", queryTestDriveInfoResult.getSheets().getCustomerIDNumber(), e);
        }
        return queryTestDriveSheetInfoRspVO;
    }

    private static String maskHighPerformance(String phoneNumber) {
        int PHONE_LENGTH = 11;
        char[] REPLACEMENT = {'*', '*', '*', '*'};
        // 快速失败检查
        if (phoneNumber == null || phoneNumber.length() != PHONE_LENGTH) {
            return phoneNumber;
        }

        // 直接操作字符数组
        char[] chars = phoneNumber.toCharArray();
        System.arraycopy(REPLACEMENT, 0, chars, 3, 4);
        return new String(chars);
    }
}
