package com.smart.adp.application.assembler;

import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR> create on 2021/2/20 11:18
 * 装配工厂
 */
public class AssemblerFactory {
    private static AssemblerFactory INSTANCE = new AssemblerFactory();

    /**
     * 装配工厂 指定任何装配过程 执行装配
     *
     * @param assembler 装配
     * @param source    S对象
     * @param target    T对象
     */
    public <S, T> T assemble(Assembler<S, T> assembler, S source, Class<T> target) {
        T assemble = assembler.assemble(source, target);
        return assemble;
    }

    public static AssemblerFactory getInstance() {
        return INSTANCE;
    }

    private Assembler getAssembler(Class type) {
        Assembler assembler = null;
        try {
            assembler = (Assembler) ReflectionUtils.accessibleConstructor(type, new Class[0]).newInstance(new Object[0]);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return assembler;
    }

    /**
     * @param type
     * @param source
     * @param targetType
     * @param <S>
     * @param <T>
     * @return
     */
    public <S, T> T convert(Class type, S source, Class<T> targetType) {
        Assembler assembler = getAssembler(type);
        T assemblerResult = (T) assemble(assembler, source, targetType);
        return assemblerResult;
    }
}
