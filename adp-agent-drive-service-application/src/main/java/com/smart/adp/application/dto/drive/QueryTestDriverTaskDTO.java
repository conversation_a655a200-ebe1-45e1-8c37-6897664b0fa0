package com.smart.adp.application.dto.drive;

import com.smart.adp.application.common.PageDTO;
import com.smart.adp.domain.enums.TestDriveTaskStatusEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 查询试驾任务入参
 * @Author: rik.ren
 * @Date: 2025/7/16 13:33
 **/
@Data
public class QueryTestDriverTaskDTO extends PageDTO {

    /**
     * 门店code，从token中解析获取
     */
    @Schema(description = "门店code", required = true)
    private String taskPersonDlrCode;

    /**
     * 产品专家id
     */
    @Schema(description = "产品专家id", required = true)
    private String taskPersonId;

    /**
     * 0未完成，1已完成
     *
     * @see TestDriveTaskStatusEnum
     */
    @Schema(description = "0未完成，1已完成，2 取消", required = true)
    private String taskStatusCode = "0";

    /**
     * 试驾任务id
     */
    @Schema(description = "试驾任务id")
    private String testDriveTaskId;

    public SacTestDriveTaskBO buildSacTestDriveTaskBO() {
        SacTestDriveTaskBO bo = new SacTestDriveTaskBO();
        bo.setTaskPersonDlrCode(this.getTaskPersonDlrCode());
        bo.setTaskPersonId(this.getTaskPersonId());
        bo.setTaskStateCode(getTaskStatusCode());
        bo.setPageNumber(this.getPageIndex());
        bo.setPageSize(this.getPageSize());
        return bo;
    }
}
