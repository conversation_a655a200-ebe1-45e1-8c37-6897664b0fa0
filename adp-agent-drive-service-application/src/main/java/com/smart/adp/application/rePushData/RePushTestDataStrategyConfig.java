package com.smart.adp.application.rePushData;

import com.smart.adp.domain.enums.RePushTestDriveTypeEnum;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import com.smart.adp.domain.utils.ApplicationContextHolder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: spring启动时加载重推数据策略到map中
 * @Author: rik.ren
 * @Date: 2025/3/9 11:08
 **/
@Configuration
public class RePushTestDataStrategyConfig {

    @Bean
    @DependsOn("applicationContextHolder")
    public Map<Integer, RePushTestDataStrategy> rePushTestDataStrategyMap() {
        Map<Integer, RePushTestDataStrategy> map = new HashMap<>();
        map.put(RePushTestDriveTypeEnum.SEND_CREATED_SMS.getCode(), ApplicationContextHolder.getBean(CreatedSmSRePushStrategy.class));
        map.put(RePushTestDriveTypeEnum.SEND_START_CDP.getCode(), ApplicationContextHolder.getBean(StartCDPRePushStrategy.class));
        map.put(RePushTestDriveTypeEnum.SEND_START_INTENTIONCAR.getCode(),
                ApplicationContextHolder.getBean(StartIntentionCarRePushStrategy.class));
        map.put(RePushTestDriveTypeEnum.SEND_STOP_BI.getCode(), ApplicationContextHolder.getBean(StopBIRePushStrategy.class));
        map.put(RePushTestDriveTypeEnum.SEND_STOP_ZTMQ.getCode(),
                ApplicationContextHolder.getBean(StopZTMQRePushStrategy.class));
        map.put(RePushTestDriveTypeEnum.SEND_STOP_TDA.getCode(), ApplicationContextHolder.getBean(StopTDARePushStrategy.class));
        return map;
    }
}