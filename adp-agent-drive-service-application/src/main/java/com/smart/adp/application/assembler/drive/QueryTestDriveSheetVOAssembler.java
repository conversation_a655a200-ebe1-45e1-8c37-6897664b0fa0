package com.smart.adp.application.assembler.drive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.AssemblerMoreSource;
import com.smart.adp.application.vo.drive.QueryTestDriveSheetRspVO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.infrastructure.feign.response.ClueEventFlowRsp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 查询试乘试驾单对象转换
 * @Author: rik.ren
 * @Date: 2025/5/18 14:30
 **/
public class QueryTestDriveSheetVOAssembler implements AssemblerMoreSource<PageVO> {

    /**
     * 从多个源对象装配到目标对象
     *
     * @param targetType 目标类型
     * @param sources    可变长度的源对象
     * @return 装配后的目标对象
     */
    @Override
    public PageVO<QueryTestDriveSheetRspVO> assemble(Class<PageVO> targetType, Object... sources) {
        if (ObjectUtil.isEmpty(sources)) {
            return PageVO.<QueryTestDriveSheetRspVO>builder().build();
        }
        PageVO<QueryTestDriveSheetRspVO> vo = new PageVO<>();
        List<QueryTestDriveSheetRspVO> result = new ArrayList<>();
        // 遍历所有源对象，提取字段
        for (Object source : sources) {
            QueryTestDriveSheetRspVO sheetRspVO = new QueryTestDriveSheetRspVO();
            if (ObjectUtil.isNotEmpty(source) && source instanceof SacTestDriveSheetBO) {
                DomainPage<SacTestDriveSheetBO> domainPage = (DomainPage<SacTestDriveSheetBO>) source;
                domainPage.getRecords().forEach(clue -> {
                    sheetRspVO.setTestDriveSheetId(clue.getTestDriveSheetId());
                    sheetRspVO.setAppointmentId(clue.getAppointmentId());
                    sheetRspVO.setTestStatus(clue.getTestStatus());
                    sheetRspVO.setDlrName(clue.getDlrName());
                    sheetRspVO.setCustomerName(clue.getCustomerName());
                    sheetRspVO.setCustomerId(clue.getCustomerId());
                    sheetRspVO.setCustomerPhone(clue.getCustomerPhone());
                    sheetRspVO.setSmallCarTypeCode(clue.getSmallCarTypeCode());
                    sheetRspVO.setSmallCarTypeName(clue.getSmallCarTypeName());
                    sheetRspVO.setPlateNumber(clue.getPlateNumber());
                    sheetRspVO.setTestTypeName(ObjectUtil.isEmpty(clue.getTestType()) ? null :
                            TestDriveTypeEnum.getByCode(clue.getTestType()).getDesc());
                    sheetRspVO.setCreatedDate(clue.getCreatedDate());
                    result.add(sheetRspVO);
                });
                vo.setRecords(result);
                vo.setTotalRow(domainPage.getTotalCount());
            } else if (ObjectUtil.isNotEmpty(source) && source instanceof TestCarPrepareBO) {
                List<TestCarPrepareBO> prepareBO = (List<TestCarPrepareBO>) source;
                Map<String, TestCarPrepareBO> testCarPrepareBOMap = prepareBO.stream().collect(Collectors.toMap(
                        TestCarPrepareBO::getCarLicenceNo, // 指定用作key的Function
                        Function.identity() // 保留原始对象作为value
                ));
                result.forEach(item -> {
                    TestCarPrepareBO testCarPrepareBO = testCarPrepareBOMap.get(item.getPlateNumber());
                    if (ObjectUtil.isNotEmpty(testCarPrepareBO)) {
                        item.setTestCarBelongStore(testCarPrepareBO.getDlrShortName());//试驾车所属门店
                    }
                });
            } else if (ObjectUtil.isNotEmpty(source) && source instanceof ClueEventFlowRsp) {
                List<ClueEventFlowRsp> flowRsps = (List<ClueEventFlowRsp>) source;
                Map<String, ClueEventFlowRsp> flowRspMap = flowRsps.stream().collect(Collectors.toMap(
                        ClueEventFlowRsp::getCustId, // 指定用作key的Function
                        Function.identity() // 保留原始对象作为value
                ));
                result.forEach(item -> {
                    ClueEventFlowRsp flowRsp = flowRspMap.get(item.getPlateNumber());
                    if (ObjectUtil.isNotEmpty(flowRsp)) {
                        item.setTestCarBelongStore(flowRsp.getEventName());//事件名称
                    }
                });
            }
        }
        return vo;
    }
}
