package com.smart.adp.application.vo.drive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 生成用户协议文件VO
 * @Author: rik.ren
 * @Date: 2025/6/28 15:01
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GenerateAgreementRspVO {
    /**
     * word文件地址，用于预览
     */
    @Schema(description = "word文件地址，用于预览")
    private String wordFileUrl;
    /**
     * PDF文件地址，用于开始试驾接口入参
     */
    @Schema(description = "PDF文件地址，用于开始试驾接口入参")
    private String pdfFileUrl;
}
