package com.smart.adp.application.dto.drive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 开始试乘试驾
 * @Author: rik.ren
 * @Date: 2025/5/20 11:08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaveBeginTestDriverSheet {

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名", required = true)
    private String customerName;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话", required = true)
    private String customerPhone;

    /**
     * 试驾单ID
     */
    @Schema(description = "试驾单ID", required = true)
    private String testDriveSheetId;

    /**
     * 客户身份证协议
     */
    @Schema(description = "客户身份证协议")
    private String customerIdNumberAgreement;

    /**
     * 驾驶证照片
     */
    @Schema(description = "驾驶证照片", required = true)
    private String drivingLicencePhoto;

    /**
     * 其他协议文件
     */
    @Schema(description = "其他协议文件")
    private String otherAgreement;

    /**
     * 试驾协议
     */
    @Schema(description = "试驾协议", required = true)
    private String testDriveAgreement;

    /**
     * 客户签名协议
     */
    @Schema(description = "客户签名协议", required = true)
    private String customerSignatureAgreement;

    /**
     * 小型车类型编码
     */
    @Schema(description = "小型车类型编码", required = true)
    private String smallCarTypeCode;
}
