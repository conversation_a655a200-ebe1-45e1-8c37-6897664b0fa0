package com.smart.adp.application.dto.drive;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.infrastructure.utils.AESUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 到店签到
 * @Author: rik.ren
 * @Date: 2025/5/30 13:25
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignTestDriveDTO {

    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id")
    @NotBlank(message = "试驾单id不能为空")
    private String testDriveSheetId;

    /**
     * 客户签名
     */
    @Schema(description = "客户签名")
    private String customerSignatureAgreement;

    /**
     * 身份证照片地址
     */
    @Schema(description = "身份证照片地址")
    private String customerIdNumberAgreement;

    /**
     * 驾驶证照片地址
     */
    @Schema(description = "驾驶证照片地址")
    private String drivingLicencePhoto;

    /**
     * 其他协议地址
     */
    @Schema(description = "其他协议地址")
    private String otherAgreement;

    /**
     * 证件号
     */
    @Schema(description = "证件号")
    @NotBlank(message = "证件号不能为空")
    @JsonProperty("IDNumber")
    private String IDNumber;

    /**
     * ocr识别的真实姓名
     */
    @Schema(description = "ocr识别的真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    /**
     * 构建签到参数
     *
     * @return
     */
    @SneakyThrows
    public SacTestDriveSheetBO buildSignBO() {
        SacTestDriveSheetBO sacTestDriveSheetBO = new SacTestDriveSheetBO(testDriveSheetId, Boolean.FALSE);
        sacTestDriveSheetBO.setOtherAgreement(otherAgreement);
        sacTestDriveSheetBO.setDrivingLicencePhoto(drivingLicencePhoto);
        sacTestDriveSheetBO.setCustomerIdNumberAgreement(customerIdNumberAgreement);
        sacTestDriveSheetBO.setCustomerSignatureAgreement(customerSignatureAgreement);
        sacTestDriveSheetBO.setIdNumber(AESUtil.encrypt(IDNumber));
        sacTestDriveSheetBO.setRealName(realName);
        return sacTestDriveSheetBO;
    }
}
