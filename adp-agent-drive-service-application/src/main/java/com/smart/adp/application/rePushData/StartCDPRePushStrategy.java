package com.smart.adp.application.rePushData;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.base.bo.IfsBaseCdpLeadsBO;
import com.smart.adp.domain.model.clue.ClueInfoAggregate;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 开始试驾后重推CDP
 * @Author: rik.ren
 * @Date: 2025/7/9 18:12
 **/
@Slf4j
@Component
public class StartCDPRePushStrategy implements RePushTestDataStrategy {
    @Autowired
    private ClueInfoAggregate clueInfoAggregate;
    @Autowired
    private BaseInfoAggregate baseInfoAggregate;

    /**
     * 重推
     *
     * @param param
     * @return
     */
    @Override
    public Boolean rePush(Object param) {
        log.info("开始重推开始试驾的CDP");
        SacTestDriveSheetBO sheetBO = (SacTestDriveSheetBO) param;
        // 要先查询线索最新的意向车型，再去推送CDP
        SacClueInfoDlrBO queryClueInfoDlrResult = clueInfoAggregate.queryClueInfoDlr(sheetBO.getCustomerPhone());
        if (ObjectUtil.isEmpty(queryClueInfoDlrResult) || StringUtils.isBlank(queryClueInfoDlrResult.getCustId())) {
            log.info("重推CDP没有查询到线索");
            return null;
        }
        // 通知CDP
        IfsBaseCdpLeadsBO cdpLeadsBO = new IfsBaseCdpLeadsBO();
        Boolean cdpResult =
                baseInfoAggregate.saveCdpLeads(cdpLeadsBO.buildStartDrivingCdpLeadBO(sheetBO.getCustomerPhone(),
                        queryClueInfoDlrResult.getIntenCarTypeName()));
        return cdpResult;
    }
}
