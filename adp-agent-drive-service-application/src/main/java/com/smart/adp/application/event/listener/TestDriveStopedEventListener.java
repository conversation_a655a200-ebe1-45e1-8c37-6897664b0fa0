package com.smart.adp.application.event.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.application.event.TestDriveStopedEvent;
import com.smart.adp.domain.adapter.IAdapterTDARequestEntity;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.base.bo.LookUpInfoBO;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.infrastructure.feign.CSCFeign;
import com.smart.adp.infrastructure.feign.TDAFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationListener;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * @Description: 结束试驾后续操作的领域事件监听器
 * @Author: rik.ren
 * @Date: 2025/6/19 13:50
 **/
@Component
@Slf4j
public class TestDriveStopedEventListener implements ApplicationListener<TestDriveStopedEvent> {
    @Autowired
    private TestDriveAggregate testDriveAggregate;
    @Autowired
    private BaseInfoAggregate baseInfoAggregate;
    @Autowired
    private IAdapterTDARequestEntity adapterTDARequest;
    @Autowired
    private TDAFeign tdaFeign;
    @Autowired
    private CSCFeign cscFeign;
    @Autowired
    @Qualifier("smartExecutor")
    private Executor asyncTaskExecutor;

    @Override
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void onApplicationEvent(TestDriveStopedEvent event) {

        // 在主线程中捕获用户上下文传递给异步线程
        UserBusiEntity userBusiEntity = UserInfoContext.get();
        log.info("结束试驾后续操作的领域事件监听器");

        CompletableFuture.runAsync(() -> {
            try {
                SacTestDriveSheetBO getTestDriveSheetResult =
                        testDriveAggregate.getTestDriveSheet(new SacTestDriveSheetBO(event.getStopTestDriveObj().getTestDriveSheetId(),
                                Boolean.TRUE));
                List<UscMdmOrgEmployeeBO> queryUscMdmEmpResult =
                        baseInfoAggregate.queryUscMdmOrgEmp(Arrays.asList(getTestDriveSheetResult.getSalesConsultantId()));

                // 1. 试驾结束时记录接口表，获取BI车机数据
                Boolean vehicleDataResult = testDriveAggregate.insertIfVehicleData(getTestDriveSheetResult);

                // 2. 发送ZTMQ
                Boolean ztmqResult = testDriveAggregate.sendZTMQ(getTestDriveSheetResult);

                // 3. 发送TDA
                List<LookUpInfoBO> lookUpResult = baseInfoAggregate.lookUpInfo("VE1040", "1");
                String tdaResult = null;
                if (CollectionUtil.isNotEmpty(lookUpResult)) {
                    HttpEntity<Object> adaptTDA = adapterTDARequest.stopTestDriveSendTda(getTestDriveSheetResult,
                            queryUscMdmEmpResult.get(0));
                    log.info("结束试驾后发送TDA入参 {}", JSONObject.toJSONString(adaptTDA));
                    tdaResult = postData(lookUpResult.get(0).getLookUpValueName(), adaptTDA);
                }
//                String tdaResult = tdaFeign.stopTestDriveSendTda(adaptTDA);
                log.info("结束试驾后发送TDA结果 {}", tdaResult);
            } catch (Exception e) {
                log.error("结束试驾事件处理失败 {}", JSONObject.toJSONString(event.getStopTestDriveObj()), e);
                throw new BusinessException(RespCode.FAIL.getCode(), "结束试驾事件处理失败");
            }
        }, asyncTaskExecutor);
    }

    private String postData(String url, HttpEntity<Object> param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);
            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, param, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("结束试驾后发送TDA异常 {}", JSONObject.toJSONString(param), e);
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    private RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }
}
