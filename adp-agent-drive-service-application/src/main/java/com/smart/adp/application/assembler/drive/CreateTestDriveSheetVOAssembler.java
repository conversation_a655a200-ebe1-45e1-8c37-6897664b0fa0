package com.smart.adp.application.assembler.drive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.assembler.Assembler;
import com.smart.adp.application.vo.drive.CreateTestDriveRspVO;
import com.smart.adp.domain.model.drive.TestDriveAggregate;

/**
 * @Description: 查询试乘试驾单对象转换
 * @Author: rik.ren
 * @Date: 2025/5/18 14:30
 **/
public class CreateTestDriveSheetVOAssembler implements Assembler<TestDriveAggregate.CreateTestDriveSheetResult, CreateTestDriveRspVO> {

    /**
     * 装配 请实现装配过程
     *
     * @param source 源
     * @param target 目标
     */
    @Override
    public CreateTestDriveRspVO assemble(TestDriveAggregate.CreateTestDriveSheetResult source, Class<CreateTestDriveRspVO> target) {
        if (ObjectUtil.isEmpty(source)) {
            return null;
        }
        return convent(source);
    }

    private CreateTestDriveRspVO convent(TestDriveAggregate.CreateTestDriveSheetResult source) {
        CreateTestDriveRspVO rspVO = new CreateTestDriveRspVO();
        CreateTestDriveRspVO.TestDriveSheetResult sacTestDriveSheetResult = rspVO.new TestDriveSheetResult();
        sacTestDriveSheetResult.setTestDriveSheetId(source.getTestDriveSheetBO().getTestDriveSheetId());
        sacTestDriveSheetResult.setAppointmentId(source.getTestDriveSheetBO().getAppointmentId());
        sacTestDriveSheetResult.setTestStatus(source.getTestDriveSheetBO().getTestStatus());
        sacTestDriveSheetResult.setRecordId(source.getTestDriveSheetBO().getRecordId());
        sacTestDriveSheetResult.setDlrCode(source.getTestDriveSheetBO().getDlrCode());
        sacTestDriveSheetResult.setDlrName(source.getTestDriveSheetBO().getDlrName());
        sacTestDriveSheetResult.setSalesConsultantName(source.getTestDriveSheetBO().getSalesConsultantName());
        sacTestDriveSheetResult.setSalesConsultantId(source.getTestDriveSheetBO().getSalesConsultantId());
        sacTestDriveSheetResult.setIntenLevelCode(source.getTestDriveSheetBO().getIntenLevelCode());
        sacTestDriveSheetResult.setIntenLevelName(source.getTestDriveSheetBO().getIntenLevelName());
        sacTestDriveSheetResult.setDriverCustomerRelation(source.getTestDriveSheetBO().getDriverCustomerRelation());
        sacTestDriveSheetResult.setDriverName(source.getTestDriveSheetBO().getDriverName());
        sacTestDriveSheetResult.setDriverPhone(source.getTestDriveSheetBO().getDriverPhone());
        sacTestDriveSheetResult.setDrivingLicenceType(source.getTestDriveSheetBO().getDrivingLicenceType());
        sacTestDriveSheetResult.setDrivingLicenceNumber(source.getTestDriveSheetBO().getDrivingLicenceNumber());
        sacTestDriveSheetResult.setAddress(source.getTestDriveSheetBO().getAddress());
        sacTestDriveSheetResult.setDlrClueOrderNo(source.getTestDriveSheetBO().getDlrClueOrderNo());
        sacTestDriveSheetResult.setCustomerName(source.getTestDriveSheetBO().getCustomerName());
        sacTestDriveSheetResult.setCustomerId(source.getTestDriveSheetBO().getCustomerId());
        sacTestDriveSheetResult.setCustomerPhone(source.getTestDriveSheetBO().getCustomerPhone());
        sacTestDriveSheetResult.setCustomerSex(source.getTestDriveSheetBO().getCustomerSex());
        sacTestDriveSheetResult.setSmallCarTypeCode(source.getTestDriveSheetBO().getSmallCarTypeCode());
        sacTestDriveSheetResult.setSmallCarTypeName(source.getTestDriveSheetBO().getSmallCarTypeName());
        sacTestDriveSheetResult.setPlateNumber(source.getTestDriveSheetBO().getPlateNumber());
        sacTestDriveSheetResult.setCarVin(source.getTestDriveSheetBO().getCarVin());
        sacTestDriveSheetResult.setTestType(source.getTestDriveSheetBO().getTestType());
        sacTestDriveSheetResult.setAppointmentChannel(source.getTestDriveSheetBO().getAppointmentChannel());
        sacTestDriveSheetResult.setDeposit(source.getTestDriveSheetBO().getDeposit());
        sacTestDriveSheetResult.setOldTestDriveSheetId(source.getTestDriveSheetBO().getOldTestDriveSheetId());
        sacTestDriveSheetResult.setReceiver(source.getTestDriveSheetBO().getReceiver());
        sacTestDriveSheetResult.setReceiverCode(source.getTestDriveSheetBO().getReceiverCode());
        sacTestDriveSheetResult.setReceiverTime(source.getTestDriveSheetBO().getReceiverTime());
        sacTestDriveSheetResult.setCreator(source.getTestDriveSheetBO().getCreator());
        sacTestDriveSheetResult.setCreatedName(source.getTestDriveSheetBO().getCreatedName());
        sacTestDriveSheetResult.setCreatedDate(source.getTestDriveSheetBO().getCreatedDate());
        sacTestDriveSheetResult.setLastUpdatedDate(source.getTestDriveSheetBO().getLastUpdatedDate());
        sacTestDriveSheetResult.setUpdateControlId(source.getTestDriveSheetBO().getUpdateControlId());
        sacTestDriveSheetResult.setIsEnable(source.getTestDriveSheetBO().getIsEnable());
        sacTestDriveSheetResult.setColumn1(source.getTestDriveSheetBO().getColumn1());
        sacTestDriveSheetResult.setColumn2(source.getTestDriveSheetBO().getColumn2());
        sacTestDriveSheetResult.setTestcarRouteId(source.getTestDriveSheetBO().getColumn3());
        sacTestDriveSheetResult.setRouteTypeName(source.getTestDriveSheetBO().getColumn4());
        sacTestDriveSheetResult.setInfoChanMCode(source.getTestDriveSheetBO().getColumn8());
        sacTestDriveSheetResult.setInfoChanDCode(source.getTestDriveSheetBO().getColumn9());
        sacTestDriveSheetResult.setIsCanChange(source.getTestDriveSheetBO().getIsCanChange());
        sacTestDriveSheetResult.setImportFlag(source.getTestDriveSheetBO().getImportFlag());
        sacTestDriveSheetResult.setRepeatFlag(source.getTestDriveSheetBO().getRepeatFlag());
        sacTestDriveSheetResult.setEvaluateFlag(source.getTestDriveSheetBO().getEvaluateFlag());
        sacTestDriveSheetResult.setTestDriveMethod(source.getTestDriveSheetBO().getTestDriveMethod());
        sacTestDriveSheetResult.setAppointmentTestDate(source.getAppointmentSheetBO().getAppointmentTestDate());
        sacTestDriveSheetResult.setAppointmentTestTime(source.getAppointmentSheetBO().getAppointmentTestTime());
        sacTestDriveSheetResult.setAppointmentStartTime(source.getAppointmentSheetBO().getAppointmentStartTime());
        sacTestDriveSheetResult.setAppointmentEndTime(source.getAppointmentSheetBO().getAppointmentEndTime());
        rspVO.setTestDriveSheetObject(sacTestDriveSheetResult);
        rspVO.setResult(source.getResult());
        return rspVO;
    }
}
