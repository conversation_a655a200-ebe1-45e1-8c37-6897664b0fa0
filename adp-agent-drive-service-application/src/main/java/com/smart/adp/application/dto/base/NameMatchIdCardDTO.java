package com.smart.adp.application.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 姓名和身份证号匹配关系校验
 * @Author: rik.ren
 * @Date: 2025/6/24 16:12
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NameMatchIdCardDTO implements Serializable {
    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;
    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String idCardNo;
}
