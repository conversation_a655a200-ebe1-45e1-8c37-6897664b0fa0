package com.smart.adp.application.dto.drive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description: ocr接口入参，formdata方式
 * @Author: rik.ren
 * @Date: 2025/6/19 17:48
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OCRInfoDTO {
    /**
     * 图片地址
     */
    @Schema(description = "图片地址")
    private String imageUrl;
    /**
     * 图片文件流
     */
    @Schema(description = "图片文件流（与图片地址二选一）", format = "binary")
    private MultipartFile imageFile;
}
