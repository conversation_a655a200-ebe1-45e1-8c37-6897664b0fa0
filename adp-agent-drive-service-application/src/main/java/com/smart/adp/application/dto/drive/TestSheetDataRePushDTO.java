package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.enums.RePushTestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 数据重推DTO
 * @Author: rik.ren
 * @Date: 2025/7/09 17:10
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestSheetDataRePushDTO {
    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id", required = true)
    @NotBlank(message = "试驾单id不能为空")
    private String testDriveSheetId;

    /**
     * 试驾预约单id
     */
    @Schema(description = "试驾预约单id", required = true)
    @NotBlank(message = "试驾预约单id不能为空")
    private String appointmentId;

    /**
     * 重推的业务类型
     *
     * @see RePushTestDriveTypeEnum
     */
    @Schema(description = "重推的业务类型，0创建试驾单发送试驾短信，1开始试驾通知CDP，2开始试驾修改线索意向车型，3获取BI车机数据，4结束试驾发送ZTMQ，5结束试驾发送TDA", required = true)
    @NotNull(message = "重推的业务类型不能为空")
    private Integer rePushType;

    /**
     * 构建试驾重推的BO
     *
     * @return
     */
    public SacTestDriveSheetBO buildRePushTestDriveBO() {
        SacTestDriveSheetBO sheetBO = new SacTestDriveSheetBO();
        sheetBO.setTestDriveSheetId(this.testDriveSheetId);
        sheetBO.setAppointmentId(this.appointmentId);
        return sheetBO;
    }
}
