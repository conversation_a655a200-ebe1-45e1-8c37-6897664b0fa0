package com.smart.adp.application.event;

import com.smart.adp.application.dto.drive.StartTestDriveDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * @Description: 开始试驾后续操作的领域事件对象
 * @Author: rik.ren
 * @Date: 2025/6/19 13:48
 **/
@Getter
@Setter
@ToString
public class TestDriveStartedEvent extends ApplicationEvent {
    private StartTestDriveDTO startTestDriveObj;

    public TestDriveStartedEvent(Object source, StartTestDriveDTO startTestDriveObj) {
        super(source);
        this.startTestDriveObj = startTestDriveObj;
    }
}
