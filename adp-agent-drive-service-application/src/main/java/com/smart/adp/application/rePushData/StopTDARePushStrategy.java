package com.smart.adp.application.rePushData;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.smart.adp.domain.adapter.IAdapterTDARequestEntity;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.base.bo.LookUpInfoBO;
import com.smart.adp.domain.model.base.bo.UscMdmOrgEmployeeBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import com.smart.adp.infrastructure.feign.TDAFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 结束试驾后重推TDA
 * @Author: rik.ren
 * @Date: 2025/7/9 18:13
 **/
@Slf4j
@Component
public class StopTDARePushStrategy implements RePushTestDataStrategy {

    @Autowired
    private BaseInfoAggregate baseInfoAggregate;
    @Autowired
    private IAdapterTDARequestEntity adapterTDARequest;
    @Autowired
    private TDAFeign tdaFeign;

    /**
     * 重推
     *
     * @param param
     * @return
     */
    @Override
    public String rePush(Object param) {
        log.info("开始重推试驾结束的TDA");
        SacTestDriveSheetBO sheetBO = (SacTestDriveSheetBO) param;
        // 1. 查询TDA接口地址
        List<LookUpInfoBO> lookUpResult = baseInfoAggregate.lookUpInfo("VE1040", "1");

        // 2. 查询销售顾问信息
        List<UscMdmOrgEmployeeBO> queryUscMdmEmpResult = baseInfoAggregate.queryUscMdmOrgEmp(Arrays.asList(sheetBO.getSalesConsultantId()));

        // 3. 调用TDA接口
        String tdaResult = null;
        if (CollectionUtil.isNotEmpty(lookUpResult)) {
            HttpEntity<Object> adaptTDA = adapterTDARequest.stopTestDriveSendTda(sheetBO,
                    queryUscMdmEmpResult.get(0));
            log.info("重推结束试驾后发送TDA入参 {}", JSONObject.toJSONString(adaptTDA));
//            tdaResult = postData(lookUpResult.get(0).getLookUpValueName(), adaptTDA);
            tdaResult = tdaFeign.stopTestDriveSendTda(adaptTDA);
            log.info("重推结束试驾后发送TDA结果 {}", JSONObject.toJSONString(adaptTDA));
        }
        return tdaResult;
    }
}
