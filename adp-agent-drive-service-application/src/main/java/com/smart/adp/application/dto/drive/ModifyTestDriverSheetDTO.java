package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestDriveMethodEnum;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 更新试乘试驾单
 * @Author: rik.ren
 * @Date: 2025/5/20 11:08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModifyTestDriverSheetDTO {

    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id", required = true)
    @NotBlank(message = "试驾单id不能为空")
    private String testDriveSheetId;

    /**
     * 试驾预约单id
     */
    @Schema(description = "试驾预约单id", required = true)
    @NotBlank(message = "试驾预约单id不能为空")
    private String appointmentId;

    /**
     * 预约类型
     *
     * @see TestDriveTypeEnum
     */
    @Schema(description = "预约类型", required = true)
    @NotBlank(message = "预约类型不能为空")
    private String testType;

    /**
     * 试驾方式
     *
     * @see TestDriveMethodEnum
     */
    @Schema(description = "试驾方式code", required = true)
    @NotBlank(message = "试驾方式不能为空")
    private String testDriveMethod;

    /**
     * 车架号
     */
    @Schema(description = "车架号", required = true)
    @NotBlank(message = "车架号不能为空")
    private String carVin;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号", required = true)
    @NotBlank(message = "车牌号不能为空")
    private String plateNumber;

    /**
     * 小型车类型编码
     */
    @Schema(description = "小型车类型编码", required = true)
    @NotBlank(message = "小型车类型编码不能为空")
    private String smallCarTypeCode;

    /**
     * 小型车类型名称
     */
    @Schema(description = "小型车类型名称", required = true)
    @NotBlank(message = "小型车类型名称不能为空")
    private String smallCarTypeName;

    /**
     * 普通试乘试驾预约日期
     */
    @Schema(description = "普通试乘试驾预约日期")
    private String appointmentTestDate;

    /**
     * 普通试乘试驾预约时间
     */
    @Schema(description = "普通试乘试驾预约时间")
    private String appointmentTestTime;

    /**
     * 深度试驾开始时间
     */
    @Schema(description = "深度试驾开始时间")
    private String appointmentStartTime;

    /**
     * 深度试驾结束时间
     */
    @Schema(description = "深度试驾结束时间")
    private String appointmentEndTime;

    /**
     * 定金
     */
    @Schema(description = "定金")
    private BigDecimal deposit;

    /**
     * 销售顾问ID
     */
    @Schema(description = "销售顾问ID", hidden = true)
    private String salesConsultantId;

    /**
     * 销售顾问姓名
     */
    @Schema(description = "销售顾问姓名", hidden = true)
    private String salesConsultantName;

    /**
     * 路线名称
     */
    @Schema(description = "路线名称", required = true)
    @NotBlank(message = "路线名称不能为空")
    private String routeTypeName;

    /**
     * 试驾路线ID
     */
    @Schema(description = "试驾路线ID", required = true)
    @NotBlank(message = "试驾路线ID不能为空")
    private String testcarRouteId;

    /**
     * 跟进人id，从token中解析获取
     */
    @Schema(description = "跟进人id", hidden = true)
    private String reviewPersonId;

    /**
     * 跟进人name，从token中解析获取
     */
    @Schema(description = "跟进人name", hidden = true)
    private String reviewPersonName;

    /**
     * 门店code，从token中解析获取
     */
    @Schema(description = "门店code", hidden = true)
    private String dlrCode;

    /**
     * 控制id
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "控制id")
    private String updateControlId;

    public void setSalesConsultantName(String salesConsultantName) {
        if (StringUtils.isEmpty(this.salesConsultantName)) {
            this.salesConsultantName = UserInfoContext.get().getEmpName();
        }
    }

    public String getSalesConsultantName() {
        if (StringUtils.isEmpty(this.salesConsultantName)) {
            this.salesConsultantName = UserInfoContext.get().getEmpName();
        }
        return salesConsultantName;
    }

    public void setSalesConsultantId(String salesConsultantId) {
        if (StringUtils.isEmpty(this.salesConsultantId)) {
            this.salesConsultantId = UserInfoContext.get().getUserID();
        }
    }

    public String getSalesConsultantId() {
        if (StringUtils.isEmpty(this.salesConsultantId)) {
            this.salesConsultantId = UserInfoContext.get().getUserID();
        }
        return salesConsultantId;
    }

    public String getReviewPersonId() {
        if (StringUtils.isEmpty(this.reviewPersonId)) {
            this.reviewPersonId = UserInfoContext.get().getUserID();
        }
        return reviewPersonId;
    }

    public String getReviewPersonName() {
        if (StringUtils.isEmpty(this.reviewPersonName)) {
            this.reviewPersonName = UserInfoContext.get().getUserName();
        }
        return reviewPersonName;
    }

    public String getDlrCode() {
        if (StringUtils.isEmpty(dlrCode)) {
            this.dlrCode = UserInfoContext.get().getDlrCode();
        }
        return dlrCode;
    }

    /**
     * 构建更新试驾单时，需要的试驾单BO
     *
     * @param configTestDriveSwitchVal
     * @param configDriveNumLimitVal
     * @return
     */
    public SacTestDriveSheetBO buildModifyBO(String configTestDriveSwitchVal, String configDriveNumLimitVal) {
        SacTestDriveSheetBO bo = new SacTestDriveSheetBO();
        bo.setTestDriveSheetId(testDriveSheetId);
        bo.setAppointmentId(appointmentId);
        bo.setTestType(testType);
        bo.setCarVin(carVin);
        bo.setDlrCode(getDlrCode());
        bo.setPlateNumber(plateNumber);
        bo.setSmallCarTypeCode(smallCarTypeCode);
        bo.setSmallCarTypeName(smallCarTypeName);
        bo.setAppointmentTestDate(appointmentTestDate);
        bo.setAppointmentTestTime(appointmentTestTime);
        bo.setAppointmentStartTime(appointmentStartTime);
        bo.setAppointmentEndTime(appointmentEndTime);
        bo.setTestDriveMethod(testDriveMethod);
        bo.setLastUpdatedDate(LocalDateTime.now());
        bo.setModifier(UserInfoContext.get().getUserID());
        bo.setModifyName(UserInfoContext.get().getEmpName());
        bo.setColumn3(testcarRouteId);
        bo.setColumn4(routeTypeName);
        bo.setConfigValueSwitch(configTestDriveSwitchVal);
        bo.setConfigValueDriveNum(configDriveNumLimitVal);
        return bo;
    }
}
