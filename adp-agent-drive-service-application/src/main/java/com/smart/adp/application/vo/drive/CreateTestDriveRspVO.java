package com.smart.adp.application.vo.drive;

import com.smart.adp.application.assembler.AssemblerFactory;
import com.smart.adp.application.assembler.drive.CreateTestDriveSheetVOAssembler;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 创建试乘试驾单返回结果
 * @Author: rik.ren
 * @Date: 2025/6/18 18:26
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建试乘试驾单返回结果")
public class CreateTestDriveRspVO {
    /**
     * 创建试乘试驾单结果
     */
    @Schema(description = "试乘试驾单创建结果")
    Boolean result;
    /**
     * 创建试乘试驾单信息
     */
    @Schema(description = "试乘试驾单信息")
    TestDriveSheetResult testDriveSheetObject;

    public static CreateTestDriveRspVO convent(TestDriveAggregate.CreateTestDriveSheetResult source) {
        return AssemblerFactory.getInstance().convert
                (CreateTestDriveSheetVOAssembler.class, source, CreateTestDriveRspVO.class);
    }

    //region 【试乘试驾返回结果内部类】
    @Data
    public class TestDriveSheetResult {

        /**
         * 试乘试驾单ID
         */
        @Schema(description = "试乘试驾单ID")
        private String testDriveSheetId;

        /**
         * 试乘试驾预约单ID
         */
        @Schema(description = "试乘试驾预约单ID")
        private String appointmentId;

        /**
         * 试乘试驾状态
         */
        @Schema(description = "试乘试驾状态")
        private String testStatus;

        /**
         * 录音ID
         */
        @Schema(description = "录音ID")
        private String recordId;

        /**
         * 所属专营店编码
         */
        @Schema(description = "所属专营店编码")
        private String dlrCode;

        /**
         * 所属专营店名称
         */
        @Schema(description = "所属专营店名称")
        private String dlrName;

        /**
         * 销售顾问姓名
         */
        @Schema(description = "销售顾问姓名")
        private String salesConsultantName;

        /**
         * 销售顾问ID
         */
        @Schema(description = "销售顾问ID")
        private String salesConsultantId;

        /**
         * 意向级别编码
         */
        @Schema(description = "意向级别编码")
        private String intenLevelCode;

        /**
         * 意向级别名称
         */
        @Schema(description = "意向级别名称")
        private String intenLevelName;

        /**
         * 驾驶人与客户关系
         */
        @Schema(description = "驾驶人与客户关系")
        private String driverCustomerRelation;

        /**
         * 驾驶人姓名
         */
        @Schema(description = "驾驶人姓名")
        private String driverName;

        /**
         * 驾驶人联系电话
         */
        @Schema(description = "驾驶人联系电话")
        private String driverPhone;

        /**
         * 驾驶证类型
         */
        @Schema(description = "驾驶证类型")
        private String drivingLicenceType;

        /**
         * 驾驶证号码
         */
        @Schema(description = "驾驶证号码")
        private String drivingLicenceNumber;

        /**
         * 驾驶人地址
         */
        @Schema(description = "驾驶人地址")
        private String address;

        /**
         * 线索单号
         */
        @Schema(description = "线索单号")
        private String dlrClueOrderNo;

        /**
         * 客户姓名
         */
        @Schema(description = "客户姓名")
        private String customerName;

        /**
         * 客户ID
         */
        @Schema(description = "客户ID")
        private String customerId;

        /**
         * 客户电话
         */
        @Schema(description = "客户电话")
        private String customerPhone;

        /**
         * 性别
         */
        @Schema(description = "性别")
        private String customerSex;

        /**
         * 试乘试驾车型编码
         */
        @Schema(description = "试乘试驾车型编码")
        private String smallCarTypeCode;

        /**
         * 试乘试驾车型名称
         */
        @Schema(description = "试乘试驾车型名称")
        private String smallCarTypeName;

        /**
         * 试驾车牌
         */
        @Schema(description = "试驾车牌")
        private String plateNumber;

        /**
         * VIN(车架号)
         */
        @Schema(description = "VIN(车架号)")
        private String carVin;

        /**
         * 试乘试驾类型(0：试乘，1：试驾，2：深度试驾)
         */
        @Schema(description = "试乘试驾类型(0：试乘，1：试驾，2：深度试驾)")
        private String testType;

        /**
         * 预约渠道(0：门店自建，1：线上预约)
         */
        @Schema(description = "预约渠道(0：门店自建，1：线上预约)")
        private String appointmentChannel;

        /**
         * 试驾押金
         */
        @Schema(description = "试驾押金")
        private java.math.BigDecimal deposit;

        /**
         * 原试驾单id，多个逗号分割
         */
        @Schema(description = "原试驾单id，多个逗号分割")
        private String oldTestDriveSheetId;

        /**
         * 接待人
         */
        @Schema(description = "接待人")
        private String receiver;

        /**
         * 接待人编码
         */
        @Schema(description = "接待人编码")
        private String receiverCode;

        /**
         * 接待时间
         */
        @Schema(description = "接待时间")
        private LocalDateTime receiverTime;

        /**
         * 创建人ID
         */
        @Schema(description = "创建人ID")
        private String creator;

        /**
         * 创建人
         */
        @Schema(description = "创建人")
        private String createdName;

        /**
         * 创建日期
         */
        @Schema(description = "创建日期")
        private LocalDateTime createdDate;

        /**
         * 最后更新日期
         */
        @Schema(description = "最后更新日期")
        private LocalDateTime lastUpdatedDate;

        /**
         * 并发控制ID
         */
        @Schema(description = "并发控制ID")
        private String updateControlId;

        /**
         * 是否可用
         */
        @Schema(description = "是否可用")
        private String isEnable;

        /**
         * 扩展字段1(试乘试驾结束备注)
         */
        @Schema(description = "扩展字段1(试乘试驾结束备注)")
        private String column1;

        /**
         * 扩展字段2 邀请码
         */
        @Schema(description = "扩展字段2 邀请码")
        private String column2;

        /**
         * 试驾路线ID
         */
        @Schema(description = "试驾路线ID")
        private String testcarRouteId;

        /**
         * 路线分类名称
         */
        @Schema(description = "路线分类名称")
        private String routeTypeName;

        /**
         * 一级信息来源编码INFO_CHAN_M_CODE
         */
        @Schema(description = "一级信息来源编码INFO_CHAN_M_CODE")
        private String infoChanMCode;

        /**
         * 二级信息来源编码INFO_CHAN_D_CODE
         */
        @Schema(description = "二级信息来源编码INFO_CHAN_D_CODE")
        private String infoChanDCode;

        /**
         * 是否可以更换门店
         */
        @Schema(description = "是否可以更换门店")
        private String isCanChange;

        /**
         * 试驾单补录标识（1 :补录）
         */
        @Schema(description = "试驾单补录标识（1 :补录）")
        private String importFlag;

        /**
         * 补录数据是否重复（1:重复）
         */
        @Schema(description = "补录数据是否重复（1:重复）")
        private String repeatFlag;

        /**
         * 是否已发送评价短信给客户（1 :已发送）
         */
        @Schema(description = "是否已发送评价短信给客户（1 :已发送）")
        private String evaluateFlag;

        /**
         * 试驾方式
         */
        @Schema(description = "试驾方式")
        private String testDriveMethod;

        /**
         * 预约试乘试驾日期(普通试乘试驾)
         */
        @Schema(description = "预约试乘试驾日期(普通试乘试驾)")
        private String appointmentTestDate;

        /**
         * 预约试乘试驾时间段(普通试乘试驾)
         */
        @Schema(description = "预约试乘试驾时间段(普通试乘试驾)")
        private String appointmentTestTime;

        /**
         * 预约超长试驾开始时间
         */
        @Schema(description = "预约超长试驾开始时间")
        private String appointmentStartTime;

        /**
         * 预约超长试驾结束时间
         */
        @Schema(description = "预约超长试驾结束时间")
        private String appointmentEndTime;
    }
    //endregion
}
