package com.smart.adp.application.dto.drive;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.application.common.PageDTO;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.bo.TestCarPrepareBO;
import com.smart.adp.domain.enums.TestDriveTypeEnum;
import com.smart.adp.domain.utils.UserUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 查询试乘试驾单入参
 * @Author: rik.ren
 * @Date: 2025/5/15 16:03
 **/
@Data
public class QueryTestDriverSheetDTO extends PageDTO {

    /**
     * 门店code，从token中解析获取
     */
    @Schema(description = "门店code", hidden = true)
    private String dlrCode;

    /**
     * 产品专家id
     */
    @Schema(description = "产品专家id")
    private String salesConsultantId;

    /**
     * 试驾类型 0试乘,1试驾,2深度试驾
     *
     * @see TestDriveTypeEnum
     */
    @Schema(description = "试驾类型 0试乘,1试驾,2深度试驾")
    private String testType;

    /**
     * 渠道来源
     */
    @Schema(description = "渠道来源")
    private String infoChanMCode;

    /**
     * 三级线索来源
     */
    @Schema(description = "三级线索来源")
    private String infoChanDCode;

    /**
     * 预约开始时间
     */
    @Schema(description = "预约开始时间")
    private String appointmentStartTimeMin;

    /**
     * 预约截止时间
     */
    @Schema(description = "预约截止时间")
    private String appointmentStartTimeMax;

    /**
     * 查询的内容
     */
    @Schema(description = "查询的内容")
    private String searchCondition;

    /**
     * 0未完成，1已完成
     */
    @Schema(description = "0未完成，1已完成")
    private String isEnd = "0";

    /**
     * 试驾单id
     */
    @Schema(description = "试驾单id")
    private String testDriveSheetId;

    public void setDlrCode(String dlrCode) {
        if (StringUtils.isEmpty(this.dlrCode)) {
            this.dlrCode = UserInfoContext.get().getDlrCode();
        }
    }

    public String getDlrCode() {
        if (StringUtils.isEmpty(this.dlrCode)) {
            this.dlrCode = UserInfoContext.get().getDlrCode();
        }
        return dlrCode;
    }

    public SacTestDriveSheetBO buildSacTestDriveSheetBO() {
        SacTestDriveSheetBO bo = new SacTestDriveSheetBO();
        bo.setDlrCode(this.getDlrCode());
        // 判断是否产品专家
        if (UserUtil.isProductExpert(UserInfoContext.get().getStationId())) {
            bo.setSalesConsultantId(UserInfoContext.get().getUserID());
        } else {
            bo.setSalesConsultantId(this.getSalesConsultantId());
        }
        bo.setInfoChanMCode(this.getInfoChanMCode());
        bo.setInfoChanDCode(this.getInfoChanDCode());
        bo.setSearchCondition(this.getSearchCondition());
        bo.setAppointmentStartTime(ObjectUtil.isEmpty(this.getAppointmentStartTimeMin()) ? null :
                this.getAppointmentStartTimeMin().toString());
        bo.setAppointmentEndTime(ObjectUtil.isEmpty(this.getAppointmentStartTimeMax()) ? null :
                this.getAppointmentStartTimeMax().toString());
        bo.setTestType(this.getTestType());
        bo.setPageNumber(this.getPageIndex());
        bo.setPageSize(this.getPageSize());
        bo.setListTestStatus("0".equals(isEnd) ? Arrays.asList(TestDriveStatusEnum.NOT_STARTED.getCode(),
                TestDriveStatusEnum.IN_PROGRESS.getCode()) : Arrays.asList(TestDriveStatusEnum.COMPLETED.getCode()));
        bo.setTestDriveSheetId(this.getTestDriveSheetId());
        return bo;
    }

    public TestCarPrepareBO buildTestCarPrepareBO(List<String> listCarLicenceNo, List<String> listResponseOrderStatus) {
        TestCarPrepareBO bo = new TestCarPrepareBO();
        bo.setListCarLicenceNo(listCarLicenceNo);
        bo.setListResponseOrderStatus(listResponseOrderStatus);
        return bo;
    }
}
