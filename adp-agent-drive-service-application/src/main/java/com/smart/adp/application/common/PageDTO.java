package com.smart.adp.application.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 分页DTO
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023-03-02 10:45
 */
@Data
@Accessors(chain = true)
@Schema(description = "分页DTO")
public class PageDTO implements Serializable {

    private static final long serialVersionUID = -1910672801183495134L;

    /**
     * 页码
     */
    @Schema(description = "页码")
    private Integer pageIndex = 1;

    /**
     * 页码
     */
    @Schema(description = "页数")
    private Integer pageSize = 10;
}
