package com.smart.adp.application.event.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.smart.adp.application.dto.drive.CreateTestDriverSheetDTO;
import com.smart.adp.application.event.TestDriveCreatedEvent;
import com.smart.adp.application.service.drive.TestDriveTaskService;
import com.smart.adp.domain.adapter.IAdapterClueRequestEntity;
import com.smart.adp.domain.common.error.BusinessException;
import com.smart.adp.domain.common.resp.EntityResult;
import com.smart.adp.domain.common.resp.RespCode;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.BaseInfoAggregate;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.ClueInfoAggregate;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.TestDriveAggregate;
import com.smart.adp.domain.model.drive.bo.SacAppointmentSheetBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.infrastructure.feign.CSCFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * @Description: 创建试乘试驾单完成后领域事件监听器
 * @Author: rik.ren
 * @Date: 2025/6/19 13:50
 **/
@Component
@Slf4j
public class TestDriveCreatedEventListener implements ApplicationListener<TestDriveCreatedEvent> {
    @Autowired
    private TestDriveAggregate testDriveAggregate;
    @Autowired
    private ClueInfoAggregate clueInfoAggregate;
    @Autowired
    private BaseInfoAggregate baseInfoAggregate;
    @Autowired
    private TestDriveTaskService testDriveTaskService;
    @Resource
    private IAdapterClueRequestEntity adapterClueRequest;
    @Autowired
    private CSCFeign cscFeign;
    @Autowired
    @Qualifier("smartExecutor")
    private Executor asyncTaskExecutor;

    @Override
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void onApplicationEvent(TestDriveCreatedEvent event) {

        // 在主线程中捕获用户上下文传递给异步线程
        UserBusiEntity userBusiEntity = UserInfoContext.get();

        log.info("创建试乘试驾单完成后领域事件监听器");

        CompletableFuture.runAsync(() -> {
            try {
                // 1. 查询线索
                SacClueInfoDlrBO sacClueInfoDlrResult =
                        clueInfoAggregate.queryClueInfoDlr(event.getSacTestDriveSheetObj().getCustomerPhone());
                if (ObjectUtil.isEmpty(sacClueInfoDlrResult) || StringUtils.isBlank(sacClueInfoDlrResult.getCustId())) {
                    // 如果线索不存在，调用线索领域的创建线索能力
                    sacClueInfoDlrResult = clueInfoAggregate.saveAgentDlrClue(
                            adapterClueRequest.CreateAgentClue(event.getSacTestDriveSheetObj(), userBusiEntity),
                            userBusiEntity);
                    if (ObjectUtil.isEmpty(sacClueInfoDlrResult) || StringUtils.isBlank(sacClueInfoDlrResult.getCustId())) {
                        log.error("创建试乘试驾单新建线索失败 {}", JSON.toJSONString(sacClueInfoDlrResult));
                        throw new BusinessException(RespCode.FAIL.getCode(), "创建试乘试驾单新建线索失败");
                    }
                }
                event.setSacClueInfoDlrObj(sacClueInfoDlrResult);

                // 2. 创建单号，修改试乘试驾单，预约单，对应预约单表的appointmentOrderNo字段
                log.info("成功创建试驾后修改试驾单信息 {}", event.getSacTestDriveSheetObj().getTestDriveSheetId());
                modifySheet(event.getSacTestDriveSheetObj(), event.getSacClueInfoDlrObj(), userBusiEntity);

                // 3. 试驾任务完成
                if (StringUtils.isNotEmpty(event.getTestDriverSheetDTO().getTestDriveTaskId())
                        && StringUtils.isNotEmpty(event.getTestDriverSheetDTO().getTaskUpdateControlId())) {
                    log.info("成功创建试驾后修改任务状态 {}", event.getTestDriverSheetDTO().getTestDriveTaskId());
                    driveTaskComplete(event.getTestDriverSheetDTO(), event.getSacTestDriveSheetObj());
                }

                // 4. 调用csc服务客户履历保存
                EntityResult<Object> resumeSaveResult = cscFeign.resumeSave(userBusiEntity.getToken(), event.buildResumeSave());
                log.info("成功创建试驾后调用客户履历结果 {}", JSON.toJSONString(resumeSaveResult));

                // 5. 发消息
                testDriveAggregate.sendMessage(event.getSacTestDriveSheetObj());
            } catch (Exception e) {
                testDriveAggregate.deleteTestDriveSheet(event.getSacTestDriveSheetObj());
                log.error("创建试驾单事件处理失败 {}", event.getTestDriverSheetDTO(), e);
            }
        }, asyncTaskExecutor);
    }

    /**
     * 创建线索后，反向更新预约单试驾单
     *
     * @param sacTestDriveSheetBO
     * @param sacClueInfoDlrBO
     */
    private void modifySheet(SacTestDriveSheetBO sacTestDriveSheetBO, SacClueInfoDlrBO sacClueInfoDlrBO, UserBusiEntity userBusiEntity) {
        String appointmentOrderNo = baseInfoAggregate.generateOrderCode("bucn_yy_no", userBusiEntity);
        SacAppointmentSheetBO appointmentSheetBO = new SacAppointmentSheetBO();
        appointmentSheetBO.setAppointmentId(sacTestDriveSheetBO.getAppointmentId());
        appointmentSheetBO.setDlrClueOrderNo(sacClueInfoDlrBO.getServerOrder());
        appointmentSheetBO.setCustomerId(sacClueInfoDlrBO.getCustId());
        appointmentSheetBO.setAppointmentOrderNo(appointmentOrderNo);
        testDriveAggregate.modifyAppointmentSheetByAppOrderOn(appointmentSheetBO);

        //对应试驾单表的testDriveOrderNo字段
        String testDriveOrderNo = baseInfoAggregate.generateOrderCode("bucn_sjsc_no", userBusiEntity);
        SacTestDriveSheetBO sheetBO = new SacTestDriveSheetBO();
        sheetBO.setTestDriveSheetId(sacTestDriveSheetBO.getTestDriveSheetId());
        sheetBO.setDlrClueOrderNo(sacClueInfoDlrBO.getServerOrder());
        sheetBO.setCustomerId(sacClueInfoDlrBO.getCustId());
        sheetBO.setTestDriveOrderNo(testDriveOrderNo);
        sheetBO.setDlrClueOrderNo(sacClueInfoDlrBO.getServerOrder());
        sheetBO.setIntenLevelCode(sacClueInfoDlrBO.getIntenLevelCode());
        sheetBO.setIntenLevelName(sacClueInfoDlrBO.getIntenLevelCode());
        sheetBO.setColumn8(sacClueInfoDlrBO.getInfoChanMCode());
        sheetBO.setColumn9(sacClueInfoDlrBO.getInfoChanDCode());
        testDriveAggregate.modifyTestDriveSheet(sheetBO);
    }

    /**
     * 试驾任务完成
     *
     * @param param
     * @param sheetBO
     */
    private void driveTaskComplete(CreateTestDriverSheetDTO param, SacTestDriveSheetBO sheetBO) {
        testDriveTaskService.modifyTestDriveTask(param.getTestDriveTaskId(), param.getTaskUpdateControlId(), sheetBO);
    }
}
