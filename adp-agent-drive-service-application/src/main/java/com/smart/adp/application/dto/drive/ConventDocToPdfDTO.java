package com.smart.adp.application.dto.drive;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: word转pdf
 * @Author: rik.ren
 * @Date: 2025/6/24 19:14
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConventDocToPdfDTO {
    /**
     * 序号
     */
    @Schema(description = "序号", hidden = true)
    private Integer serialNumber = 1;
    /**
     * word在obs中的地址
     */
    @Schema(description = "需要的是以https://obs-adp-uat.obs.cn-east-3.myhuaweicloud.com/xxx开头的地址", required = true)
    private String wordFileUrl;
    /**
     * 上传到的obs地址
     */
    @Schema(description = "不是桶的地址，是目录，例如testDriveAgreementFile-contracts", required = true)
    private String outputPath;
    /**
     * 文件名 如果是试驾协议，请拼接如下："testDriveAgreement_" + param.getName()+"_"+ param.getTestDriveSheetId() + ".pdf"
     */
    @Schema(description = "文件名", required = true)
    private String outputFileName;

}
