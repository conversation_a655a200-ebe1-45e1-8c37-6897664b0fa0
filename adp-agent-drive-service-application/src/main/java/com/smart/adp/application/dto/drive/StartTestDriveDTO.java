package com.smart.adp.application.dto.drive;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.enums.TestDriveStatusEnum;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.infrastructure.utils.AESUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @Description: 开始试驾入参DTO
 * @Author: rik.ren
 * @Date: 2025/5/28 20:10
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StartTestDriveDTO {

    /**
     * 线索ID
     */
    @Schema(description = "线索ID")
    private String custId;

    /**
     * 押金
     */
    @Schema(description = "押金")
    private Integer deposit;

    /**
     * 驾驶证
     */
    @Schema(description = "驾驶证")
    private String drivingLicencePhoto;

    /**
     * 客户手机号
     */
    @Schema(description = "客户手机号")
    @NotBlank(message = "客户手机号不能为空")
    private String customerPhone;

    /**
     * 身份证照片
     */
    @Schema(description = "身份证照片")
    private String customerIdNumberAgreement;

    /**
     * 试驾单ID
     */
    @Schema(description = "试驾单ID")
    @NotBlank(message = "试驾单id不能为空")
    private String testDriveSheetId;

    /**
     * 车型名称
     */
    @Schema(description = "车型")
    @NotBlank(message = "车型不能为空")
    private String smallCarTypeCode;

    /**
     * 开始里程
     */
    @Schema(description = "开始里程")
    private java.math.BigDecimal testStartRoadHaul;

    /**
     * 客户签名
     */
    @Schema(description = "客户签名")
    @NotBlank(message = "客户签名不能为空")
    private String customerSignatureAgreement;

    /**
     * 其他协议
     */
    @Schema(description = "其他协议")
    private String otherAgreement;

    /**
     * 试驾协议
     */
    @Schema(description = "试驾协议")
//    @NotBlank(message = "试驾协议不能为空")
    private String testDriveAgreement;

    /**
     * 试驾协议PDF文件地址
     */
    @Schema(description = "试驾协议PDF文件地址")
    @NotBlank(message = "试驾协议PDF不能为空")
    private String testDriveAgreementPDF;

    /**
     * 试驾单状态
     *
     * @see TestDriveStatusEnum
     */
    @Schema(description = "试驾单状态", hidden = true)
    private String testStatus = TestDriveStatusEnum.IN_PROGRESS.getCode();

    /**
     * 证件号
     */
    @Schema(description = "证件号")
    @NotBlank(message = "证件号不能为空")
    @JsonProperty("IDNumber")
    private String IDNumber;

    /**
     * ocr识别的真实姓名
     */
    @Schema(description = "ocr识别的真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    /**
     * 构建开始试驾的试驾单BO
     *
     * @return
     */
    @SneakyThrows
    public SacTestDriveSheetBO buildStartTestDriveBO() {
        SacTestDriveSheetBO sheetBO = new SacTestDriveSheetBO();
        sheetBO.setTestDriveSheetId(this.testDriveSheetId);
        sheetBO.setTestDriveAgreement(this.testDriveAgreement);
        sheetBO.setTestDriveAgreementPDF(testDriveAgreementPDF);
        sheetBO.setOtherAgreement(this.otherAgreement);
        sheetBO.setCustomerSignatureAgreement(this.customerSignatureAgreement);
        sheetBO.setCustomerIdNumberAgreement(this.customerIdNumberAgreement);
        sheetBO.setDrivingLicencePhoto(drivingLicencePhoto);
        sheetBO.setModifyName(UserInfoContext.get().getEmpName());
        sheetBO.setModifier(UserInfoContext.get().getUserID());
        sheetBO.setLastUpdatedDate(LocalDateTime.now());
        sheetBO.setTestStatus(TestDriveStatusEnum.IN_PROGRESS.getCode());
        sheetBO.setRealName(this.realName);
        sheetBO.setIdNumber(AESUtil.encrypt(this.IDNumber));
        sheetBO.setTestStartRoadHaul(this.testStartRoadHaul);
        return sheetBO;
    }

}
