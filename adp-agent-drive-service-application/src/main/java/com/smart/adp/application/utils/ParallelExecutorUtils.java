package com.smart.adp.application.utils;

import com.smart.adp.infrastructure.advice.MyBusinessLogicFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * @Description: 多线程任务并行
 * @Author: rik.ren
 * @Date: 2025/3/18 15:11
 **/
@Slf4j
@Component
public class ParallelExecutorUtils {
    // 默认线程池配置
    private static Executor staticExecutor; // 静态引用

//    @Autowired
//    private ThreadPoolExecutor smartExecutor;
    @Autowired
    private Executor driveExecutor;

    /**
     * 并行执行多个任务（使用默认线程池）
     * 强约定任务不能返回null
     *
     * @param tasks 并行任务
     * @param <T>   返回的类型
     * @return
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> execute(Callable<T>... tasks) throws Exception {
        return execute(staticExecutor, tasks);
    }

    /**
     * 并行执行多个任务（自定义线程池）
     * 强约定任务不能返回null
     */
    public static <T> List<T> execute(Executor executor, Callable<T>... tasks) throws Exception {
        List<CompletableFuture<T>> futures = new ArrayList<>();

        // 提交所有任务
        for (Callable<T> task : tasks) {
            CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return task.call();
                } catch (Exception e) {
                    throw new CompletionException(e);
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        try {
            allFutures.get(); // 检查异常
        } catch (ExecutionException e) {
            throw (Exception) e.getCause(); // 抛出原始异常
        }

        // 收集结果
        List<T> results = new ArrayList<>();
        for (CompletableFuture<T> future : futures) {
            results.add(future.get());
        }
        return results;
    }

    /**
     * 带函数的并行处理，当并行处理完成后，指定调用传入的方法，需要的类型结
     * 强约定任务不能返回null
     *
     * @param function 函数式方法名
     * @param param    函数式方法需要的参数
     * @param tasks    并行任务
     * @param <T>      并行任务返回的结果类型
     * @param <M>      函数式编程返回的结果类型
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public static <T, M> M executeFunction(MyBusinessLogicFunction<T, M> function, Object[] param, Callable<T>... tasks) throws Exception {
        return executeFunction(staticExecutor, function, param, tasks);
    }

    @SuppressWarnings("unchecked")
    private static <T, M> M executeFunction(Executor executor, MyBusinessLogicFunction<T, M> function, Object[] param,
                                            Callable<T>... tasks) throws Exception {
        List<T> executeResult = execute(executor, tasks);
        // 执行后置函数
        Object result = function.myselfBusinessLogic(executeResult, param);
        return (M) result;
    }

    /**
     * 关闭默认线程池（应用结束时调用）
     */
//    public static void shutdown() {
//        staticExecutor.shutdown();
//    }

    /**
     * 初始化静态引用
     */
    @PostConstruct
    public void init() {
//        staticExecutor = this.smartExecutor;
        staticExecutor = this.driveExecutor;
    }

}