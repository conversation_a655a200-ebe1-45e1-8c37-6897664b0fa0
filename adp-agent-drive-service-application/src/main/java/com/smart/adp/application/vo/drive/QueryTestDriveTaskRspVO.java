package com.smart.adp.application.vo.drive;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.common.DomainPage;
import com.smart.adp.domain.common.PageVO;
import com.smart.adp.domain.common.constants.TimeConstant;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 分页查询试驾任务VO
 * @Author: rik.ren
 * @Date: 2025/5/14 17:06
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class QueryTestDriveTaskRspVO {

    /**
     * 任务Id
     */
    @Schema(description = "任务Id")
    private String testDriveTaskId;

    /**
     * 任务标题
     */
    @Schema(description = "任务标题")
    private String taskTitle;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private String createdDate;

    /**
     * 所属专营店名称
     */
    @Schema(description = "所属专营店名称")
    private String dlrName;

    /**
     * 客户姓名
     */
    @Schema(description = "客户姓名")
    private String customerName;

    /**
     * 客户电话
     */
    @Schema(description = "客户电话")
    private String customerPhone;

    /**
     * 是否是本门店线索 -1线索不存在，0不是本门店线索，1是本门店线索
     */
    @Schema(description = "是否是本门店线索，-1线索不存在，0不是本门店线索，1是本门店线索")
    private Integer isCurrentStore;

    /**
     * 线索归属产品专家姓名
     */
    @Schema(description = "线索归属产品专家姓名")
    private String cluesReviewPersonName;

    /**
     * 试驾单销售顾问姓名
     */
    @Schema(description = "试驾单销售顾问姓名")
    private String salesConsultantName;

    /**
     * 试乘试驾车型名称
     */
    @Schema(description = "试乘试驾车型名称")
    private String smallCarTypeName;

    /**
     * 预约试乘试驾时间
     */
    @Schema(description = "预约试乘试驾时间")
    private String appointmentTestDateTime;

    /**
     * 并发控制锁
     */
    @Schema(description = "并发控制锁")
    private String updateControlId;


    public static PageVO<QueryTestDriveTaskRspVO> convent(DomainPage<SacTestDriveTaskBO> testDriveTaskDomainPage,
                                                          List<SacClueInfoDlrBO> listClueInfoDlr) {
        if (ObjectUtil.isEmpty(testDriveTaskDomainPage) || CollectionUtil.isEmpty(testDriveTaskDomainPage.getRecords())) {
            return PageVO.<QueryTestDriveTaskRspVO>builder().build();
        }
        String currentStore = UserInfoContext.get().getDlrCode();
        // 使用条件表达式初始化Map，确保变量成为实际上的最终变量
        Map<String, SacClueInfoDlrBO> testCarPrepareBOMap = CollectionUtil.isNotEmpty(listClueInfoDlr)
                ? listClueInfoDlr.stream()
                .collect(Collectors.toMap(
                        SacClueInfoDlrBO::getPhone,
                        Function.identity(),
                        (existing, replacement) -> existing)) // 保留第一个出现的值
                : Collections.emptyMap();

        // 使用Stream处理转换逻辑
        List<QueryTestDriveTaskRspVO> result = testDriveTaskDomainPage.getRecords().stream()
                .map(taskBO -> {
                    QueryTestDriveTaskRspVO taskRspVO = new QueryTestDriveTaskRspVO();
                    taskRspVO.setTestDriveTaskId(taskBO.getId());
                    taskRspVO.setTaskTitle(taskBO.getTaskTitle());
                    taskRspVO.setCreatedDate(TimeConstant.DEFAULT_FORMATTER.format(taskBO.getCreatedDate()));
                    taskRspVO.setCustomerName(taskBO.getCustName());
                    taskRspVO.setCustomerPhone(taskBO.getPhone());
                    SacClueInfoDlrBO sacClueInfoDlrBO = testCarPrepareBOMap.get(taskBO.getPhone());
                    taskRspVO.setIsCurrentStore(ObjectUtil.isEmpty(sacClueInfoDlrBO) ? -1 :
                            currentStore.equals(taskBO.getTaskPersonDlrCode()) ? 1 : 0);
                    taskRspVO.setCluesReviewPersonName(ObjectUtil.isEmpty(sacClueInfoDlrBO) ? null : sacClueInfoDlrBO.getReviewPersonName());
                    taskRspVO.setDlrName(taskBO.getTaskPersonDlrName());
                    taskRspVO.setSalesConsultantName(taskBO.getSalesConsultantName());
                    taskRspVO.setAppointmentTestDateTime(ObjectUtil.isEmpty(taskBO.getAppointmentTestDate())
                            ? null
                            : taskBO.getAppointmentTestDate().concat(" ").concat(taskBO.getAppointmentTestTime()));
                    taskRspVO.setSmallCarTypeName(taskBO.getSmallCarTypeName());
                    taskRspVO.setUpdateControlId(taskBO.getUpdateControlId());
                    return taskRspVO;
                })
                .collect(Collectors.toList());

        // 构建分页结果
        PageVO<QueryTestDriveTaskRspVO> vo = new PageVO<>();
        vo.setRecords(result);
        vo.setTotalRow(testDriveTaskDomainPage.getTotalCount());
        return vo;
    }

}
