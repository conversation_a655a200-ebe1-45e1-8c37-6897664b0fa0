package com.smart.adp.application.dto.cdp;

import lombok.Builder;
import lombok.Data;

/**
 * @Description: 开始试驾通知CDP的DTO
 * @Author: rik.ren
 * @Date: 2025/5/29 17:31
 **/
@Data
@Builder
public class StartDrivingSendCDPDTO {

    /**
     * 线索手机号
     */
    private String bk;

    /**
     * 线索手机号
     */
    private String mobile;

    /**
     * 最后更新系统来源
     */
    private String cLastupdateSystem;

    /**
     * 备注
     */
    private String remark;

    /**
     * 意向车型
     */
    private String cInterestedCarModel;
}
