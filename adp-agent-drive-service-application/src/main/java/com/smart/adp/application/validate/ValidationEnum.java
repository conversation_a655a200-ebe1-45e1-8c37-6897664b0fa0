package com.smart.adp.application.validate;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 类描述：入参校验枚举
 *
 * <AUTHOR>
 * @Description: ValidationEnum
 * @date 2025/3/5
 */
@Getter
@NoArgsConstructor
public enum ValidationEnum {

    /**
     * 校验微信支付时，参数是否完全填写
     */
    modifyDlrInfoCheck("modifyDlrInfoCheck", "这个校验用在编辑线索详情的") {
        @Override
        public Boolean validationFunc(Object value) {
            if (ObjectUtil.isEmpty(value)) {
                return Boolean.FALSE;
            }
//            ClueDlrModifyDTO clueDlrModifyParam = value instanceof ClueDlrModifyDTO ?
//                    ((ClueDlrModifyDTO) value) : null;
//            // 1. 校验传入要修改的类型是否合法
//            ClueModifyTypeEnum modifyType = ClueModifyTypeEnum.getByCode(clueDlrModifyParam.getClueModifyType());
//            if (ObjectUtil.isEmpty(modifyType)) {
//                return Boolean.FALSE;
//            }
            return Boolean.TRUE;
        }
    };

    private static final String regex = "^[a-zA-Z0-9-_]+$";

    /**
     * 校验字段名
     */
    private String field;

    /**
     * 校验的描述
     */
    private String desc;

    ValidationEnum(String field, String note) {
        this.field = field;
        this.desc = note;
    }

    public static ValidationEnum getByField(String field) {

        ValidationEnum[] values = ValidationEnum.values();
        for (ValidationEnum enums : values) {
            if (Objects.equals(enums.getField(), field)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 策略在枚举中对应的抽象方法
     *
     * @param value
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public abstract Boolean validationFunc(Object value) throws InstantiationException, IllegalAccessException;

    /**
     * 内部类用来在枚举中使用注入对象
     */
    @Component
    private static class ServiceBeanInjector {
        // 在这个枚举中，使用内部注入的依赖来调用service对应的方法
//        private static IClueDlrService innerclueDlrService;
//        @Resource
//        private IClueDlrService clueDlrService;

//        @PostConstruct
//        private void postConstruct() {
//            ServiceBeanInjector.innerclueDlrService = clueDlrService;
//        }
    }
}