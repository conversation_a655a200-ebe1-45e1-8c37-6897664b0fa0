package com.smart.adp.application.rePushData;

import cn.hutool.core.util.ObjectUtil;
import com.smart.adp.domain.context.UserInfoContext;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.clue.ClueInfoAggregate;
import com.smart.adp.domain.model.clue.bo.SacClueInfoDlrBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.strategy.rePushData.RePushTestDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 开始试驾后重推线索agent-clue修改意向车型
 * @Author: rik.ren
 * @Date: 2025/7/9 18:12
 **/
@Slf4j
@Component
public class StartIntentionCarRePushStrategy implements RePushTestDataStrategy {
    @Autowired
    private ClueInfoAggregate clueInfoAggregate;

    /**
     * 重推
     *
     * @param param
     * @return
     */
    @Override
    public String rePush(Object param) {
        log.info("开始重推开始试驾修改意向车型");
        SacTestDriveSheetBO sheetBO = (SacTestDriveSheetBO) param;
        UserBusiEntity userBusiEntity = UserInfoContext.get();
        // 1. 根据手机号获取线索信息
        SacClueInfoDlrBO queryClueInfoDlrResult = clueInfoAggregate.queryClueInfoDlr(sheetBO.getCustomerPhone());
        if (ObjectUtil.isEmpty(queryClueInfoDlrResult) || StringUtils.isBlank(queryClueInfoDlrResult.getCustId())) {
            log.info("重推没有查询到线索");
            return null;
        }
        log.info("重推查询到的线索 {}", queryClueInfoDlrResult.getCustId());
        // 更新线索的意向车型
        String modifyIntentionCarResult = clueInfoAggregate.modifyIntentionCar(
                queryClueInfoDlrResult.getCustId(),
                sheetBO.getSmallCarTypeCode(),
                userBusiEntity);
        log.info("重推意向车型结果 {}", modifyIntentionCarResult);
        return modifyIntentionCarResult;
    }
}
