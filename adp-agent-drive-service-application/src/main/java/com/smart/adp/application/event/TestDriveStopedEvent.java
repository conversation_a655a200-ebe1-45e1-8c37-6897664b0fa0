package com.smart.adp.application.event;

import com.smart.adp.application.dto.drive.StopTestDriveDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * @Description: 结束试驾后续操作的领域事件对象
 * @Author: rik.ren
 * @Date: 2025/6/19 13:48
 **/
@Getter
@Setter
@ToString
public class TestDriveStopedEvent extends ApplicationEvent {
    private StopTestDriveDTO stopTestDriveObj;

    public TestDriveStopedEvent(Object source, StopTestDriveDTO stopTestDriveObj) {
        super(source);
        this.stopTestDriveObj = stopTestDriveObj;
    }
}
