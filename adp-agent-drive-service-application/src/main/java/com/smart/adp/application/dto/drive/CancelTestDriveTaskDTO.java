package com.smart.adp.application.dto.drive;

import com.smart.adp.domain.enums.TestTaskStatusCodeEnum;
import com.smart.adp.domain.helper.StringHelper;
import com.smart.adp.domain.model.base.entity.UserBusiEntity;
import com.smart.adp.domain.model.drive.bo.SacTestDriveSheetBO;
import com.smart.adp.domain.model.drive.bo.SacTestDriveTaskBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @Description: 试驾任务取消DTO
 * @Author: rik.ren
 * @Date: 2025/5/27 19:40
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CancelTestDriveTaskDTO {

    /**
     * 任务id
     */
    @Schema(description = "任务id")
    @NotBlank(message = "任务id不能为空")
    private String testDriveTaskId;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Schema(description = "试驾任务乐观锁ID")
    private String taskUpdateControlId;


    public SacTestDriveTaskBO buildCancelTaskBO(String taskId, String oldUpdateControlId, UserBusiEntity userBusiEntity) {
        SacTestDriveTaskBO sacTestDriveTaskBO = new SacTestDriveTaskBO();
        sacTestDriveTaskBO.setId(taskId);
        sacTestDriveTaskBO.setTaskStateCode(TestTaskStatusCodeEnum.CANCELLED.getCode());
        sacTestDriveTaskBO.setTaskStateName(TestTaskStatusCodeEnum.CANCELLED.getDesc());
        sacTestDriveTaskBO.setUpdateControlId(StringHelper.GetGUID());
        sacTestDriveTaskBO.setOldUpdateControlId(oldUpdateControlId);
        sacTestDriveTaskBO.setLastUpdatedDate(LocalDateTime.now());
        sacTestDriveTaskBO.setModifier(userBusiEntity.getUserID());
        sacTestDriveTaskBO.setModifyName(userBusiEntity.getEmpName());
        return sacTestDriveTaskBO;
    }

}
